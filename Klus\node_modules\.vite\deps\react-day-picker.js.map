{"version": 3, "sources": ["../../react-day-picker/node_modules/.pnpm/@rollup+plugin-typescript@11.1.5_rollup@4.9.1_tslib@2.6.2_typescript@5.3.3/node_modules/tslib/tslib.es6.js", "../../react-day-picker/src/types/DayPickerMultiple.ts", "../../react-day-picker/src/types/DayPickerRange.ts", "../../react-day-picker/src/types/DayPickerSingle.ts", "../../react-day-picker/src/contexts/DayPicker/defaultClassNames.ts", "../../react-day-picker/src/contexts/DayPicker/formatters/formatCaption.ts", "../../react-day-picker/src/contexts/DayPicker/formatters/formatDay.ts", "../../react-day-picker/src/contexts/DayPicker/formatters/formatMonthCaption.ts", "../../react-day-picker/src/contexts/DayPicker/formatters/formatWeekNumber.ts", "../../react-day-picker/src/contexts/DayPicker/formatters/formatWeekdayName.ts", "../../react-day-picker/src/contexts/DayPicker/formatters/formatYearCaption.ts", "../../react-day-picker/src/contexts/DayPicker/labels/labelDay.ts", "../../react-day-picker/src/contexts/DayPicker/labels/labelMonthDropdown.ts", "../../react-day-picker/src/contexts/DayPicker/labels/labelNext.ts", "../../react-day-picker/src/contexts/DayPicker/labels/labelPrevious.ts", "../../react-day-picker/src/contexts/DayPicker/labels/labelWeekday.ts", "../../react-day-picker/src/contexts/DayPicker/labels/labelWeekNumber.ts", "../../react-day-picker/src/contexts/DayPicker/labels/labelYearDropdown.ts", "../../react-day-picker/src/contexts/DayPicker/defaultContextValues.ts", "../../react-day-picker/src/contexts/DayPicker/utils/parseFromToProps.ts", "../../react-day-picker/src/contexts/DayPicker/DayPickerContext.tsx", "../../react-day-picker/src/components/CaptionLabel/CaptionLabel.tsx", "../../react-day-picker/src/components/IconDropdown/IconDropdown.tsx", "../../react-day-picker/src/components/Dropdown/Dropdown.tsx", "../../react-day-picker/src/components/MonthsDropdown/MonthsDropdown.tsx", "../../react-day-picker/src/components/YearsDropdown/YearsDropdown.tsx", "../../react-day-picker/src/hooks/useControlledValue/useControlledValue.ts", "../../react-day-picker/src/contexts/Navigation/utils/getInitialMonth.ts", "../../react-day-picker/src/contexts/Navigation/useNavigationState.ts", "../../react-day-picker/src/contexts/Navigation/utils/getDisplayMonths.ts", "../../react-day-picker/src/contexts/Navigation/utils/getNextMonth.ts", "../../react-day-picker/src/contexts/Navigation/utils/getPreviousMonth.ts", "../../react-day-picker/src/contexts/Navigation/NavigationContext.tsx", "../../react-day-picker/src/components/CaptionDropdowns/CaptionDropdowns.tsx", "../../react-day-picker/src/components/IconLeft/IconLeft.tsx", "../../react-day-picker/src/components/IconRight/IconRight.tsx", "../../react-day-picker/src/components/Button/Button.tsx", "../../react-day-picker/src/components/Navigation/Navigation.tsx", "../../react-day-picker/src/components/CaptionNavigation/CaptionNavigation.tsx", "../../react-day-picker/src/components/Caption/Caption.tsx", "../../react-day-picker/src/components/Footer/Footer.tsx", "../../react-day-picker/src/components/HeadRow/utils/getWeekdays.ts", "../../react-day-picker/src/components/HeadRow/HeadRow.tsx", "../../react-day-picker/src/components/Head/Head.tsx", "../../react-day-picker/src/components/DayContent/DayContent.tsx", "../../react-day-picker/src/contexts/SelectMultiple/SelectMultipleContext.tsx", "../../react-day-picker/src/contexts/SelectRange/utils/addToRange.ts", "../../react-day-picker/src/contexts/SelectRange/SelectRangeContext.tsx", "../../react-day-picker/src/contexts/Modifiers/utils/matcherToArray.ts", "../../react-day-picker/src/contexts/Modifiers/utils/getCustomModifiers.ts", "../../react-day-picker/src/types/Modifiers.ts", "../../react-day-picker/src/contexts/Modifiers/utils/getInternalModifiers.ts", "../../react-day-picker/src/contexts/Modifiers/ModifiersContext.tsx", "../../react-day-picker/src/types/Matchers.ts", "../../react-day-picker/src/contexts/Modifiers/utils/isDateInRange.ts", "../../react-day-picker/src/contexts/Modifiers/utils/isMatch.ts", "../../react-day-picker/src/contexts/Modifiers/utils/getActiveModifiers.ts", "../../react-day-picker/src/contexts/Focus/utils/getInitialFocusTarget.ts", "../../react-day-picker/src/contexts/Focus/utils/getNextFocus.ts", "../../react-day-picker/src/contexts/Focus/FocusContext.tsx", "../../react-day-picker/src/hooks/useActiveModifiers/useActiveModifiers.tsx", "../../react-day-picker/src/contexts/SelectSingle/SelectSingleContext.tsx", "../../react-day-picker/src/hooks/useDayEventHandlers/useDayEventHandlers.tsx", "../../react-day-picker/src/hooks/useSelectedDays/useSelectedDays.ts", "../../react-day-picker/src/hooks/useDayRender/utils/getDayClassNames.ts", "../../react-day-picker/src/hooks/useDayRender/utils/getDayStyle.ts", "../../react-day-picker/src/hooks/useDayRender/useDayRender.tsx", "../../react-day-picker/src/components/Day/Day.tsx", "../../react-day-picker/src/components/WeekNumber/WeekNumber.tsx", "../../react-day-picker/src/components/Row/Row.tsx", "../../react-day-picker/src/components/Table/utils/daysToMonthWeeks.ts", "../../react-day-picker/src/components/Table/utils/getMonthWeeks.ts", "../../react-day-picker/src/components/Table/Table.tsx", "../../react-day-picker/src/hooks/useId/useId.ts", "../../react-day-picker/src/components/Month/Month.tsx", "../../react-day-picker/src/components/Months/Months.tsx", "../../react-day-picker/src/components/Root/Root.tsx", "../../react-day-picker/src/contexts/RootProvider.tsx", "../../react-day-picker/src/DayPicker.tsx", "../../react-day-picker/src/hooks/useInput/utils/isValidDate.tsx", "../../react-day-picker/src/hooks/useInput/useInput.ts", "../../react-day-picker/src/types/DayPickerDefault.ts"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    function next() {\r\n        while (env.stack.length) {\r\n            var rec = env.stack.pop();\r\n            try {\r\n                var result = rec.dispose && rec.dispose.call(rec.value);\r\n                if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n};\r\n", "import { DayPickerProps } from 'DayPicker';\n\nimport { DayPickerContextValue } from 'contexts/DayPicker';\n\nimport { DayPickerBase } from './DayPickerBase';\nimport { SelectMultipleEventHandler } from './EventHandlers';\n\n/** The props for the {@link DayPicker} component when using `mode=\"multiple\"`. */\nexport interface DayPickerMultipleProps extends DayPickerBase {\n  mode: 'multiple';\n  /** The selected days. */\n  selected?: Date[] | undefined;\n  /** Event fired when a days added or removed to the selection. */\n  onSelect?: SelectMultipleEventHandler;\n  /** The minimum amount of days that can be selected. */\n  min?: number;\n  /** The maximum amount of days that can be selected. */\n  max?: number;\n}\n\n/** Returns true when the props are of type {@link DayPickerMultipleProps}. */\nexport function isDayPickerMultiple(\n  props: DayPickerProps | DayPickerContextValue\n): props is DayPickerMultipleProps {\n  return props.mode === 'multiple';\n}\n", "import { DayPickerProps } from 'DayPicker';\n\nimport { DayPickerContextValue } from 'contexts/DayPicker';\n\nimport { DayPickerBase } from './DayPickerBase';\nimport { SelectRangeEventHandler } from './EventHandlers';\nimport { DateRange } from './Matchers';\n\n/** The props for the {@link DayPicker} component when using `mode=\"range\"`. */\nexport interface DayPickerRangeProps extends DayPickerBase {\n  mode: 'range';\n  /** The selected range of days. */\n  selected?: DateRange | undefined;\n  /** Event fired when a range (or a part of the range) is selected. */\n  onSelect?: SelectRangeEventHandler;\n  /** The minimum amount of days that can be selected. */\n  min?: number;\n  /** The maximum amount of days that can be selected. */\n  max?: number;\n}\n\n/** Returns true when the props are of type {@link DayPickerRangeProps}. */\nexport function isDayPickerRange(\n  props: DayPickerProps | DayPickerContextValue\n): props is DayPickerRangeProps {\n  return props.mode === 'range';\n}\n", "import { DayPickerProps } from 'DayPicker';\n\nimport { DayPickerContextValue } from 'contexts/DayPicker';\n\nimport { DayPickerBase } from './DayPickerBase';\nimport { SelectSingleEventHandler } from './EventHandlers';\n\n/** The props for the {@link DayPicker} component when using `mode=\"single\"`. */\nexport interface DayPickerSingleProps extends DayPickerBase {\n  mode: 'single';\n  /** The selected day. */\n  selected?: Date | undefined;\n  /** Event fired when a day is selected. */\n  onSelect?: SelectSingleEventHandler;\n  /** Make the selection required. */\n  required?: boolean;\n}\n\n/** Returns true when the props are of type {@link DayPickerSingleProps}. */\nexport function isDayPickerSingle(\n  props: DayPickerProps | DayPickerContextValue\n): props is DayPickerSingleProps {\n  return props.mode === 'single';\n}\n", "import { ClassNames } from 'types/Styles';\n\n/**\n * The name of the default CSS classes.\n */\nexport const defaultClassNames: Required<ClassNames> = {\n  root: 'rdp',\n  multiple_months: 'rdp-multiple_months',\n  with_weeknumber: 'rdp-with_weeknumber',\n  vhidden: 'rdp-vhidden',\n  button_reset: 'rdp-button_reset',\n  button: 'rdp-button',\n\n  caption: 'rdp-caption',\n\n  caption_start: 'rdp-caption_start',\n  caption_end: 'rdp-caption_end',\n  caption_between: 'rdp-caption_between',\n  caption_label: 'rdp-caption_label',\n\n  caption_dropdowns: 'rdp-caption_dropdowns',\n\n  dropdown: 'rdp-dropdown',\n  dropdown_month: 'rdp-dropdown_month',\n  dropdown_year: 'rdp-dropdown_year',\n  dropdown_icon: 'rdp-dropdown_icon',\n\n  months: 'rdp-months',\n  month: 'rdp-month',\n  table: 'rdp-table',\n  tbody: 'rdp-tbody',\n  tfoot: 'rdp-tfoot',\n\n  head: 'rdp-head',\n  head_row: 'rdp-head_row',\n  head_cell: 'rdp-head_cell',\n\n  nav: 'rdp-nav',\n  nav_button: 'rdp-nav_button',\n  nav_button_previous: 'rdp-nav_button_previous',\n  nav_button_next: 'rdp-nav_button_next',\n\n  nav_icon: 'rdp-nav_icon',\n\n  row: 'rdp-row',\n  weeknumber: 'rdp-weeknumber',\n  cell: 'rdp-cell',\n\n  day: 'rdp-day',\n  day_today: 'rdp-day_today',\n  day_outside: 'rdp-day_outside',\n  day_selected: 'rdp-day_selected',\n  day_disabled: 'rdp-day_disabled',\n  day_hidden: 'rdp-day_hidden',\n  day_range_start: 'rdp-day_range_start',\n  day_range_end: 'rdp-day_range_end',\n  day_range_middle: 'rdp-day_range_middle'\n};\n", "import { format, Locale } from 'date-fns';\n\n/**\n * The default formatter for the caption.\n */\nexport function formatCaption(\n  month: Date,\n  options?: { locale?: Locale }\n): string {\n  return format(month, 'LLLL y', options);\n}\n", "import { format, Locale } from 'date-fns';\n\n/**\n * The default formatter for the Day button.\n */\nexport function formatDay(day: Date, options?: { locale?: Locale }): string {\n  return format(day, 'd', options);\n}\n", "import { format, Locale } from 'date-fns';\n\n/**\n * The default formatter for the Month caption.\n */\nexport function formatMonthCaption(\n  month: Date,\n  options?: { locale?: Locale }\n): string {\n  return format(month, 'LLLL', options);\n}\n", "/**\n * The default formatter for the week number.\n */\nexport function formatWeekNumber(weekNumber: number): string {\n  return `${weekNumber}`;\n}\n", "import { format, Locale } from 'date-fns';\n\n/**\n * The default formatter for the name of the weekday.\n */\nexport function formatWeekdayName(\n  weekday: Date,\n  options?: { locale?: Locale }\n): string {\n  return format(weekday, 'cccccc', options);\n}\n", "import { format, Locale } from 'date-fns';\n\n/**\n * The default formatter for the Year caption.\n */\nexport function formatYearCaption(\n  year: Date,\n  options?: {\n    locale?: Locale;\n  }\n): string {\n  return format(year, 'yyyy', options);\n}\n", "import { format } from 'date-fns';\n\nimport { DayLabel } from 'types/Labels';\n\n/**\n * The default ARIA label for the day button.\n */\nexport const labelDay: DayLabel = (day, activeModifiers, options): string => {\n  return format(day, 'do MMMM (EEEE)', options);\n};\n", "/**\n * The default ARIA label for the WeekNumber element.\n */\nexport const labelMonthDropdown = (): string => {\n  return 'Month: ';\n};\n", "import { NavButtonLabel } from 'types/Labels';\n\n/**\n * The default ARIA label for next month button in navigation\n */\nexport const labelNext: NavButtonLabel = (): string => {\n  return 'Go to next month';\n};\n", "import { NavButtonLabel } from 'types/Labels';\n\n/**\n * The default ARIA label for previous month button in navigation\n */\nexport const labelPrevious: NavButtonLabel = (): string => {\n  return 'Go to previous month';\n};\n", "import { format } from 'date-fns';\n\nimport { WeekdayLabel } from 'types/Labels';\n\n/**\n * The default ARIA label for the Weekday element.\n */\nexport const labelWeekday: WeekdayLabel = (day, options): string => {\n  return format(day, 'cccc', options);\n};\n", "import { WeekNumberLabel } from 'types/Labels';\n\n/**\n * The default ARIA label for the WeekNumber element.\n */\nexport const labelWeekNumber: WeekNumberLabel = (n): string => {\n  return `Week n. ${n}`;\n};\n", "/**\n * The default ARIA label for the WeekNumber element.\n */\nexport const labelYearDropdown = (): string => {\n  return 'Year: ';\n};\n", "import { enUS } from 'date-fns/locale';\n\nimport { CaptionLayout } from 'components/Caption';\nimport { DayPickerContextValue } from 'contexts/DayPicker';\n\nimport { defaultClassNames } from './defaultClassNames';\nimport * as formatters from './formatters';\nimport * as labels from './labels';\n\nexport type DefaultContextProps =\n  | 'captionLayout'\n  | 'classNames'\n  | 'formatters'\n  | 'locale'\n  | 'labels'\n  | 'modifiersClassNames'\n  | 'modifiers'\n  | 'numberOfMonths'\n  | 'styles'\n  | 'today'\n  | 'mode';\n\nexport type DefaultContextValues = Pick<\n  DayPickerContextValue,\n  DefaultContextProps\n>;\n/**\n * Returns the default values to use in the DayPickerContext, in case they are\n * not passed down with the DayPicker initial props.\n */\nexport function getDefaultContextValues(): DefaultContextValues {\n  const captionLayout: CaptionLayout = 'buttons';\n  const classNames = defaultClassNames;\n  const locale = enUS;\n  const modifiersClassNames = {};\n  const modifiers = {};\n  const numberOfMonths = 1;\n  const styles = {};\n  const today = new Date();\n\n  return {\n    captionLayout,\n    classNames,\n    formatters,\n    labels,\n    locale,\n    modifiersClassNames,\n    modifiers,\n    numberOfMonths,\n    styles,\n    today,\n    mode: 'default'\n  };\n}\n", "import { endOfMonth, startOfDay, startOfMonth } from 'date-fns';\n\nimport { DayPickerBase } from 'types/DayPickerBase';\n\n/** Return the `fromDate` and `toDate` prop values values parsing the DayPicker props. */\nexport function parseFromToProps(\n  props: Pick<\n    DayPickerBase,\n    'fromYear' | 'toYear' | 'fromDate' | 'toDate' | 'fromMonth' | 'toMonth'\n  >\n): { fromDate: Date | undefined; toDate: Date | undefined } {\n  const { fromYear, toYear, fromMonth, toMonth } = props;\n  let { fromDate, toDate } = props;\n\n  if (fromMonth) {\n    fromDate = startOfMonth(fromMonth);\n  } else if (fromYear) {\n    fromDate = new Date(fromYear, 0, 1);\n  }\n  if (toMonth) {\n    toDate = endOfMonth(toMonth);\n  } else if (toYear) {\n    toDate = new Date(toYear, 11, 31);\n  }\n\n  return {\n    fromDate: fromDate ? startOfDay(fromDate) : undefined,\n    toDate: toDate ? startOfDay(toDate) : undefined\n  };\n}\n", "import { createContext, ReactNode, useContext } from 'react';\n\nimport { Locale } from 'date-fns';\nimport { DayPickerProps } from 'DayPicker';\n\nimport { CaptionLayout } from 'components/Caption';\nimport { DayPickerBase, DaySelectionMode } from 'types/DayPickerBase';\nimport {\n  DayPickerMultipleProps,\n  isDayPickerMultiple\n} from 'types/DayPickerMultiple';\nimport { DayPickerRangeProps, isDayPickerRange } from 'types/DayPickerRange';\nimport { DayPickerSingleProps, isDayPickerSingle } from 'types/DayPickerSingle';\nimport { Formatters } from 'types/Formatters';\nimport { Labels } from 'types/Labels';\nimport { Matcher } from 'types/Matchers';\nimport { DayModifiers, ModifiersClassNames } from 'types/Modifiers';\nimport { ClassNames, Styles } from 'types/Styles';\n\nimport { getDefaultContextValues } from './defaultContextValues';\nimport { parseFromToProps } from './utils';\n\n/**\n * The value of the {@link DayPickerContext} extends the props from DayPicker\n * with default and cleaned up values.\n */\nexport interface DayPickerContextValue extends DayPickerBase {\n  mode: DaySelectionMode;\n  onSelect?:\n    | DayPickerSingleProps['onSelect']\n    | DayPickerMultipleProps['onSelect']\n    | DayPickerRangeProps['onSelect'];\n  required?: boolean;\n  min?: number;\n  max?: number;\n  selected?: Matcher | Matcher[];\n\n  captionLayout: CaptionLayout;\n  classNames: Required<ClassNames>;\n  formatters: Formatters;\n  labels: Labels;\n  locale: Locale;\n  modifiersClassNames: ModifiersClassNames;\n  modifiers: DayModifiers;\n  numberOfMonths: number;\n  styles: Styles;\n  today: Date;\n}\n\n/**\n * The DayPicker context shares the props passed to DayPicker within internal\n * and custom components. It is used to set the default values and perform\n * one-time calculations required to render the days.\n *\n * Access to this context from the {@link useDayPicker} hook.\n */\nexport const DayPickerContext = createContext<\n  DayPickerContextValue | undefined\n>(undefined);\n\n/** The props for the {@link DayPickerProvider}. */\nexport interface DayPickerProviderProps {\n  /** The initial props from the DayPicker component. */\n  initialProps: DayPickerProps;\n  children?: ReactNode;\n}\n/**\n * The provider for the {@link DayPickerContext}, assigning the defaults from the\n * initial DayPicker props.\n */\nexport function DayPickerProvider(props: DayPickerProviderProps): JSX.Element {\n  const { initialProps } = props;\n\n  const defaultContextValues = getDefaultContextValues();\n\n  const { fromDate, toDate } = parseFromToProps(initialProps);\n\n  let captionLayout =\n    initialProps.captionLayout ?? defaultContextValues.captionLayout;\n  if (captionLayout !== 'buttons' && (!fromDate || !toDate)) {\n    // When no from/to dates are set, the caption is always buttons\n    captionLayout = 'buttons';\n  }\n\n  let onSelect;\n  if (\n    isDayPickerSingle(initialProps) ||\n    isDayPickerMultiple(initialProps) ||\n    isDayPickerRange(initialProps)\n  ) {\n    onSelect = initialProps.onSelect;\n  }\n\n  const value: DayPickerContextValue = {\n    ...defaultContextValues,\n    ...initialProps,\n    captionLayout,\n    classNames: {\n      ...defaultContextValues.classNames,\n      ...initialProps.classNames\n    },\n    components: {\n      ...initialProps.components\n    },\n    formatters: {\n      ...defaultContextValues.formatters,\n      ...initialProps.formatters\n    },\n    fromDate,\n    labels: {\n      ...defaultContextValues.labels,\n      ...initialProps.labels\n    },\n    mode: initialProps.mode || defaultContextValues.mode,\n    modifiers: {\n      ...defaultContextValues.modifiers,\n      ...initialProps.modifiers\n    },\n    modifiersClassNames: {\n      ...defaultContextValues.modifiersClassNames,\n      ...initialProps.modifiersClassNames\n    },\n    onSelect,\n    styles: {\n      ...defaultContextValues.styles,\n      ...initialProps.styles\n    },\n    toDate\n  };\n\n  return (\n    <DayPickerContext.Provider value={value}>\n      {props.children}\n    </DayPickerContext.Provider>\n  );\n}\n\n/**\n * Hook to access the {@link DayPickerContextValue}.\n *\n * Use the DayPicker context to access to the props passed to DayPicker inside\n * internal or custom components.\n */\nexport function useDayPicker(): DayPickerContextValue {\n  const context = useContext(DayPickerContext);\n  if (!context) {\n    throw new Error(`useDayPicker must be used within a DayPickerProvider.`);\n  }\n  return context;\n}\n", "import { useDayPicker } from 'contexts/DayPicker';\n\n/** The props for the {@link CaptionLabel} component. */\nexport interface CaptionLabelProps {\n  /** The ID for the heading element. Must be the same as the labelled-by in Table. */\n  id?: string;\n  /** The month where the caption is displayed. */\n  displayMonth: Date;\n  /** The index of the month where the caption is displayed. Older custom components may miss this prop. */\n  displayIndex?: number | undefined;\n}\n\n/** Render the caption for the displayed month. This component is used when `captionLayout=\"buttons\"`. */\nexport function CaptionLabel(props: CaptionLabelProps): JSX.Element {\n  const {\n    locale,\n    classNames,\n    styles,\n    formatters: { formatCaption }\n  } = useDayPicker();\n  return (\n    <div\n      className={classNames.caption_label}\n      style={styles.caption_label}\n      aria-live=\"polite\"\n      role=\"presentation\"\n      id={props.id}\n    >\n      {formatCaption(props.displayMonth, { locale })}\n    </div>\n  );\n}\n", "import { StyledComponent } from 'types/Styles';\n\n/**\n * Render the icon in the styled drop-down.\n */\nexport function IconDropdown(props: StyledComponent): JSX.Element {\n  return (\n    <svg\n      width=\"8px\"\n      height=\"8px\"\n      viewBox=\"0 0 120 120\"\n      data-testid=\"iconDropdown\"\n      {...props}\n    >\n      <path\n        d=\"M4.22182541,48.2218254 C8.44222828,44.0014225 15.2388494,43.9273804 19.5496459,47.9996989 L19.7781746,48.2218254 L60,88.443 L100.221825,48.2218254 C104.442228,44.0014225 111.238849,43.9273804 115.549646,47.9996989 L115.778175,48.2218254 C119.998577,52.4422283 120.07262,59.2388494 116.000301,63.5496459 L115.778175,63.7781746 L67.7781746,111.778175 C63.5577717,115.998577 56.7611506,116.07262 52.4503541,112.000301 L52.2218254,111.778175 L4.22182541,63.7781746 C-0.0739418023,59.4824074 -0.0739418023,52.5175926 4.22182541,48.2218254 Z\"\n        fill=\"currentColor\"\n        fillRule=\"nonzero\"\n      ></path>\n    </svg>\n  );\n}\n", "import {\n  ChangeEventHandler,\n  CSSProperties,\n  ReactNode,\n  SelectHTMLAttributes\n} from 'react';\n\nimport { IconDropdown } from 'components/IconDropdown';\nimport { useDayPicker } from 'contexts/DayPicker';\n\n/** The props for the {@link Dropdown} component. */\nexport interface DropdownProps {\n  /** The name attribute of the element. */\n  name?: string;\n  /** The caption displayed to replace the hidden select. */\n  caption?: ReactNode;\n  children?: SelectHTMLAttributes<HTMLSelectElement>['children'];\n  className?: string;\n  ['aria-label']?: string;\n  style?: CSSProperties;\n  /** The selected value. */\n  value?: string | number;\n  onChange?: ChangeEventHandler<HTMLSelectElement>;\n}\n\n/**\n * Render a styled select component – displaying a caption and a custom\n * drop-down icon.\n */\nexport function Dropdown(props: DropdownProps): JSX.Element {\n  const { onChange, value, children, caption, className, style } = props;\n  const dayPicker = useDayPicker();\n\n  const IconDropdownComponent =\n    dayPicker.components?.IconDropdown ?? IconDropdown;\n  return (\n    <div className={className} style={style}>\n      <span className={dayPicker.classNames.vhidden}>\n        {props['aria-label']}\n      </span>\n      <select\n        name={props.name}\n        aria-label={props['aria-label']}\n        className={dayPicker.classNames.dropdown}\n        style={dayPicker.styles.dropdown}\n        value={value}\n        onChange={onChange}\n      >\n        {children}\n      </select>\n      <div\n        className={dayPicker.classNames.caption_label}\n        style={dayPicker.styles.caption_label}\n        aria-hidden=\"true\"\n      >\n        {caption}\n        {\n          <IconDropdownComponent\n            className={dayPicker.classNames.dropdown_icon}\n            style={dayPicker.styles.dropdown_icon}\n          />\n        }\n      </div>\n    </div>\n  );\n}\n", "import { ChangeEventHandler } from 'react';\n\nimport { isSameYear, setMonth, startOfMonth } from 'date-fns';\n\nimport { Dropdown } from 'components/Dropdown';\nimport { useDayPicker } from 'contexts/DayPicker';\nimport { MonthChangeEventHandler } from 'types/EventHandlers';\n\n/** The props for the {@link MonthsDropdown} component. */\nexport interface MonthsDropdownProps {\n  /** The month where the dropdown is displayed. */\n  displayMonth: Date;\n  onChange: MonthChangeEventHandler;\n}\n\n/** Render the dropdown to navigate between months. */\nexport function MonthsDropdown(props: MonthsDropdownProps): JSX.Element {\n  const {\n    fromDate,\n    toDate,\n    styles,\n    locale,\n    formatters: { formatMonthCaption },\n    classNames,\n    components,\n    labels: { labelMonthDropdown }\n  } = useDayPicker();\n\n  // Dropdown should appear only when both from/toDate is set\n  if (!fromDate) return <></>;\n  if (!toDate) return <></>;\n\n  const dropdownMonths: Date[] = [];\n\n  if (isSameYear(fromDate, toDate)) {\n    // only display the months included in the range\n    const date = startOfMonth(fromDate);\n    for (let month = fromDate.getMonth(); month <= toDate.getMonth(); month++) {\n      dropdownMonths.push(setMonth(date, month));\n    }\n  } else {\n    // display all the 12 months\n    const date = startOfMonth(new Date()); // Any date should be OK, as we just need the year\n    for (let month = 0; month <= 11; month++) {\n      dropdownMonths.push(setMonth(date, month));\n    }\n  }\n\n  const handleChange: ChangeEventHandler<HTMLSelectElement> = (e) => {\n    const selectedMonth = Number(e.target.value);\n    const newMonth = setMonth(startOfMonth(props.displayMonth), selectedMonth);\n    props.onChange(newMonth);\n  };\n\n  const DropdownComponent = components?.Dropdown ?? Dropdown;\n\n  return (\n    <DropdownComponent\n      name=\"months\"\n      aria-label={labelMonthDropdown()}\n      className={classNames.dropdown_month}\n      style={styles.dropdown_month}\n      onChange={handleChange}\n      value={props.displayMonth.getMonth()}\n      caption={formatMonthCaption(props.displayMonth, { locale })}\n    >\n      {dropdownMonths.map((m) => (\n        <option key={m.getMonth()} value={m.getMonth()}>\n          {formatMonthCaption(m, { locale })}\n        </option>\n      ))}\n    </DropdownComponent>\n  );\n}\n", "import { ChangeEventHandler } from 'react';\n\nimport { setYear, startOfMonth, startOfYear } from 'date-fns';\n\nimport { Dropdown } from 'components/Dropdown';\nimport { useDayPicker } from 'contexts/DayPicker';\nimport { MonthChangeEventHandler } from 'types/EventHandlers';\n\n/**\n * The props for the {@link YearsDropdown} component.\n */\nexport interface YearsDropdownProps {\n  /** The month where the drop-down is displayed. */\n  displayMonth: Date;\n  /** Callback to handle the `change` event. */\n  onChange: MonthChangeEventHandler;\n}\n\n/**\n * Render a dropdown to change the year. Take in account the `nav.fromDate` and\n * `toDate` from context.\n */\nexport function YearsDropdown(props: YearsDropdownProps): JSX.Element {\n  const { displayMonth } = props;\n  const {\n    fromDate,\n    toDate,\n    locale,\n    styles,\n    classNames,\n    components,\n    formatters: { formatYearCaption },\n    labels: { labelYearDropdown }\n  } = useDayPicker();\n\n  const years: Date[] = [];\n\n  // Dropdown should appear only when both from/toDate is set\n  if (!fromDate) return <></>;\n  if (!toDate) return <></>;\n\n  const fromYear = fromDate.getFullYear();\n  const toYear = toDate.getFullYear();\n  for (let year = fromYear; year <= toYear; year++) {\n    years.push(setYear(startOfYear(new Date()), year));\n  }\n\n  const handleChange: ChangeEventHandler<HTMLSelectElement> = (e) => {\n    const newMonth = setYear(\n      startOfMonth(displayMonth),\n      Number(e.target.value)\n    );\n    props.onChange(newMonth);\n  };\n\n  const DropdownComponent = components?.Dropdown ?? Dropdown;\n\n  return (\n    <DropdownComponent\n      name=\"years\"\n      aria-label={labelYearDropdown()}\n      className={classNames.dropdown_year}\n      style={styles.dropdown_year}\n      onChange={handleChange}\n      value={displayMonth.getFullYear()}\n      caption={formatYearCaption(displayMonth, { locale })}\n    >\n      {years.map((year) => (\n        <option key={year.getFullYear()} value={year.getFullYear()}>\n          {formatYearCaption(year, { locale })}\n        </option>\n      ))}\n    </DropdownComponent>\n  );\n}\n", "import { Dispatch, SetStateAction, useState } from 'react';\n\nexport type DispatchStateAction<T> = Dispatch<SetStateAction<T>>;\n\n/**\n * Helper hook for using controlled/uncontrolled values from a component props.\n *\n * When the value is not controlled, pass `undefined` as `controlledValue` and\n * use the returned setter to update it.\n *\n * When the value is controlled, pass the controlled value as second\n * argument, which will be always returned as `value`.\n */\nexport function useControlledValue<T>(\n  defaultValue: T,\n  controlledValue: T | undefined\n): [T, DispatchStateAction<T>] {\n  const [uncontrolledValue, setValue] = useState(defaultValue);\n\n  const value =\n    controlledValue === undefined ? uncontrolledValue : controlledValue;\n\n  return [value, setValue] as [T, DispatchStateAction<T>];\n}\n", "import { addMonths, differenceInCalendarMonths, startOfMonth } from 'date-fns';\n\nimport { DayPickerContextValue } from 'contexts/DayPicker';\n\n/** Return the initial month according to the given options. */\nexport function getInitialMonth(context: Partial<DayPickerContextValue>): Date {\n  const { month, defaultMonth, today } = context;\n  let initialMonth = month || defaultMonth || today || new Date();\n\n  const { toDate, fromDate, numberOfMonths = 1 } = context;\n\n  // Fix the initialMonth if is after the to-date\n  if (toDate && differenceInCalendarMonths(toDate, initialMonth) < 0) {\n    const offset = -1 * (numberOfMonths - 1);\n    initialMonth = addMonths(toDate, offset);\n  }\n  // Fix the initialMonth if is before the from-date\n  if (fromDate && differenceInCalendarMonths(initialMonth, fromDate) < 0) {\n    initialMonth = fromDate;\n  }\n  return startOfMonth(initialMonth);\n}\n", "import { startOfMonth } from 'date-fns';\n\nimport { useDayPicker } from 'contexts/DayPicker';\nimport { useControlledValue } from 'hooks/useControlledValue';\n\nimport { getInitialMonth } from './utils/getInitialMonth';\n\nexport type NavigationState = [\n  /** The month DayPicker is navigating at */\n  month: Date,\n  /** Go to the specified month. */\n  goToMonth: (month: Date) => void\n];\n\n/** Controls the navigation state. */\nexport function useNavigationState(): NavigationState {\n  const context = useDayPicker();\n  const initialMonth = getInitialMonth(context);\n  const [month, setMonth] = useControlledValue(initialMonth, context.month);\n\n  const goToMonth = (date: Date) => {\n    if (context.disableNavigation) return;\n    const month = startOfMonth(date);\n    setMonth(month);\n    context.onMonthChange?.(month);\n  };\n\n  return [month, goToMonth];\n}\n", "import { addMonths, differenceInCalendarMonths, startOfMonth } from 'date-fns';\n\n/**\n * Return the months to display in the component according to the number of\n * months and the from/to date.\n */\nexport function getDisplayMonths(\n  month: Date,\n  {\n    reverseMonths,\n    numberOfMonths\n  }: {\n    reverseMonths?: boolean;\n    numberOfMonths: number;\n  }\n): Date[] {\n  const start = startOfMonth(month);\n  const end = startOfMonth(addMonths(start, numberOfMonths));\n  const monthsDiff = differenceInCalendarMonths(end, start);\n  let months = [];\n\n  for (let i = 0; i < monthsDiff; i++) {\n    const nextMonth = addMonths(start, i);\n    months.push(nextMonth);\n  }\n\n  if (reverseMonths) months = months.reverse();\n  return months;\n}\n", "import { addMonths, differenceInCalendarMonths, startOfMonth } from 'date-fns';\n\n/**\n * Returns the next month the user can navigate to according to the given\n * options.\n *\n * Please note that the next month is not always the next calendar month:\n *\n * - if after the `toDate` range, is undefined;\n * - if the navigation is paged, is the number of months displayed ahead.\n *\n */\nexport function getNextMonth(\n  startingMonth: Date,\n  options: {\n    numberOfMonths?: number;\n    fromDate?: Date;\n    toDate?: Date;\n    pagedNavigation?: boolean;\n    today?: Date;\n    disableNavigation?: boolean;\n  }\n): Date | undefined {\n  if (options.disableNavigation) {\n    return undefined;\n  }\n  const { toDate, pagedNavigation, numberOfMonths = 1 } = options;\n  const offset = pagedNavigation ? numberOfMonths : 1;\n  const month = startOfMonth(startingMonth);\n\n  if (!toDate) {\n    return addMonths(month, offset);\n  }\n\n  const monthsDiff = differenceInCalendarMonths(toDate, startingMonth);\n\n  if (monthsDiff < numberOfMonths) {\n    return undefined;\n  }\n\n  // Jump forward as the number of months when paged navigation\n  return addMonths(month, offset);\n}\n", "import { addMonths, differenceInCalendarMonths, startOfMonth } from 'date-fns';\n\n/**\n * Returns the next previous the user can navigate to, according to the given\n * options.\n *\n * Please note that the previous month is not always the previous calendar\n * month:\n *\n * - if before the `fromDate` date, is `undefined`;\n * - if the navigation is paged, is the number of months displayed before.\n *\n */\nexport function getPreviousMonth(\n  startingMonth: Date,\n  options: {\n    numberOfMonths?: number;\n    fromDate?: Date;\n    toDate?: Date;\n    pagedNavigation?: boolean;\n    today?: Date;\n    disableNavigation?: boolean;\n  }\n): Date | undefined {\n  if (options.disableNavigation) {\n    return undefined;\n  }\n  const { fromDate, pagedNavigation, numberOfMonths = 1 } = options;\n  const offset = pagedNavigation ? numberOfMonths : 1;\n  const month = startOfMonth(startingMonth);\n  if (!fromDate) {\n    return addMonths(month, -offset);\n  }\n  const monthsDiff = differenceInCalendarMonths(month, fromDate);\n\n  if (monthsDiff <= 0) {\n    return undefined;\n  }\n\n  // Jump back as the number of months when paged navigation\n  return addMonths(month, -offset);\n}\n", "import { createContext, ReactNode, useContext } from 'react';\n\nimport { addMonths, isBefore, isSameMonth } from 'date-fns';\n\nimport { useDayPicker } from '../DayPicker';\nimport { useNavigationState } from './useNavigationState';\nimport { getDisplayMonths } from './utils/getDisplayMonths';\nimport { getNextMonth } from './utils/getNextMonth';\nimport { getPreviousMonth } from './utils/getPreviousMonth';\n\n/** Represents the value of the {@link NavigationContext}. */\nexport interface NavigationContextValue {\n  /** The month to display in the calendar. When `numberOfMonths` is greater than one, is the first of the displayed months. */\n  currentMonth: Date;\n  /** The months rendered by DayPicker. DayPicker can render multiple months via `numberOfMonths`. */\n  displayMonths: Date[];\n  /** Navigate to the specified month. */\n  goToMonth: (month: Date) => void;\n  /** Navigate to the specified date. */\n  goToDate: (date: Date, refDate?: Date) => void;\n  /** The next month to display. */\n  nextMonth?: Date;\n  /** The previous month to display. */\n  previousMonth?: Date;\n  /** Whether the given day is included in the displayed months. */\n  isDateDisplayed: (day: Date) => boolean;\n}\n\n/**\n * The Navigation context shares details and methods to navigate the months in DayPicker.\n * Access this context from the {@link useNavigation} hook.\n */\nexport const NavigationContext = createContext<\n  NavigationContextValue | undefined\n>(undefined);\n\n/** Provides the values for the {@link NavigationContext}. */\nexport function NavigationProvider(props: {\n  children?: ReactNode;\n}): JSX.Element {\n  const dayPicker = useDayPicker();\n  const [currentMonth, goToMonth] = useNavigationState();\n\n  const displayMonths = getDisplayMonths(currentMonth, dayPicker);\n  const nextMonth = getNextMonth(currentMonth, dayPicker);\n  const previousMonth = getPreviousMonth(currentMonth, dayPicker);\n\n  const isDateDisplayed = (date: Date) => {\n    return displayMonths.some((displayMonth) =>\n      isSameMonth(date, displayMonth)\n    );\n  };\n\n  const goToDate = (date: Date, refDate?: Date) => {\n    if (isDateDisplayed(date)) {\n      return;\n    }\n\n    if (refDate && isBefore(date, refDate)) {\n      goToMonth(addMonths(date, 1 + dayPicker.numberOfMonths * -1));\n    } else {\n      goToMonth(date);\n    }\n  };\n\n  const value: NavigationContextValue = {\n    currentMonth,\n    displayMonths,\n    goToMonth,\n    goToDate,\n    previousMonth,\n    nextMonth,\n    isDateDisplayed\n  };\n\n  return (\n    <NavigationContext.Provider value={value}>\n      {props.children}\n    </NavigationContext.Provider>\n  );\n}\n\n/**\n * Hook to access the {@link NavigationContextValue}. Use this hook to navigate\n * between months or years in DayPicker.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nexport function useNavigation(): NavigationContextValue {\n  const context = useContext(NavigationContext);\n  if (!context) {\n    throw new Error('useNavigation must be used within a NavigationProvider');\n  }\n  return context;\n}\n", "import { addMonths } from 'date-fns';\n\nimport { CaptionProps } from 'components/Caption/Caption';\nimport { CaptionLabel } from 'components/CaptionLabel';\nimport { MonthsDropdown } from 'components/MonthsDropdown';\nimport { YearsDropdown } from 'components/YearsDropdown';\nimport { useDayPicker } from 'contexts/DayPicker';\nimport { useNavigation } from 'contexts/Navigation';\nimport { MonthChangeEventHandler } from 'types/EventHandlers';\n\n/**\n * Render a caption with the dropdowns to navigate between months and years.\n */\nexport function CaptionDropdowns(props: CaptionProps): JSX.Element {\n  const { classNames, styles, components } = useDayPicker();\n  const { goToMonth } = useNavigation();\n\n  const handleMonthChange: MonthChangeEventHandler = (newMonth) => {\n    goToMonth(\n      addMonths(newMonth, props.displayIndex ? -props.displayIndex : 0)\n    );\n  };\n  const CaptionLabelComponent = components?.CaptionLabel ?? CaptionLabel;\n  const captionLabel = (\n    <CaptionLabelComponent id={props.id} displayMonth={props.displayMonth} />\n  );\n  return (\n    <div\n      className={classNames.caption_dropdowns}\n      style={styles.caption_dropdowns}\n    >\n      {/* Caption label is visually hidden but for a11y. */}\n      <div className={classNames.vhidden}>{captionLabel}</div>\n      <MonthsDropdown\n        onChange={handleMonthChange}\n        displayMonth={props.displayMonth}\n      />\n      <YearsDropdown\n        onChange={handleMonthChange}\n        displayMonth={props.displayMonth}\n      />\n    </div>\n  );\n}\n", "import { StyledComponent } from 'types/Styles';\n\n/**\n * Ren<PERSON> the \"previous month\" button in the navigation.\n */\nexport function IconLeft(props: StyledComponent): JSX.Element {\n  return (\n    <svg width=\"16px\" height=\"16px\" viewBox=\"0 0 120 120\" {...props}>\n      <path\n        d=\"M69.490332,3.34314575 C72.6145263,0.218951416 77.6798462,0.218951416 80.8040405,3.34314575 C83.8617626,6.40086786 83.9268205,11.3179931 80.9992143,14.4548388 L80.8040405,14.6568542 L35.461,60 L80.8040405,105.343146 C83.8617626,108.400868 83.9268205,113.317993 80.9992143,116.454839 L80.8040405,116.656854 C77.7463184,119.714576 72.8291931,119.779634 69.6923475,116.852028 L69.490332,116.656854 L18.490332,65.6568542 C15.4326099,62.5991321 15.367552,57.6820069 18.2951583,54.5451612 L18.490332,54.3431458 L69.490332,3.34314575 Z\"\n        fill=\"currentColor\"\n        fillRule=\"nonzero\"\n      ></path>\n    </svg>\n  );\n}\n", "import { StyledComponent } from 'types/Styles';\n\n/**\n * Ren<PERSON> the \"next month\" button in the navigation.\n */\nexport function IconRight(props: StyledComponent): JSX.Element {\n  return (\n    <svg width=\"16px\" height=\"16px\" viewBox=\"0 0 120 120\" {...props}>\n      <path\n        d=\"M49.8040405,3.34314575 C46.6798462,0.218951416 41.6145263,0.218951416 38.490332,3.34314575 C35.4326099,6.40086786 35.367552,11.3179931 38.2951583,14.4548388 L38.490332,14.6568542 L83.8333725,60 L38.490332,105.343146 C35.4326099,108.400868 35.367552,113.317993 38.2951583,116.454839 L38.490332,116.656854 C41.5480541,119.714576 46.4651794,119.779634 49.602025,116.852028 L49.8040405,116.656854 L100.804041,65.6568542 C103.861763,62.5991321 103.926821,57.6820069 100.999214,54.5451612 L100.804041,54.3431458 L49.8040405,3.34314575 Z\"\n        fill=\"currentColor\"\n      ></path>\n    </svg>\n  );\n}\n", "import { forwardRef } from 'react';\n\nimport { useDayPicker } from 'contexts/DayPicker';\n\n/** The props for the {@link Button} component. */\nexport type ButtonProps = JSX.IntrinsicElements['button'];\n\n/** Render a button HTML element applying the reset class name. */\nexport const Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  (props, ref) => {\n    const { classNames, styles } = useDayPicker();\n\n    const classNamesArr = [classNames.button_reset, classNames.button];\n    if (props.className) {\n      classNamesArr.push(props.className);\n    }\n    const className = classNamesArr.join(' ');\n\n    const style = { ...styles.button_reset, ...styles.button };\n    if (props.style) {\n      Object.assign(style, props.style);\n    }\n\n    return (\n      <button\n        {...props}\n        ref={ref}\n        type=\"button\"\n        className={className}\n        style={style}\n      />\n    );\n  }\n);\n", "import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react';\n\nimport { IconLeft } from 'components/IconLeft';\nimport { IconRight } from 'components/IconRight';\nimport { useDayPicker } from 'contexts/DayPicker';\n\nimport { Button } from '../Button';\n\n/** The props for the {@link Navigation} component. */\nexport interface NavigationProps {\n  /** The month where the caption is displayed. */\n  displayMonth: Date;\n  /** The previous month. */\n  previousMonth?: Date;\n  /** The next month. */\n  nextMonth?: Date;\n  /** Hide the previous button. */\n  hidePrevious: boolean;\n  /** Hide the next button. */\n  hideNext: boolean;\n  /** Event handler when the next button is clicked. */\n  onNextClick: MouseEventHandler<HTMLButtonElement>;\n  /** Event handler when the previous button is clicked. */\n  onPreviousClick: MouseEventHandler<HTMLButtonElement>;\n}\n\n/** A component rendering the navigation buttons or the drop-downs. */\nexport function Navigation(props: NavigationProps): JSX.Element {\n  const {\n    dir,\n    locale,\n    classNames,\n    styles,\n    labels: { labelPrevious, labelNext },\n    components\n  } = useDayPicker();\n\n  if (!props.nextMonth && !props.previousMonth) {\n    return <></>;\n  }\n\n  const previousLabel = labelPrevious(props.previousMonth, { locale });\n  const previousClassName = [\n    classNames.nav_button,\n    classNames.nav_button_previous\n  ].join(' ');\n\n  const nextLabel = labelNext(props.nextMonth, { locale });\n  const nextClassName = [\n    classNames.nav_button,\n    classNames.nav_button_next\n  ].join(' ');\n\n  const IconRightComponent = components?.IconRight ?? IconRight;\n  const IconLeftComponent = components?.IconLeft ?? IconLeft;\n  return (\n    <div className={classNames.nav} style={styles.nav}>\n      {!props.hidePrevious && (\n        <Button\n          name=\"previous-month\"\n          aria-label={previousLabel}\n          className={previousClassName}\n          style={styles.nav_button_previous}\n          disabled={!props.previousMonth}\n          onClick={props.onPreviousClick}\n        >\n          {dir === 'rtl' ? (\n            <IconRightComponent\n              className={classNames.nav_icon}\n              style={styles.nav_icon}\n            />\n          ) : (\n            <IconLeftComponent\n              className={classNames.nav_icon}\n              style={styles.nav_icon}\n            />\n          )}\n        </Button>\n      )}\n      {!props.hideNext && (\n        <Button\n          name=\"next-month\"\n          aria-label={nextLabel}\n          className={nextClassName}\n          style={styles.nav_button_next}\n          disabled={!props.nextMonth}\n          onClick={props.onNextClick}\n        >\n          {dir === 'rtl' ? (\n            <IconLeftComponent\n              className={classNames.nav_icon}\n              style={styles.nav_icon}\n            />\n          ) : (\n            <IconRightComponent\n              className={classNames.nav_icon}\n              style={styles.nav_icon}\n            />\n          )}\n        </Button>\n      )}\n    </div>\n  );\n}\n", "import { MouseEventHandler } from 'react';\n\nimport { isSameMonth } from 'date-fns';\n\nimport { CaptionProps } from 'components/Caption/Caption';\nimport { Navigation } from 'components/Navigation';\nimport { useDayPicker } from 'contexts/DayPicker';\nimport { useNavigation } from 'contexts/Navigation';\n\n/**\n * Render a caption with a button-based navigation.\n */\nexport function CaptionNavigation(props: CaptionProps): JSX.Element {\n  const { numberOfMonths } = useDayPicker();\n  const { previousMonth, nextMonth, goToMonth, displayMonths } =\n    useNavigation();\n\n  const displayIndex = displayMonths.findIndex((month) =>\n    isSameMonth(props.displayMonth, month)\n  );\n\n  const isFirst = displayIndex === 0;\n  const isLast = displayIndex === displayMonths.length - 1;\n\n  const hideNext = numberOfMonths > 1 && (isFirst || !isLast);\n  const hidePrevious = numberOfMonths > 1 && (isLast || !isFirst);\n\n  const handlePreviousClick: MouseEventHandler = () => {\n    if (!previousMonth) return;\n    goToMonth(previousMonth);\n  };\n\n  const handleNextClick: MouseEventHandler = () => {\n    if (!nextMonth) return;\n    goToMonth(nextMonth);\n  };\n\n  return (\n    <Navigation\n      displayMonth={props.displayMonth}\n      hideNext={hideNext}\n      hidePrevious={hidePrevious}\n      nextMonth={nextMonth}\n      previousMonth={previousMonth}\n      onPreviousClick={handlePreviousClick}\n      onNextClick={handleNextClick}\n    />\n  );\n}\n", "import { CaptionDropdowns } from 'components/CaptionDropdowns';\nimport { CaptionLabel } from 'components/CaptionLabel';\nimport { CaptionNavigation } from 'components/CaptionNavigation';\nimport { useDayPicker } from 'contexts/DayPicker';\n\n/** Represent the props of the {@link Caption} component. */\nexport interface CaptionProps {\n  /** The ID for the heading element. Must be the same as the labelled-by in Table. */\n  id?: string;\n  /** The month where the caption is displayed. */\n  displayMonth: Date;\n  /** The index of the month where the caption is displayed. Older custom components may miss this prop. */\n  displayIndex?: number | undefined;\n}\n\n/**\n * The layout of the caption:\n *\n * - `dropdown`: display dropdowns for choosing the month and the year.\n * - `buttons`: display previous month / next month buttons.\n * - `dropdown-buttons`: display both month / year dropdowns and previous month / next month buttons.\n */\nexport type CaptionLayout = 'dropdown' | 'buttons' | 'dropdown-buttons';\n\n/**\n * Render the caption of a month. The caption has a different layout when\n * setting the {@link DayPickerBase.captionLayout} prop.\n */\nexport function Caption(props: CaptionProps): JSX.Element {\n  const { classNames, disableNavigation, styles, captionLayout, components } =\n    useDayPicker();\n\n  const CaptionLabelComponent = components?.CaptionLabel ?? CaptionLabel;\n\n  let caption: JSX.Element;\n  if (disableNavigation) {\n    caption = (\n      <CaptionLabelComponent id={props.id} displayMonth={props.displayMonth} />\n    );\n  } else if (captionLayout === 'dropdown') {\n    caption = (\n      <CaptionDropdowns displayMonth={props.displayMonth} id={props.id} />\n    );\n  } else if (captionLayout === 'dropdown-buttons') {\n    caption = (\n      <>\n        <CaptionDropdowns\n          displayMonth={props.displayMonth}\n          displayIndex={props.displayIndex}\n          id={props.id}\n        />\n        <CaptionNavigation\n          displayMonth={props.displayMonth}\n          displayIndex={props.displayIndex}\n          id={props.id}\n        />\n      </>\n    );\n  } else {\n    caption = (\n      <>\n        <CaptionLabelComponent\n          id={props.id}\n          displayMonth={props.displayMonth}\n          displayIndex={props.displayIndex}\n        />\n        <CaptionNavigation displayMonth={props.displayMonth} id={props.id} />\n      </>\n    );\n  }\n\n  return (\n    <div className={classNames.caption} style={styles.caption}>\n      {caption}\n    </div>\n  );\n}\n", "import { useDayPicker } from 'contexts/DayPicker';\n\nexport interface FooterProps {\n  /** The month where the footer is displayed. */\n  displayMonth?: Date;\n}\n/** Render the Footer component (empty as default).*/\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function Footer(props: FooterProps): JSX.Element {\n  const {\n    footer,\n    styles,\n    classNames: { tfoot }\n  } = useDayPicker();\n  if (!footer) return <></>;\n  return (\n    <tfoot className={tfoot} style={styles.tfoot}>\n      <tr>\n        <td colSpan={8}>{footer}</td>\n      </tr>\n    </tfoot>\n  );\n}\n", "import { addDays, Locale, startOfISOWeek, startOfWeek } from 'date-fns';\n\n/**\n * Generate a series of 7 days, starting from the week, to use for formatting\n * the weekday names (Monday, Tuesday, etc.).\n */\nexport function getWeekdays(\n  locale?: Locale,\n  /** The index of the first day of the week (0 - Sunday). */\n  weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6,\n  /** Use ISOWeek instead of locale/ */\n  ISOWeek?: boolean\n): Date[] {\n  const start = ISOWeek\n    ? startOfISOWeek(new Date())\n    : startOfWeek(new Date(), { locale, weekStartsOn });\n\n  const days = [];\n  for (let i = 0; i < 7; i++) {\n    const day = addDays(start, i);\n    days.push(day);\n  }\n  return days;\n}\n", "import { useDayPicker } from 'contexts/DayPicker';\n\nimport { getWeekdays } from './utils';\n\n/**\n * Render the HeadRow component - i.e. the table head row with the weekday names.\n */\nexport function HeadRow(): JSX.Element {\n  const {\n    classNames,\n    styles,\n    showWeekNumber,\n    locale,\n    weekStartsOn,\n    ISOWeek,\n    formatters: { formatWeekdayName },\n    labels: { labelWeekday }\n  } = useDayPicker();\n\n  const weekdays = getWeekdays(locale, weekStartsOn, ISOWeek);\n\n  return (\n    <tr style={styles.head_row} className={classNames.head_row}>\n      {showWeekNumber && (\n        <td style={styles.head_cell} className={classNames.head_cell}></td>\n      )}\n      {weekdays.map((weekday, i) => (\n        <th\n          key={i}\n          scope=\"col\"\n          className={classNames.head_cell}\n          style={styles.head_cell}\n          aria-label={labelWeekday(weekday, { locale })}\n        >\n          {formatWeekdayName(weekday, { locale })}\n        </th>\n      ))}\n    </tr>\n  );\n}\n", "import { HeadRow } from 'components/HeadRow';\nimport { useDayPicker } from 'contexts/DayPicker';\n\n/** Render the table head. */\nexport function Head(): JSX.Element {\n  const { classNames, styles, components } = useDayPicker();\n  const HeadRowComponent = components?.HeadRow ?? HeadRow;\n  return (\n    <thead style={styles.head} className={classNames.head}>\n      <HeadRowComponent />\n    </thead>\n  );\n}\n", "import { useDayPicker } from 'contexts/DayPicker';\nimport { ActiveModifiers } from 'types/Modifiers';\n\n/** Represent the props for the {@link DayContent} component. */\nexport interface DayContentProps {\n  /** The date representing the day. */\n  date: Date;\n  /** The month where the day is displayed. */\n  displayMonth: Date;\n  /** The active modifiers for the given date. */\n  activeModifiers: ActiveModifiers;\n}\n\n/** Render the content of the day cell. */\nexport function DayContent(props: DayContentProps): JSX.Element {\n  const {\n    locale,\n    formatters: { formatDay }\n  } = useDayPicker();\n\n  return <>{formatDay(props.date, { locale })}</>;\n}\n", "import { createContext, ReactNode, useContext } from 'react';\n\nimport { isSameDay } from 'date-fns';\n\nimport { DayPickerBase } from 'types/DayPickerBase';\nimport {\n  DayPickerMultipleProps,\n  isDayPickerMultiple\n} from 'types/DayPickerMultiple';\nimport { DayClickEventHandler } from 'types/EventHandlers';\nimport { InternalModifier, Modifiers } from 'types/Modifiers';\n\n/** Represent the modifiers that are changed by the multiple selection. */\nexport type SelectMultipleModifiers = Pick<\n  Modifiers,\n  InternalModifier.Disabled\n>;\n\n/** Represents the value of a {@link SelectMultipleContext}. */\nexport interface SelectMultipleContextValue {\n  /** The days that have been selected. */\n  selected: Date[] | undefined;\n  /** The modifiers for the corresponding selection. */\n  modifiers: SelectMultipleModifiers;\n  /** Event handler to attach to the day button to enable the multiple select. */\n  onDayClick?: DayClickEventHandler;\n}\n\n/**\n * The SelectMultiple context shares details about the selected days when in\n * multiple selection mode.\n *\n * Access this context from the {@link useSelectMultiple} hook.\n */\nexport const SelectMultipleContext = createContext<\n  SelectMultipleContextValue | undefined\n>(undefined);\n\nexport type SelectMultipleProviderProps = {\n  initialProps: DayPickerBase;\n  children?: ReactNode;\n};\n\n/** Provides the values for the {@link SelectMultipleContext}. */\nexport function SelectMultipleProvider(\n  props: SelectMultipleProviderProps\n): JSX.Element {\n  if (!isDayPickerMultiple(props.initialProps)) {\n    const emptyContextValue: SelectMultipleContextValue = {\n      selected: undefined,\n      modifiers: {\n        disabled: []\n      }\n    };\n    return (\n      <SelectMultipleContext.Provider value={emptyContextValue}>\n        {props.children}\n      </SelectMultipleContext.Provider>\n    );\n  }\n  return (\n    <SelectMultipleProviderInternal\n      initialProps={props.initialProps}\n      children={props.children}\n    />\n  );\n}\n\n/** @private */\nexport interface SelectMultipleProviderInternalProps {\n  initialProps: DayPickerMultipleProps;\n  children?: ReactNode;\n}\n\nexport function SelectMultipleProviderInternal({\n  initialProps,\n  children\n}: SelectMultipleProviderInternalProps): JSX.Element {\n  const { selected, min, max } = initialProps;\n\n  const onDayClick: DayClickEventHandler = (day, activeModifiers, e) => {\n    initialProps.onDayClick?.(day, activeModifiers, e);\n\n    const isMinSelected = Boolean(\n      activeModifiers.selected && min && selected?.length === min\n    );\n    if (isMinSelected) {\n      return;\n    }\n\n    const isMaxSelected = Boolean(\n      !activeModifiers.selected && max && selected?.length === max\n    );\n    if (isMaxSelected) {\n      return;\n    }\n\n    const selectedDays = selected ? [...selected] : [];\n\n    if (activeModifiers.selected) {\n      const index = selectedDays.findIndex((selectedDay) =>\n        isSameDay(day, selectedDay)\n      );\n      selectedDays.splice(index, 1);\n    } else {\n      selectedDays.push(day);\n    }\n    initialProps.onSelect?.(selectedDays, day, activeModifiers, e);\n  };\n\n  const modifiers: SelectMultipleModifiers = {\n    disabled: []\n  };\n\n  if (selected) {\n    modifiers.disabled.push((day: Date) => {\n      const isMaxSelected = max && selected.length > max - 1;\n      const isSelected = selected.some((selectedDay) =>\n        isSameDay(selectedDay, day)\n      );\n      return Boolean(isMaxSelected && !isSelected);\n    });\n  }\n\n  const contextValue = {\n    selected,\n    onDayClick,\n    modifiers\n  };\n\n  return (\n    <SelectMultipleContext.Provider value={contextValue}>\n      {children}\n    </SelectMultipleContext.Provider>\n  );\n}\n\n/**\n * Hook to access the {@link SelectMultipleContextValue}.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nexport function useSelectMultiple(): SelectMultipleContextValue {\n  const context = useContext(SelectMultipleContext);\n  if (!context) {\n    throw new Error(\n      'useSelectMultiple must be used within a SelectMultipleProvider'\n    );\n  }\n  return context;\n}\n", "import { isAfter, isBefore, isSameDay } from 'date-fns';\n\nimport { DateRange } from 'types/Matchers';\n\n/**\n * Add a day to an existing range.\n *\n * The returned range takes in account the `undefined` values and if the added\n * day is already present in the range.\n */\nexport function addToRange(\n  day: Date,\n  range?: DateRange\n): DateRange | undefined {\n  const { from, to } = range || {};\n  if (from && to) {\n    if (isSameDay(to, day) && isSameDay(from, day)) {\n      return undefined;\n    }\n    if (isSameDay(to, day)) {\n      return { from: to, to: undefined };\n    }\n    if (isSameDay(from, day)) {\n      return undefined;\n    }\n    if (isAfter(from, day)) {\n      return { from: day, to };\n    }\n    return { from, to: day };\n  }\n  if (to) {\n    if (isAfter(day, to)) {\n      return { from: to, to: day };\n    }\n    return { from: day, to };\n  }\n  if (from) {\n    if (isBefore(day, from)) {\n      return { from: day, to: from };\n    }\n    return { from, to: day };\n  }\n  return { from: day, to: undefined };\n}\n", "import { createContext, ReactNode, useContext } from 'react';\n\nimport {\n  addDays,\n  differenceInCalendarDays,\n  isSameDay,\n  subDays\n} from 'date-fns';\n\nimport { DayPickerBase } from 'types/DayPickerBase';\nimport { DayPickerRangeProps, isDayPickerRange } from 'types/DayPickerRange';\nimport { DayClickEventHandler } from 'types/EventHandlers';\nimport { DateRange } from 'types/Matchers';\nimport { InternalModifier, Modifiers } from 'types/Modifiers';\n\nimport { addToRange } from './utils/addToRange';\n\n/** Represent the modifiers that are changed by the range selection. */\nexport type SelectRangeModifiers = Pick<\n  Modifiers,\n  | InternalModifier.Disabled\n  | InternalModifier.RangeEnd\n  | InternalModifier.RangeMiddle\n  | InternalModifier.RangeStart\n>;\n\n/** Represents the value of a {@link SelectRangeContext}. */\nexport interface SelectRangeContextValue {\n  /** The range of days that has been selected. */\n  selected: DateRange | undefined;\n  /** The modifiers for the corresponding selection. */\n  modifiers: SelectRangeModifiers;\n  /** Event handler to attach to the day button to enable the range select. */\n  onDayClick?: DayClickEventHandler;\n}\n\n/**\n * The SelectRange context shares details about the selected days when in\n * range selection mode.\n *\n * Access this context from the {@link useSelectRange} hook.\n */\nexport const SelectRangeContext = createContext<\n  SelectRangeContextValue | undefined\n>(undefined);\n\nexport interface SelectRangeProviderProps {\n  initialProps: DayPickerBase;\n  children?: ReactNode;\n}\n\n/** Provides the values for the {@link SelectRangeProvider}. */\nexport function SelectRangeProvider(\n  props: SelectRangeProviderProps\n): JSX.Element {\n  if (!isDayPickerRange(props.initialProps)) {\n    const emptyContextValue: SelectRangeContextValue = {\n      selected: undefined,\n      modifiers: {\n        range_start: [],\n        range_end: [],\n        range_middle: [],\n        disabled: []\n      }\n    };\n    return (\n      <SelectRangeContext.Provider value={emptyContextValue}>\n        {props.children}\n      </SelectRangeContext.Provider>\n    );\n  }\n  return (\n    <SelectRangeProviderInternal\n      initialProps={props.initialProps}\n      children={props.children}\n    />\n  );\n}\n\n/** @private */\nexport interface SelectRangeProviderInternalProps {\n  initialProps: DayPickerRangeProps;\n  children?: ReactNode;\n}\n\nexport function SelectRangeProviderInternal({\n  initialProps,\n  children\n}: SelectRangeProviderInternalProps): JSX.Element {\n  const { selected } = initialProps;\n  const { from: selectedFrom, to: selectedTo } = selected || {};\n  const min = initialProps.min;\n  const max = initialProps.max;\n\n  const onDayClick: DayClickEventHandler = (day, activeModifiers, e) => {\n    initialProps.onDayClick?.(day, activeModifiers, e);\n    const newRange = addToRange(day, selected);\n    initialProps.onSelect?.(newRange, day, activeModifiers, e);\n  };\n\n  const modifiers: SelectRangeModifiers = {\n    range_start: [],\n    range_end: [],\n    range_middle: [],\n    disabled: []\n  };\n\n  if (selectedFrom) {\n    modifiers.range_start = [selectedFrom];\n    if (!selectedTo) {\n      modifiers.range_end = [selectedFrom];\n    } else {\n      modifiers.range_end = [selectedTo];\n      if (!isSameDay(selectedFrom, selectedTo)) {\n        modifiers.range_middle = [\n          {\n            after: selectedFrom,\n            before: selectedTo\n          }\n        ];\n      }\n    }\n  } else if (selectedTo) {\n    modifiers.range_start = [selectedTo];\n    modifiers.range_end = [selectedTo];\n  }\n\n  if (min) {\n    if (selectedFrom && !selectedTo) {\n      modifiers.disabled.push({\n        after: subDays(selectedFrom, min - 1),\n        before: addDays(selectedFrom, min - 1)\n      });\n    }\n    if (selectedFrom && selectedTo) {\n      modifiers.disabled.push({\n        after: selectedFrom,\n        before: addDays(selectedFrom, min - 1)\n      });\n    }\n    if (!selectedFrom && selectedTo) {\n      modifiers.disabled.push({\n        after: subDays(selectedTo, min - 1),\n        before: addDays(selectedTo, min - 1)\n      });\n    }\n  }\n  if (max) {\n    if (selectedFrom && !selectedTo) {\n      modifiers.disabled.push({\n        before: addDays(selectedFrom, -max + 1)\n      });\n      modifiers.disabled.push({\n        after: addDays(selectedFrom, max - 1)\n      });\n    }\n    if (selectedFrom && selectedTo) {\n      const selectedCount =\n        differenceInCalendarDays(selectedTo, selectedFrom) + 1;\n      const offset = max - selectedCount;\n      modifiers.disabled.push({\n        before: subDays(selectedFrom, offset)\n      });\n      modifiers.disabled.push({\n        after: addDays(selectedTo, offset)\n      });\n    }\n    if (!selectedFrom && selectedTo) {\n      modifiers.disabled.push({\n        before: addDays(selectedTo, -max + 1)\n      });\n      modifiers.disabled.push({\n        after: addDays(selectedTo, max - 1)\n      });\n    }\n  }\n\n  return (\n    <SelectRangeContext.Provider value={{ selected, onDayClick, modifiers }}>\n      {children}\n    </SelectRangeContext.Provider>\n  );\n}\n\n/**\n * Hook to access the {@link SelectRangeContextValue}.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nexport function useSelectRange(): SelectRangeContextValue {\n  const context = useContext(SelectRangeContext);\n  if (!context) {\n    throw new Error('useSelectRange must be used within a SelectRangeProvider');\n  }\n  return context;\n}\n", "import { Matcher } from 'types/Matchers';\n\n/** Normalize to array a matcher input. */\nexport function matcherToArray(\n  matcher: Matcher | Matcher[] | undefined\n): Matcher[] {\n  if (Array.isArray(matcher)) {\n    return [...matcher];\n  } else if (matcher !== undefined) {\n    return [matcher];\n  } else {\n    return [];\n  }\n}\n", "import { CustomModifiers, DayModifiers } from 'types/Modifiers';\n\nimport { matcherToArray } from './matcherToArray';\n\n/** Create CustomModifiers from dayModifiers */\nexport function getCustomModifiers(\n  dayModifiers: DayModifiers\n): CustomModifiers {\n  const customModifiers: CustomModifiers = {};\n  Object.entries(dayModifiers).forEach(([modifier, matcher]) => {\n    customModifiers[modifier] = matcherToArray(matcher);\n  });\n  return customModifiers;\n}\n", "import { CSSProperties } from 'react';\n\nimport { Matcher } from './Matchers';\n\n/** A _modifier_ represents different styles or states of a day displayed in the calendar. */\nexport type Modifier = string;\n\n/** The modifiers used by DayPicker. */\nexport type Modifiers = CustomModifiers & InternalModifiers;\n\n/** The name of the modifiers that are used internally by DayPicker. */\nexport enum InternalModifier {\n  Outside = 'outside',\n  /** Name of the modifier applied to the disabled days, using the `disabled` prop. */\n  Disabled = 'disabled',\n  /** Name of the modifier applied to the selected days using the `selected` prop). */\n  Selected = 'selected',\n  /** Name of the modifier applied to the hidden days using the `hidden` prop). */\n  Hidden = 'hidden',\n  /** Name of the modifier applied to the day specified using the `today` prop). */\n  Today = 'today',\n  /** The modifier applied to the day starting a selected range, when in range selection mode.  */\n  RangeStart = 'range_start',\n  /** The modifier applied to the day ending a selected range, when in range selection mode.  */\n  RangeEnd = 'range_end',\n  /** The modifier applied to the days between the start and the end of a selected range, when in range selection mode.  */\n  RangeMiddle = 'range_middle'\n}\n\n/** Map of matchers used for the internal modifiers. */\nexport type InternalModifiers = Record<InternalModifier, Matcher[]>;\n\n/**\n * The modifiers that are matching a day in the calendar. Use the {@link useActiveModifiers} hook to get the modifiers for a day.\n *\n * ```\n * const activeModifiers: ActiveModifiers = {\n *  selected: true,\n *  customModifier: true\n * }\n * ```\n *\n * */\nexport type ActiveModifiers = Record<Modifier, true> &\n  Partial<Record<InternalModifier, true>>;\n\n/** The style to apply to each day element matching a modifier. */\nexport type ModifiersStyles = Record<Modifier, CSSProperties> &\n  Partial<Record<InternalModifier, CSSProperties>>;\n\n/** The classnames to assign to each day element matching a modifier. */\nexport type ModifiersClassNames = Record<Modifier, string> &\n  Partial<Record<InternalModifier, string>>;\n\n/** The custom modifiers passed to the {@link DayPickerBase.modifiers}. */\nexport type DayModifiers = Record<Modifier, Matcher | Matcher[]>;\n\n/**\n * A map of matchers used as custom modifiers by DayPicker component. This is\n * the same as {@link DayModifiers]], but it accepts only array of [[Matcher}s.\n */\nexport type CustomModifiers = Record<Modifier, Matcher[]>;\n", "import { DayPickerContextValue } from 'contexts/DayPicker';\nimport { SelectMultipleContextValue } from 'contexts/SelectMultiple';\nimport { SelectRangeContextValue } from 'contexts/SelectRange';\nimport { isDayPickerMultiple } from 'types/DayPickerMultiple';\nimport { isDayPickerRange } from 'types/DayPickerRange';\nimport { InternalModifier, InternalModifiers } from 'types/Modifiers';\n\nimport { matcherToArray } from './matcherToArray';\n\nconst {\n  Selected,\n  Disabled,\n  Hidden,\n  Today,\n  RangeEnd,\n  RangeMiddle,\n  RangeStart,\n  Outside\n} = InternalModifier;\n\n/** Return the {@link InternalModifiers} from the DayPicker and select contexts. */\nexport function getInternalModifiers(\n  dayPicker: DayPickerContextValue,\n  selectMultiple: SelectMultipleContextValue,\n  selectRange: SelectRangeContextValue\n) {\n  const internalModifiers: InternalModifiers = {\n    [Selected]: matcherToArray(dayPicker.selected),\n    [Disabled]: matcherToArray(dayPicker.disabled),\n    [Hidden]: matcherToArray(dayPicker.hidden),\n    [Today]: [dayPicker.today],\n    [RangeEnd]: [],\n    [RangeMiddle]: [],\n    [RangeStart]: [],\n    [Outside]: []\n  };\n\n  if (dayPicker.fromDate) {\n    internalModifiers[Disabled].push({ before: dayPicker.fromDate });\n  }\n  if (dayPicker.toDate) {\n    internalModifiers[Disabled].push({ after: dayPicker.toDate });\n  }\n\n  if (isDayPickerMultiple(dayPicker)) {\n    internalModifiers[Disabled] = internalModifiers[Disabled].concat(\n      selectMultiple.modifiers[Disabled]\n    );\n  } else if (isDayPickerRange(dayPicker)) {\n    internalModifiers[Disabled] = internalModifiers[Disabled].concat(\n      selectRange.modifiers[Disabled]\n    );\n    internalModifiers[RangeStart] = selectRange.modifiers[RangeStart];\n    internalModifiers[RangeMiddle] = selectRange.modifiers[RangeMiddle];\n    internalModifiers[RangeEnd] = selectRange.modifiers[RangeEnd];\n  }\n  return internalModifiers;\n}\n", "import { createContext, useContext, ReactNode } from 'react';\n\nimport { useDayPicker } from 'contexts/DayPicker';\nimport { useSelectMultiple } from 'contexts/SelectMultiple';\nimport { useSelectRange } from 'contexts/SelectRange';\nimport { CustomModifiers, InternalModifiers, Modifiers } from 'types/Modifiers';\n\nimport { getCustomModifiers } from './utils/getCustomModifiers';\nimport { getInternalModifiers } from './utils/getInternalModifiers';\n\n/** The Modifiers context store the modifiers used in DayPicker. To access the value of this context, use {@link useModifiers}. */\nexport const ModifiersContext = createContext<Modifiers | undefined>(undefined);\n\nexport type ModifiersProviderProps = { children: ReactNode };\n\n/** Provide the value for the {@link ModifiersContext}. */\nexport function ModifiersProvider(props: ModifiersProviderProps): JSX.Element {\n  const dayPicker = useDayPicker();\n  const selectMultiple = useSelectMultiple();\n  const selectRange = useSelectRange();\n\n  const internalModifiers: InternalModifiers = getInternalModifiers(\n    dayPicker,\n    selectMultiple,\n    selectRange\n  );\n\n  const customModifiers: CustomModifiers = getCustomModifiers(\n    dayPicker.modifiers\n  );\n\n  const modifiers: Modifiers = {\n    ...internalModifiers,\n    ...customModifiers\n  };\n\n  return (\n    <ModifiersContext.Provider value={modifiers}>\n      {props.children}\n    </ModifiersContext.Provider>\n  );\n}\n\n/**\n * Return the modifiers used by DayPicker.\n *\n * This hook is meant to be used inside internal or custom components.\n * Requires to be wrapped into {@link ModifiersProvider}.\n *\n */\nexport function useModifiers(): Modifiers {\n  const context = useContext(ModifiersContext);\n  if (!context) {\n    throw new Error('useModifiers must be used within a ModifiersProvider');\n  }\n  return context;\n}\n", "/**\n * A value or a function that matches a specific day.\n *\n *\n * Matchers are passed to <PERSON>P<PERSON> via {@link DayPickerBase.disabled},\n * {@link DayPickerBase.hidden]] or [[DayPickerProps.selected} and are used to\n * determine if a day should get a {@link Modifier}.\n *\n * Matchers can be of different types:\n *\n * ```\n * // will always match the day\n * const booleanMatcher: Matcher = true;\n *\n *  // will match the today's date\n * const dateMatcher: Matcher = new Date();\n *\n * // will match the days in the array\n * const arrayMatcher: Matcher = [new Date(2019, 1, 2), new Date(2019, 1, 4)];\n *\n * // will match days after the 2nd of February 2019\n * const afterMatcher: DateAfter = { after: new Date(2019, 1, 2) };\n *  // will match days before the 2nd of February 2019 }\n * const beforeMatcher: DateBefore = { before: new Date(2019, 1, 2) };\n *\n * // will match Sundays\n * const dayOfWeekMatcher: DayOfWeek = {\n *  dayOfWeek: 0\n * };\n *\n * // will match the included days, except the two dates\n * const intervalMatcher: DateInterval = {\n *    after: new Date(2019, 1, 2),\n *    before: new Date(2019, 1, 5)\n * };\n *\n * // will match the included days, including the two dates\n * const rangeMatcher: DateRange = {\n *    from: new Date(2019, 1, 2),\n *    to: new Date(2019, 1, 5)\n * };\n *\n * // will match when the function return true\n * const functionMatcher: Matcher = (day: Date) => {\n *  return day.getMonth() === 2 // match when month is March\n * };\n * ```\n *\n * @see {@link isMatch}\n *\n * */\nexport type Matcher =\n  | boolean\n  | ((date: Date) => boolean)\n  | Date\n  | Date[]\n  | DateRange\n  | DateBefore\n  | DateAfter\n  | DateInterval\n  | DayOfWeek;\n\n/** A matcher to match a day falling after the specified date, with the date not included. */\nexport type DateAfter = { after: Date };\n\n/** A matcher to match a day falling before the specified date, with the date not included. */\nexport type DateBefore = { before: Date };\n\n/** A matcher to match a day falling before and/or after two dates, where the dates are not included. */\nexport type DateInterval = { before: Date; after: Date };\n\n/** A matcher to match a range of dates. The range can be open. Differently from {@link DateInterval}, the dates here are included. */\nexport type DateRange = { from: Date | undefined; to?: Date | undefined };\n\n/** A matcher to match a date being one of the specified days of the week (`0-6`, where `0` is Sunday). */\nexport type DayOfWeek = { dayOfWeek: number[] };\n\n/** Returns true if `matcher` is of type {@link DateInterval}. */\nexport function isDateInterval(matcher: unknown): matcher is DateInterval {\n  return Boolean(\n    matcher &&\n      typeof matcher === 'object' &&\n      'before' in matcher &&\n      'after' in matcher\n  );\n}\n\n/** Returns true if `value` is a {@link DateRange} type. */\nexport function isDateRange(value: unknown): value is DateRange {\n  return Boolean(value && typeof value === 'object' && 'from' in value);\n}\n\n/** Returns true if `value` is of type {@link DateAfter}. */\nexport function isDateAfterType(value: unknown): value is DateAfter {\n  return Boolean(value && typeof value === 'object' && 'after' in value);\n}\n\n/** Returns true if `value` is of type {@link DateBefore}. */\nexport function isDateBeforeType(value: unknown): value is DateBefore {\n  return Boolean(value && typeof value === 'object' && 'before' in value);\n}\n\n/** Returns true if `value` is a {@link DayOfWeek} type. */\nexport function isDayOfWeekType(value: unknown): value is DayOfWeek {\n  return Boolean(value && typeof value === 'object' && 'dayOfWeek' in value);\n}\n", "import { differenceInCalendarDays, isSameDay } from 'date-fns';\n\nimport { DateRange } from 'types/Matchers';\n\n/** Return `true` whether `date` is inside `range`. */\nexport function isDateInRange(date: Date, range: DateRange): boolean {\n  let { from, to } = range;\n  if (from && to) {\n    const isRangeInverted = differenceInCalendarDays(to, from) < 0;\n    if (isRangeInverted) {\n      [from, to] = [to, from];\n    }\n    const isInRange =\n      differenceInCalendarDays(date, from) >= 0 &&\n      differenceInCalendarDays(to, date) >= 0;\n    return isInRange;\n  }\n  if (to) {\n    return isSameDay(to, date);\n  }\n  if (from) {\n    return isSameDay(from, date);\n  }\n  return false;\n}\n", "import { differenceInCalendarDays, isAfter, isDate, isSameDay } from 'date-fns';\n\nimport {\n  isDateAfterType,\n  isDateBeforeType,\n  isDateInterval,\n  isDateRange,\n  isDayOfWeekType,\n  Matcher\n} from 'types/Matchers';\n\nimport { isDateInRange } from './isDateInRange';\n\n/** Returns true if `value` is a Date type. */\nfunction isDateType(value: unknown): value is Date {\n  return isDate(value);\n}\n\n/** Returns true if `value` is an array of valid dates. */\nfunction isArrayOfDates(value: unknown): value is Date[] {\n  return Array.isArray(value) && value.every(isDate);\n}\n\n/**\n * Returns whether a day matches against at least one of the given Matchers.\n *\n * ```\n * const day = new Date(2022, 5, 19);\n * const matcher1: DateRange = {\n *    from: new Date(2021, 12, 21),\n *    to: new Date(2021, 12, 30)\n * }\n * const matcher2: DateRange = {\n *    from: new Date(2022, 5, 1),\n *    to: new Date(2022, 5, 23)\n * }\n *\n * const isMatch(day, [matcher1, matcher2]); // true, since day is in the matcher1 range.\n * ```\n * */\nexport function isMatch(day: Date, matchers: Matcher[]): boolean {\n  return matchers.some((matcher: Matcher) => {\n    if (typeof matcher === 'boolean') {\n      return matcher;\n    }\n    if (isDateType(matcher)) {\n      return isSameDay(day, matcher);\n    }\n    if (isArrayOfDates(matcher)) {\n      return matcher.includes(day);\n    }\n    if (isDateRange(matcher)) {\n      return isDateInRange(day, matcher);\n    }\n    if (isDayOfWeekType(matcher)) {\n      return matcher.dayOfWeek.includes(day.getDay());\n    }\n    if (isDateInterval(matcher)) {\n      const diffBefore = differenceInCalendarDays(matcher.before, day);\n      const diffAfter = differenceInCalendarDays(matcher.after, day);\n      const isDayBefore = diffBefore > 0;\n      const isDayAfter = diffAfter < 0;\n      const isClosedInterval = isAfter(matcher.before, matcher.after);\n      if (isClosedInterval) {\n        return isDayAfter && isDayBefore;\n      } else {\n        return isDayBefore || isDayAfter;\n      }\n    }\n    if (isDateAfterType(matcher)) {\n      return differenceInCalendarDays(day, matcher.after) > 0;\n    }\n    if (isDateBeforeType(matcher)) {\n      return differenceInCalendarDays(matcher.before, day) > 0;\n    }\n    if (typeof matcher === 'function') {\n      return matcher(day);\n    }\n    return false;\n  });\n}\n", "import { isSameMonth } from 'date-fns';\n\nimport { ActiveModifiers, Modifiers } from 'types/Modifiers';\n\nimport { isMatch } from './isMatch';\n\n/** Return the active modifiers for the given day. */\nexport function getActiveModifiers(\n  day: Date,\n  /** The modifiers to match for the given date. */\n  modifiers: Modifiers,\n  /** The month where the day is displayed, to add the \"outside\" modifiers.  */\n  displayMonth?: Date\n): ActiveModifiers {\n  const matchedModifiers = Object.keys(modifiers).reduce(\n    (result: string[], key: string): string[] => {\n      const modifier = modifiers[key];\n      if (isMatch(day, modifier)) {\n        result.push(key);\n      }\n      return result;\n    },\n    []\n  );\n  const activeModifiers: ActiveModifiers = {};\n  matchedModifiers.forEach((modifier) => (activeModifiers[modifier] = true));\n\n  if (displayMonth && !isSameMonth(day, displayMonth)) {\n    activeModifiers.outside = true;\n  }\n\n  return activeModifiers;\n}\n", "import { addDays, endOfMonth, startOfMonth } from 'date-fns';\n\nimport { getActiveModifiers } from 'contexts/Modifiers';\nimport { Modifiers } from 'types/Modifiers';\n\n/**\n * Returns the day that should be the target of the focus when DayPicker is\n * rendered the first time.\n *\n * TODO: this function doesn't consider if the day is outside the month. We\n * implemented this check in `useDayRender` but it should probably go here. See\n * https://github.com/gpbl/react-day-picker/pull/1576\n */\nexport function getInitialFocusTarget(\n  displayMonths: Date[],\n  modifiers: Modifiers\n) {\n  const firstDayInMonth = startOfMonth(displayMonths[0]);\n  const lastDayInMonth = endOfMonth(displayMonths[displayMonths.length - 1]);\n\n  // TODO: cleanup code\n  let firstFocusableDay;\n  let today;\n  let date = firstDayInMonth;\n  while (date <= lastDayInMonth) {\n    const activeModifiers = getActiveModifiers(date, modifiers);\n    const isFocusable = !activeModifiers.disabled && !activeModifiers.hidden;\n    if (!isFocusable) {\n      date = addDays(date, 1);\n      continue;\n    }\n    if (activeModifiers.selected) {\n      return date;\n    }\n    if (activeModifiers.today && !today) {\n      today = date;\n    }\n    if (!firstFocusableDay) {\n      firstFocusableDay = date;\n    }\n    date = addDays(date, 1);\n  }\n  if (today) {\n    return today;\n  } else {\n    return firstFocusableDay;\n  }\n}\n", "import {\n  addDays,\n  addMonths,\n  addWeeks,\n  addYears,\n  endOfISOWeek,\n  endOfWeek,\n  max,\n  min,\n  startOfISOWeek,\n  startOfWeek\n} from 'date-fns';\n\nimport { DayPickerContextValue } from 'contexts/DayPicker';\nimport { getActiveModifiers } from 'contexts/Modifiers';\nimport { Modifiers } from 'types/Modifiers';\n\nexport type MoveFocusBy =\n  | 'day'\n  | 'week'\n  | 'startOfWeek'\n  | 'endOfWeek'\n  | 'month'\n  | 'year';\n\nexport type MoveFocusDirection = 'after' | 'before';\n\nexport type FocusDayPickerContext = Partial<\n  Pick<\n    DayPickerContextValue,\n    'ISOWeek' | 'weekStartsOn' | 'fromDate' | 'toDate' | 'locale'\n  >\n>;\n\nexport type FocusDayOptions = {\n  moveBy: MoveFocusBy;\n  direction: MoveFocusDirection;\n  context: FocusDayPickerContext;\n  modifiers?: Modifiers;\n  retry?: { count: number; lastFocused: Date };\n};\n\nconst MAX_RETRY = 365;\n\n/** Return the next date to be focused. */\nexport function getNextFocus(focusedDay: Date, options: FocusDayOptions): Date {\n  const {\n    moveBy,\n    direction,\n    context,\n    modifiers,\n    retry = { count: 0, lastFocused: focusedDay }\n  } = options;\n  const { weekStartsOn, fromDate, toDate, locale } = context;\n\n  const moveFns = {\n    day: addDays,\n    week: addWeeks,\n    month: addMonths,\n    year: addYears,\n    startOfWeek: (date: Date) =>\n      context.ISOWeek\n        ? startOfISOWeek(date)\n        : startOfWeek(date, { locale, weekStartsOn }),\n    endOfWeek: (date: Date) =>\n      context.ISOWeek\n        ? endOfISOWeek(date)\n        : endOfWeek(date, { locale, weekStartsOn })\n  };\n\n  let newFocusedDay = moveFns[moveBy](\n    focusedDay,\n    direction === 'after' ? 1 : -1\n  );\n\n  if (direction === 'before' && fromDate) {\n    newFocusedDay = max([fromDate, newFocusedDay]);\n  } else if (direction === 'after' && toDate) {\n    newFocusedDay = min([toDate, newFocusedDay]);\n  }\n  let isFocusable = true;\n\n  if (modifiers) {\n    const activeModifiers = getActiveModifiers(newFocusedDay, modifiers);\n    isFocusable = !activeModifiers.disabled && !activeModifiers.hidden;\n  }\n  if (isFocusable) {\n    return newFocusedDay;\n  } else {\n    if (retry.count > MAX_RETRY) {\n      return retry.lastFocused;\n    }\n    return getNextFocus(newFocusedDay, {\n      moveBy,\n      direction,\n      context,\n      modifiers,\n      retry: {\n        ...retry,\n        count: retry.count + 1\n      }\n    });\n  }\n}\n", "import { createContext, ReactNode, useContext, useState } from 'react';\n\nimport { isSameDay } from 'date-fns';\n\nimport { useDayPicker } from 'contexts/DayPicker';\n\nimport { useModifiers } from '../Modifiers';\nimport { useNavigation } from '../Navigation';\nimport { getInitialFocusTarget } from './utils/getInitialFocusTarget';\nimport {\n  getNextFocus,\n  MoveFocusBy,\n  MoveFocusDirection\n} from './utils/getNextFocus';\n\n/** Represents the value of the {@link FocusContext}. */\nexport type FocusContextValue = {\n  /** The day currently focused. */\n  focusedDay: Date | undefined;\n  /** Day that will be focused.  */\n  focusTarget: Date | undefined;\n  /** Focus a day. */\n  focus: (day: Date) => void;\n  /** Blur the focused day. */\n  blur: () => void;\n  /** Focus the day after the focused day. */\n  focusDayAfter: () => void;\n  /** Focus the day before the focused day. */\n  focusDayBefore: () => void;\n  /** Focus the day in the week before the focused day. */\n  focusWeekBefore: () => void;\n  /** Focus the day in the week after the focused day. */\n  focusWeekAfter: () => void;\n  /* Focus the day in the month before the focused day. */\n  focusMonthBefore: () => void;\n  /* Focus the day in the month after the focused day. */\n  focusMonthAfter: () => void;\n  /* Focus the day in the year before the focused day. */\n  focusYearBefore: () => void;\n  /* Focus the day in the year after the focused day. */\n  focusYearAfter: () => void;\n  /* Focus the day at the start of the week of the focused day. */\n  focusStartOfWeek: () => void;\n  /* Focus the day at the end of the week of focused day. */\n  focusEndOfWeek: () => void;\n};\n\n/**\n * The Focus context shares details about the focused day for the keyboard\n *\n * Access this context from the {@link useFocusContext} hook.\n */\nexport const FocusContext = createContext<FocusContextValue | undefined>(\n  undefined\n);\n\nexport type FocusProviderProps = { children: ReactNode };\n\n/** The provider for the {@link FocusContext}. */\nexport function FocusProvider(props: FocusProviderProps): JSX.Element {\n  const navigation = useNavigation();\n  const modifiers = useModifiers();\n\n  const [focusedDay, setFocusedDay] = useState<Date | undefined>();\n  const [lastFocused, setLastFocused] = useState<Date | undefined>();\n\n  const initialFocusTarget = getInitialFocusTarget(\n    navigation.displayMonths,\n    modifiers\n  );\n\n  // TODO: cleanup and test obscure code below\n  const focusTarget =\n    focusedDay ?? (lastFocused && navigation.isDateDisplayed(lastFocused))\n      ? lastFocused\n      : initialFocusTarget;\n\n  const blur = () => {\n    setLastFocused(focusedDay);\n    setFocusedDay(undefined);\n  };\n  const focus = (date: Date) => {\n    setFocusedDay(date);\n  };\n\n  const context = useDayPicker();\n\n  const moveFocus = (moveBy: MoveFocusBy, direction: MoveFocusDirection) => {\n    if (!focusedDay) return;\n    const nextFocused = getNextFocus(focusedDay, {\n      moveBy,\n      direction,\n      context,\n      modifiers\n    });\n    if (isSameDay(focusedDay, nextFocused)) return undefined;\n    navigation.goToDate(nextFocused, focusedDay);\n    focus(nextFocused);\n  };\n\n  const value: FocusContextValue = {\n    focusedDay,\n    focusTarget,\n    blur,\n    focus,\n    focusDayAfter: () => moveFocus('day', 'after'),\n    focusDayBefore: () => moveFocus('day', 'before'),\n    focusWeekAfter: () => moveFocus('week', 'after'),\n    focusWeekBefore: () => moveFocus('week', 'before'),\n    focusMonthBefore: () => moveFocus('month', 'before'),\n    focusMonthAfter: () => moveFocus('month', 'after'),\n    focusYearBefore: () => moveFocus('year', 'before'),\n    focusYearAfter: () => moveFocus('year', 'after'),\n    focusStartOfWeek: () => moveFocus('startOfWeek', 'before'),\n    focusEndOfWeek: () => moveFocus('endOfWeek', 'after')\n  };\n\n  return (\n    <FocusContext.Provider value={value}>\n      {props.children}\n    </FocusContext.Provider>\n  );\n}\n\n/**\n * Hook to access the {@link FocusContextValue}. Use this hook to handle the\n * focus state of the elements.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nexport function useFocusContext(): FocusContextValue {\n  const context = useContext(FocusContext);\n  if (!context) {\n    throw new Error('useFocusContext must be used within a FocusProvider');\n  }\n  return context;\n}\n", "import { getActiveModifiers, useModifiers } from 'contexts/Modifiers';\nimport { ActiveModifiers } from 'types/Modifiers';\n\n/**\n * Return the active modifiers for the specified day.\n *\n * This hook is meant to be used inside internal or custom components.\n *\n * @param day\n * @param displayMonth\n */\nexport function useActiveModifiers(\n  day: Date,\n  /**\n   * The month where the date is displayed. If not the same as `date`, the day\n   * is an \"outside day\".\n   */\n  displayMonth?: Date\n): ActiveModifiers {\n  const modifiers = useModifiers();\n  const activeModifiers = getActiveModifiers(day, modifiers, displayMonth);\n  return activeModifiers;\n}\n", "import { createContext, ReactNode, useContext } from 'react';\n\nimport { DayPickerBase } from 'types/DayPickerBase';\nimport { DayPickerSingleProps, isDayPickerSingle } from 'types/DayPickerSingle';\nimport { DayClickEventHandler } from 'types/EventHandlers';\n\n/** Represents the value of a {@link SelectSingleContext}. */\nexport interface SelectSingleContextValue {\n  /** The day that has been selected. */\n  selected: Date | undefined;\n  /** Event handler to attach to the day button to enable the single select. */\n  onDayClick?: DayClickEventHandler;\n}\n\n/**\n * The SelectSingle context shares details about the selected days when in\n * single selection mode.\n *\n * Access this context from the {@link useSelectSingle} hook.\n */\nexport const SelectSingleContext = createContext<\n  SelectSingleContextValue | undefined\n>(undefined);\n\nexport interface SelectSingleProviderProps {\n  initialProps: DayPickerBase;\n  children?: ReactNode;\n}\n\n/** Provides the values for the {@link SelectSingleProvider}. */\nexport function SelectSingleProvider(\n  props: SelectSingleProviderProps\n): JSX.Element {\n  if (!isDayPickerSingle(props.initialProps)) {\n    const emptyContextValue: SelectSingleContextValue = {\n      selected: undefined\n    };\n    return (\n      <SelectSingleContext.Provider value={emptyContextValue}>\n        {props.children}\n      </SelectSingleContext.Provider>\n    );\n  }\n  return (\n    <SelectSingleProviderInternal\n      initialProps={props.initialProps}\n      children={props.children}\n    />\n  );\n}\n\n/** @private */\nexport interface SelectSingleProviderInternal {\n  initialProps: DayPickerSingleProps;\n  children?: ReactNode;\n}\n\nexport function SelectSingleProviderInternal({\n  initialProps,\n  children\n}: SelectSingleProviderInternal): JSX.Element {\n  const onDayClick: DayClickEventHandler = (day, activeModifiers, e) => {\n    initialProps.onDayClick?.(day, activeModifiers, e);\n\n    if (activeModifiers.selected && !initialProps.required) {\n      initialProps.onSelect?.(undefined, day, activeModifiers, e);\n      return;\n    }\n    initialProps.onSelect?.(day, day, activeModifiers, e);\n  };\n\n  const contextValue: SelectSingleContextValue = {\n    selected: initialProps.selected,\n    onDayClick\n  };\n  return (\n    <SelectSingleContext.Provider value={contextValue}>\n      {children}\n    </SelectSingleContext.Provider>\n  );\n}\n\n/**\n * Hook to access the {@link SelectSingleContextValue}.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nexport function useSelectSingle(): SelectSingleContextValue {\n  const context = useContext(SelectSingleContext);\n  if (!context) {\n    throw new Error(\n      'useSelectSingle must be used within a SelectSingleProvider'\n    );\n  }\n  return context;\n}\n", "import {\n  FocusEventHandler,\n  HTMLProps,\n  KeyboardEventHandler,\n  MouseEventHandler,\n  PointerEventHandler,\n  TouchEventHandler\n} from 'react';\n\nimport { useDayPicker } from 'contexts/DayPicker';\nimport { useFocusContext } from 'contexts/Focus';\nimport { useSelectMultiple } from 'contexts/SelectMultiple';\nimport { useSelectRange } from 'contexts/SelectRange';\nimport { useSelectSingle } from 'contexts/SelectSingle';\nimport { isDayPickerMultiple } from 'types/DayPickerMultiple';\nimport { isDayPickerRange } from 'types/DayPickerRange';\nimport { isDayPickerSingle } from 'types/DayPickerSingle';\nimport { ActiveModifiers } from 'types/Modifiers';\n\nexport type EventName =\n  | 'onClick'\n  | 'onFocus'\n  | 'onBlur'\n  | 'onKeyDown'\n  | 'onKeyUp'\n  | 'onMouseEnter'\n  | 'onMouseLeave'\n  | 'onPointerEnter'\n  | 'onPointerLeave'\n  | 'onTouchCancel'\n  | 'onTouchEnd'\n  | 'onTouchMove'\n  | 'onTouchStart';\n\nexport type DayEventName =\n  | 'onDayClick'\n  | 'onDayFocus'\n  | 'onDayBlur'\n  | 'onDayKeyDown'\n  | 'onDayKeyUp'\n  | 'onDayMouseEnter'\n  | 'onDayMouseLeave'\n  | 'onDayPointerEnter'\n  | 'onDayPointerLeave'\n  | 'onDayTouchCancel'\n  | 'onDayTouchEnd'\n  | 'onDayTouchMove'\n  | 'onDayTouchStart';\n\nexport type DayEventHandlers = Pick<HTMLProps<HTMLButtonElement>, EventName>;\n\n/**\n * This hook returns details about the content to render in the day cell.\n *\n *\n * When a day cell is rendered in the table, DayPicker can either:\n *\n * - render nothing: when the day is outside the month or has matched the\n *   \"hidden\" modifier.\n * - render a button when `onDayClick` or a selection mode is set.\n * - render a non-interactive element: when no selection mode is set, the day\n *   cell shouldn’t respond to any interaction. DayPicker should render a `div`\n *   or a `span`.\n *\n * ### Usage\n *\n * Use this hook to customize the behavior of the {@link Day} component. Create a\n * new `Day` component using this hook and pass it to the `components` prop.\n * The source of {@link Day} can be a good starting point.\n *\n */\nexport function useDayEventHandlers(\n  date: Date,\n  activeModifiers: ActiveModifiers\n): DayEventHandlers {\n  const dayPicker = useDayPicker();\n  const single = useSelectSingle();\n  const multiple = useSelectMultiple();\n  const range = useSelectRange();\n  const {\n    focusDayAfter,\n    focusDayBefore,\n    focusWeekAfter,\n    focusWeekBefore,\n    blur,\n    focus,\n    focusMonthBefore,\n    focusMonthAfter,\n    focusYearBefore,\n    focusYearAfter,\n    focusStartOfWeek,\n    focusEndOfWeek\n  } = useFocusContext();\n\n  const onClick: MouseEventHandler = (e) => {\n    if (isDayPickerSingle(dayPicker)) {\n      single.onDayClick?.(date, activeModifiers, e);\n    } else if (isDayPickerMultiple(dayPicker)) {\n      multiple.onDayClick?.(date, activeModifiers, e);\n    } else if (isDayPickerRange(dayPicker)) {\n      range.onDayClick?.(date, activeModifiers, e);\n    } else {\n      dayPicker.onDayClick?.(date, activeModifiers, e);\n    }\n  };\n\n  const onFocus: FocusEventHandler = (e) => {\n    focus(date);\n    dayPicker.onDayFocus?.(date, activeModifiers, e);\n  };\n\n  const onBlur: FocusEventHandler = (e) => {\n    blur();\n    dayPicker.onDayBlur?.(date, activeModifiers, e);\n  };\n\n  const onMouseEnter: MouseEventHandler = (e) => {\n    dayPicker.onDayMouseEnter?.(date, activeModifiers, e);\n  };\n  const onMouseLeave: MouseEventHandler = (e) => {\n    dayPicker.onDayMouseLeave?.(date, activeModifiers, e);\n  };\n  const onPointerEnter: PointerEventHandler = (e) => {\n    dayPicker.onDayPointerEnter?.(date, activeModifiers, e);\n  };\n  const onPointerLeave: PointerEventHandler = (e) => {\n    dayPicker.onDayPointerLeave?.(date, activeModifiers, e);\n  };\n  const onTouchCancel: TouchEventHandler = (e) => {\n    dayPicker.onDayTouchCancel?.(date, activeModifiers, e);\n  };\n  const onTouchEnd: TouchEventHandler = (e) => {\n    dayPicker.onDayTouchEnd?.(date, activeModifiers, e);\n  };\n  const onTouchMove: TouchEventHandler = (e) => {\n    dayPicker.onDayTouchMove?.(date, activeModifiers, e);\n  };\n  const onTouchStart: TouchEventHandler = (e) => {\n    dayPicker.onDayTouchStart?.(date, activeModifiers, e);\n  };\n\n  const onKeyUp: KeyboardEventHandler = (e) => {\n    dayPicker.onDayKeyUp?.(date, activeModifiers, e);\n  };\n\n  const onKeyDown: KeyboardEventHandler = (e) => {\n    switch (e.key) {\n      case 'ArrowLeft':\n        e.preventDefault();\n        e.stopPropagation();\n        dayPicker.dir === 'rtl' ? focusDayAfter() : focusDayBefore();\n        break;\n      case 'ArrowRight':\n        e.preventDefault();\n        e.stopPropagation();\n        dayPicker.dir === 'rtl' ? focusDayBefore() : focusDayAfter();\n        break;\n      case 'ArrowDown':\n        e.preventDefault();\n        e.stopPropagation();\n        focusWeekAfter();\n        break;\n      case 'ArrowUp':\n        e.preventDefault();\n        e.stopPropagation();\n        focusWeekBefore();\n        break;\n      case 'PageUp':\n        e.preventDefault();\n        e.stopPropagation();\n        e.shiftKey ? focusYearBefore() : focusMonthBefore();\n        break;\n      case 'PageDown':\n        e.preventDefault();\n        e.stopPropagation();\n        e.shiftKey ? focusYearAfter() : focusMonthAfter();\n        break;\n      case 'Home':\n        e.preventDefault();\n        e.stopPropagation();\n        focusStartOfWeek();\n        break;\n      case 'End':\n        e.preventDefault();\n        e.stopPropagation();\n        focusEndOfWeek();\n        break;\n    }\n    dayPicker.onDayKeyDown?.(date, activeModifiers, e);\n  };\n\n  const eventHandlers: DayEventHandlers = {\n    onClick,\n    onFocus,\n    onBlur,\n    onKeyDown,\n    onKeyUp,\n    onMouseEnter,\n    onMouseLeave,\n    onPointerEnter,\n    onPointerLeave,\n    onTouchCancel,\n    onTouchEnd,\n    onTouchMove,\n    onTouchStart\n  };\n\n  return eventHandlers;\n}\n", "import { useDayPicker } from 'contexts/DayPicker';\nimport { useSelectMultiple } from 'contexts/SelectMultiple';\nimport { useSelectRange } from 'contexts/SelectRange';\nimport { useSelectSingle } from 'contexts/SelectSingle';\nimport { isDayPickerMultiple } from 'types/DayPickerMultiple';\nimport { isDayPickerRange } from 'types/DayPickerRange';\nimport { isDayPickerSingle } from 'types/DayPickerSingle';\nimport { DateRange } from 'types/Matchers';\n\nexport type SelectedDays = Date | Date[] | DateRange | undefined;\n\n/**\n * Return the current selected days when DayPicker is in selection mode. Days\n * selected by the custom selection mode are not returned.\n *\n * This hook is meant to be used inside internal or custom components.\n *\n */\nexport function useSelectedDays(): SelectedDays {\n  const dayPicker = useDayPicker();\n  const single = useSelectSingle();\n  const multiple = useSelectMultiple();\n  const range = useSelectRange();\n\n  const selectedDays = isDayPickerSingle(dayPicker)\n    ? single.selected\n    : isDayPickerMultiple(dayPicker)\n      ? multiple.selected\n      : isDayPickerRange(dayPicker)\n        ? range.selected\n        : undefined;\n\n  return selectedDays;\n}\n", "import { DayPickerContextValue } from 'contexts/DayPicker';\nimport { ActiveModifiers, InternalModifier } from 'types/Modifiers';\n\nfunction isInternalModifier(modifier: string): modifier is InternalModifier {\n  return Object.values(InternalModifier).includes(modifier as InternalModifier);\n}\n\n/**\n * Return the class names for the Day element, according to the given active\n * modifiers.\n *\n * Custom class names are set via `modifiersClassNames` or `classNames`,\n * where the first have the precedence.\n */\nexport function getDayClassNames(\n  dayPicker: Pick<DayPickerContextValue, 'modifiersClassNames' | 'classNames'>,\n  activeModifiers: ActiveModifiers\n) {\n  const classNames: string[] = [dayPicker.classNames.day];\n  Object.keys(activeModifiers).forEach((modifier) => {\n    const customClassName = dayPicker.modifiersClassNames[modifier];\n    if (customClassName) {\n      classNames.push(customClassName);\n    } else if (isInternalModifier(modifier)) {\n      const internalClassName = dayPicker.classNames[`day_${modifier}`];\n      if (internalClassName) {\n        classNames.push(internalClassName);\n      }\n    }\n  });\n  return classNames;\n}\n", "import { CSSProperties } from 'react';\n\nimport { DayPickerContextValue } from 'contexts/DayPicker';\nimport { ActiveModifiers } from 'types/Modifiers';\n\n/** Return the style for the Day element, according to the given active modifiers. */\nexport function getDayStyle(\n  dayPicker: Pick<DayPickerContextValue, 'modifiersStyles' | 'styles'>,\n  activeModifiers: ActiveModifiers\n): CSSProperties {\n  let style: CSSProperties = {\n    ...dayPicker.styles.day\n  };\n  Object.keys(activeModifiers).forEach((modifier) => {\n    style = {\n      ...style,\n      ...dayPicker.modifiersStyles?.[modifier]\n    };\n  });\n  return style;\n}\n", "import { RefObject, useEffect } from 'react';\n\nimport { isSameDay } from 'date-fns';\n\nimport { ButtonProps } from 'components/Button';\nimport { DayContent } from 'components/DayContent';\nimport { useDayPicker } from 'contexts/DayPicker';\nimport { useFocusContext } from 'contexts/Focus';\nimport { useActiveModifiers } from 'hooks/useActiveModifiers';\nimport {\n  DayEventHandlers,\n  useDayEventHandlers\n} from 'hooks/useDayEventHandlers';\nimport { SelectedDays, useSelectedDays } from 'hooks/useSelectedDays';\nimport { ActiveModifiers } from 'types/Modifiers';\nimport { StyledComponent } from 'types/Styles';\n\nimport { getDayClassNames } from './utils/getDayClassNames';\nimport { getDayStyle } from './utils/getDayStyle';\n\nexport type DayRender = {\n  /** Whether the day should be rendered a `button` instead of a `div` */\n  isButton: boolean;\n  /** Whether the day should be hidden. */\n  isHidden: boolean;\n  /** The modifiers active for the given day. */\n  activeModifiers: ActiveModifiers;\n  /** The props to apply to the button element (when `isButton` is true). */\n  buttonProps: StyledComponent &\n    Pick<ButtonProps, 'disabled' | 'aria-selected' | 'tabIndex'> &\n    DayEventHandlers;\n  /** The props to apply to the div element (when `isButton` is false). */\n  divProps: StyledComponent;\n  selectedDays: SelectedDays;\n};\n\n/**\n * Return props and data used to render the {@link Day} component.\n *\n * Use this hook when creating a component to replace the built-in `Day`\n * component.\n */\nexport function useDayRender(\n  /** The date to render. */\n  day: Date,\n  /** The month where the date is displayed (if not the same as `date`, it means it is an \"outside\" day). */\n  displayMonth: Date,\n  /** A ref to the button element that will be target of focus when rendered (if required). */\n  buttonRef: RefObject<HTMLButtonElement>\n): DayRender {\n  const dayPicker = useDayPicker();\n  const focusContext = useFocusContext();\n  const activeModifiers = useActiveModifiers(day, displayMonth);\n  const eventHandlers = useDayEventHandlers(day, activeModifiers);\n  const selectedDays = useSelectedDays();\n  const isButton = Boolean(\n    dayPicker.onDayClick || dayPicker.mode !== 'default'\n  );\n\n  // Focus the button if the day is focused according to the focus context\n  useEffect(() => {\n    if (activeModifiers.outside) return;\n    if (!focusContext.focusedDay) return;\n    if (!isButton) return;\n    if (isSameDay(focusContext.focusedDay, day)) {\n      buttonRef.current?.focus();\n    }\n  }, [\n    focusContext.focusedDay,\n    day,\n    buttonRef,\n    isButton,\n    activeModifiers.outside\n  ]);\n\n  const className = getDayClassNames(dayPicker, activeModifiers).join(' ');\n  const style = getDayStyle(dayPicker, activeModifiers);\n  const isHidden = Boolean(\n    (activeModifiers.outside && !dayPicker.showOutsideDays) ||\n      activeModifiers.hidden\n  );\n\n  const DayContentComponent = dayPicker.components?.DayContent ?? DayContent;\n  const children = (\n    <DayContentComponent\n      date={day}\n      displayMonth={displayMonth}\n      activeModifiers={activeModifiers}\n    />\n  );\n\n  const divProps = {\n    style,\n    className,\n    children,\n    role: 'gridcell'\n  };\n\n  const isFocusTarget =\n    focusContext.focusTarget &&\n    isSameDay(focusContext.focusTarget, day) &&\n    !activeModifiers.outside;\n\n  const isFocused =\n    focusContext.focusedDay && isSameDay(focusContext.focusedDay, day);\n\n  const buttonProps = {\n    ...divProps,\n    disabled: activeModifiers.disabled,\n    role: 'gridcell',\n    ['aria-selected']: activeModifiers.selected,\n    tabIndex: isFocused || isFocusTarget ? 0 : -1,\n    ...eventHandlers\n  };\n\n  const dayRender: DayRender = {\n    isButton,\n    isHidden,\n    activeModifiers: activeModifiers,\n    selectedDays,\n    buttonProps,\n    divProps\n  };\n\n  return dayRender;\n}\n", "import { useRef } from 'react';\n\nimport { useDayRender } from 'hooks/useDayRender';\n\nimport { Button } from '../Button';\n\n/** Represent the props used by the {@link Day} component. */\nexport interface DayProps {\n  /** The month where the date is displayed. */\n  displayMonth: Date;\n  /** The date to render. */\n  date: Date;\n}\n\n/**\n * The content of a day cell – as a button or span element according to its\n * modifiers.\n */\nexport function Day(props: DayProps): JSX.Element {\n  const buttonRef = useRef<HTMLButtonElement>(null);\n  const dayRender = useDayRender(props.date, props.displayMonth, buttonRef);\n\n  if (dayRender.isHidden) {\n    return <div role=\"gridcell\"></div>;\n  }\n  if (!dayRender.isButton) {\n    return <div {...dayRender.divProps} />;\n  }\n  return <Button name=\"day\" ref={buttonRef} {...dayRender.buttonProps} />;\n}\n", "import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react';\n\nimport { useDayPicker } from 'contexts/DayPicker';\n\nimport { Button } from '../Button';\n\n/**\n * The props for the {@link WeekNumber} component.\n */\nexport interface WeekNumberProps {\n  /** The number of the week. */\n  number: number;\n  /** The dates in the week. */\n  dates: Date[];\n}\n\n/**\n * Render the week number element. If `onWeekNumberClick` is passed to DayPicker, it\n * renders a button, otherwise a span element.\n */\nexport function WeekNumber(props: WeekNumberProps): JSX.Element {\n  const { number: weekNumber, dates } = props;\n  const {\n    onWeekNumberClick,\n    styles,\n    classNames,\n    locale,\n    labels: { labelWeekNumber },\n    formatters: { formatWeekNumber }\n  } = useDayPicker();\n\n  const content = formatWeekNumber(Number(weekNumber), { locale });\n\n  if (!onWeekNumberClick) {\n    return (\n      <span className={classNames.weeknumber} style={styles.weeknumber}>\n        {content}\n      </span>\n    );\n  }\n\n  const label = labelWeekNumber(Number(weekNumber), { locale });\n\n  const handleClick: MouseEventHandler = function (e) {\n    onWeekNumberClick(weekNumber, dates, e);\n  };\n\n  return (\n    <Button\n      name=\"week-number\"\n      aria-label={label}\n      className={classNames.weeknumber}\n      style={styles.weeknumber}\n      onClick={handleClick}\n    >\n      {content}\n    </Button>\n  );\n}\n", "import { getUnixTime } from 'date-fns';\n\nimport { Day } from 'components/Day';\nimport { WeekNumber } from 'components/WeekNumber';\nimport { useDayPicker } from 'contexts/DayPicker';\n\n/**\n * The props for the {@link Row} component.\n */\nexport interface RowProps {\n  /** The month where the row is displayed. */\n  displayMonth: Date;\n  /** The number of the week to render. */\n  weekNumber: number;\n  /** The days contained in the week. */\n  dates: Date[];\n}\n\n/** Render a row in the calendar, with the days and the week number. */\nexport function Row(props: RowProps): JSX.Element {\n  const { styles, classNames, showWeekNumber, components } = useDayPicker();\n\n  const DayComponent = components?.Day ?? Day;\n  const WeeknumberComponent = components?.WeekNumber ?? WeekNumber;\n\n  let weekNumberCell;\n  if (showWeekNumber) {\n    weekNumberCell = (\n      <td className={classNames.cell} style={styles.cell}>\n        <WeeknumberComponent number={props.weekNumber} dates={props.dates} />\n      </td>\n    );\n  }\n\n  return (\n    <tr className={classNames.row} style={styles.row}>\n      {weekNumberCell}\n      {props.dates.map((date) => (\n        <td\n          className={classNames.cell}\n          style={styles.cell}\n          key={getUnixTime(date)}\n          role=\"presentation\"\n        >\n          <DayComponent displayMonth={props.displayMonth} date={date} />\n        </td>\n      ))}\n    </tr>\n  );\n}\n", "import {\n  addDays,\n  differenceInCalendarDays,\n  endOfISOWeek,\n  endOfWeek,\n  getISOWeek,\n  getWeek,\n  Locale,\n  startOfISOWeek,\n  startOfWeek\n} from 'date-fns';\n\nimport { MonthWeek } from './getMonthWeeks';\n\n/** Return the weeks between two dates.  */\nexport function daysToMonthWeeks(\n  fromDate: Date,\n  toDate: Date,\n  options?: {\n    ISOWeek?: boolean;\n    locale?: Locale;\n    weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6;\n    firstWeekContainsDate?: 1 | 4;\n  }\n): MonthWeek[] {\n  const toWeek = options?.ISOWeek\n    ? endOfISOWeek(toDate)\n    : endOfWeek(toDate, options);\n  const fromWeek = options?.ISOWeek\n    ? startOfISOWeek(fromDate)\n    : startOfWeek(fromDate, options);\n\n  const nOfDays = differenceInCalendarDays(toWeek, fromWeek);\n  const days: Date[] = [];\n\n  for (let i = 0; i <= nOfDays; i++) {\n    days.push(addDays(fromWeek, i));\n  }\n\n  const weeksInMonth = days.reduce((result: MonthWeek[], date) => {\n    const weekNumber = options?.ISOWeek\n      ? getISOWeek(date)\n      : getWeek(date, options);\n\n    const existingWeek = result.find(\n      (value) => value.weekNumber === weekNumber\n    );\n    if (existingWeek) {\n      existingWeek.dates.push(date);\n      return result;\n    }\n    result.push({\n      weekNumber,\n      dates: [date]\n    });\n    return result;\n  }, []);\n\n  return weeksInMonth;\n}\n", "import {\n  addWeeks,\n  endOfMonth,\n  getWeeksInMonth,\n  Locale,\n  startOfMonth\n} from 'date-fns';\n\nimport { daysToMonthWeeks } from './daysToMonthWeeks';\n\n/** Represents a week in the month.*/\nexport type MonthWeek = {\n  /** The week number from the start of the year. */\n  weekNumber: number;\n  /** The dates in the week. */\n  dates: Date[];\n};\n\n/**\n * Return the weeks belonging to the given month, adding the \"outside days\" to\n * the first and last week.\n */\nexport function getMonthWeeks(\n  month: Date,\n  options: {\n    locale: Locale;\n    useFixedWeeks?: boolean;\n    weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6;\n    firstWeekContainsDate?: 1 | 4;\n    ISOWeek?: boolean;\n  }\n): MonthWeek[] {\n  const weeksInMonth: MonthWeek[] = daysToMonthWeeks(\n    startOfMonth(month),\n    endOfMonth(month),\n    options\n  );\n\n  if (options?.useFixedWeeks) {\n    // Add extra weeks to the month, up to 6 weeks\n    const nrOfMonthWeeks = getWeeksInMonth(month, options);\n    if (nrOfMonthWeeks < 6) {\n      const lastWeek = weeksInMonth[weeksInMonth.length - 1];\n      const lastDate = lastWeek.dates[lastWeek.dates.length - 1];\n      const toDate = addWeeks(lastDate, 6 - nrOfMonthWeeks);\n      const extraWeeks = daysToMonthWeeks(\n        addWeeks(lastDate, 1),\n        toDate,\n        options\n      );\n      weeksInMonth.push(...extraWeeks);\n    }\n  }\n  return weeksInMonth;\n}\n", "import { Footer } from 'components/Footer';\nimport { Head } from 'components/Head';\nimport { Row } from 'components/Row';\nimport { useDayPicker } from 'contexts/DayPicker';\n\nimport { getMonthWeeks } from './utils/getMonthWeeks';\n\n/** The props for the {@link Table} component. */\nexport interface TableProps {\n  /** ID of table element */\n  id?: string;\n  /** The ID of the label of the table (the same given to the Caption). */\n  ['aria-labelledby']?: string;\n  /** The month where the table is displayed. */\n  displayMonth: Date;\n}\n\n/** Render the table with the calendar. */\nexport function Table(props: TableProps): JSX.Element {\n  const {\n    locale,\n    classNames,\n    styles,\n    hideHead,\n    fixedWeeks,\n    components,\n    weekStartsOn,\n    firstWeekContainsDate,\n    ISOWeek\n  } = useDayPicker();\n\n  const weeks = getMonthWeeks(props.displayMonth, {\n    useFixedWeeks: Boolean(fixedWeeks),\n    ISOWeek,\n    locale,\n    weekStartsOn,\n    firstWeekContainsDate\n  });\n\n  const HeadComponent = components?.Head ?? Head;\n  const RowComponent = components?.Row ?? Row;\n  const FooterComponent = components?.Footer ?? Footer;\n  return (\n    <table\n      id={props.id}\n      className={classNames.table}\n      style={styles.table}\n      role=\"grid\"\n      aria-labelledby={props['aria-labelledby']}\n    >\n      {!hideHead && <HeadComponent />}\n      <tbody className={classNames.tbody} style={styles.tbody}>\n        {weeks.map((week) => (\n          <RowComponent\n            displayMonth={props.displayMonth}\n            key={week.weekNumber}\n            dates={week.dates}\n            weekNumber={week.weekNumber}\n          />\n        ))}\n      </tbody>\n      <FooterComponent displayMonth={props.displayMonth} />\n    </table>\n  );\n}\n", "/*\nThe MIT License (MIT)\n\nCopyright (c) 2018-present, React Training LLC\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/\n\n/* eslint-disable prefer-const */\n/* eslint-disable @typescript-eslint/ban-ts-comment */\n/*\n * Welcome to @reach/auto-id!\n * Let's see if we can make sense of why this hook exists and its\n * implementation.\n *\n * Some background:\n *   1. Accessibility APIs rely heavily on element IDs\n *   2. Requiring developers to put IDs on every element in Reach UI is both\n *      cumbersome and error-prone\n *   3. With a component model, we can generate IDs for them!\n *\n * Solution 1: Generate random IDs.\n *\n * This works great as long as you don't server render your app. When React (in\n * the client) tries to reuse the markup from the server, the IDs won't match\n * and React will then recreate the entire DOM tree.\n *\n * Solution 2: Increment an integer\n *\n * This sounds great. Since we're rendering the exact same tree on the server\n * and client, we can increment a counter and get a deterministic result between\n * client and server. Also, JS integers can go up to nine-quadrillion. I'm\n * pretty sure the tab will be closed before an app never needs\n * 10 quadrillion IDs!\n *\n * Problem solved, right?\n *\n * Ah, but there's a catch! React's concurrent rendering makes this approach\n * non-deterministic. While the client and server will end up with the same\n * elements in the end, depending on suspense boundaries (and possibly some user\n * input during the initial render) the incrementing integers won't always match\n * up.\n *\n * Solution 3: Don't use IDs at all on the server; patch after first render.\n *\n * What we've done here is solution 2 with some tricks. With this approach, the\n * ID returned is an empty string on the first render. This way the server and\n * client have the same markup no matter how wild the concurrent rendering may\n * have gotten.\n *\n * After the render, we patch up the components with an incremented ID. This\n * causes a double render on any components with `useId`. Shouldn't be a problem\n * since the components using this hook should be small, and we're only updating\n * the ID attribute on the DOM, nothing big is happening.\n *\n * It doesn't have to be an incremented number, though--we could do generate\n * random strings instead, but incrementing a number is probably the cheapest\n * thing we can do.\n *\n * Additionally, we only do this patchup on the very first client render ever.\n * Any calls to `useId` that happen dynamically in the client will be\n * populated immediately with a value. So, we only get the double render after\n * server hydration and never again, SO BACK OFF ALRIGHT?\n */\n\nimport { useEffect, useLayoutEffect, useState } from 'react';\n\nfunction canUseDOM() {\n  return !!(\n    typeof window !== 'undefined' &&\n    window.document &&\n    window.document.createElement\n  );\n}\n/**\n * React currently throws a warning when using useLayoutEffect on the server. To\n * get around it, we can conditionally useEffect on the server (no-op) and\n * useLayoutEffect in the browser. We occasionally need useLayoutEffect to\n * ensure we don't get a render flash for certain operations, but we may also\n * need affected components to render on the server. One example is when setting\n * a component's descendants to retrieve their index values.\n *\n * Important to note that using this hook as an escape hatch will break the\n * eslint dependency warnings unless you rename the import to `useLayoutEffect`.\n * Use sparingly only when the effect won't effect the rendered HTML to avoid\n * any server/client mismatch.\n *\n * If a useLayoutEffect is needed and the result would create a mismatch, it's\n * likely that the component in question shouldn't be rendered on the server at\n * all, so a better approach would be to lazily render those in a parent\n * component after client-side hydration.\n *\n * https://gist.github.com/gaearon/e7d97cdf38a2907924ea12e4ebdf3c85\n * https://github.com/reduxjs/react-redux/blob/master/src/utils/useIsomorphicLayoutEffect.js\n *\n * @param effect\n * @param deps\n */\nconst useIsomorphicLayoutEffect = canUseDOM() ? useLayoutEffect : useEffect;\n\nlet serverHandoffComplete = false;\nlet id = 0;\nfunction genId() {\n  return `react-day-picker-${++id}`;\n}\n\n/* eslint-disable react-hooks/rules-of-hooks */\n\n/**\n * useId\n *\n * Autogenerate IDs to facilitate WAI-ARIA and server rendering.\n *\n * Note: The returned ID will initially be `null` and will update after a\n * component mounts. Users may need to supply their own ID if they need\n * consistent values for SSR.\n *\n * @see Docs https://reach.tech/auto-id\n */\nfunction useId(idFromProps: string): string;\nfunction useId(idFromProps: number): number;\nfunction useId(idFromProps: string | number): string | number;\nfunction useId(idFromProps: string | undefined | null): string | undefined;\nfunction useId(idFromProps: number | undefined | null): number | undefined;\nfunction useId(\n  idFromProps: string | number | undefined | null\n): string | number | undefined;\nfunction useId(): string | undefined;\n\nfunction useId(providedId?: number | string | undefined | null) {\n  // TODO: Remove error flag when updating internal deps to React 18. None of\n  // our tricks will play well with concurrent rendering anyway.\n\n  // If this instance isn't part of the initial render, we don't have to do the\n  // double render/patch-up dance. We can just generate the ID and return it.\n  let initialId = providedId ?? (serverHandoffComplete ? genId() : null);\n  let [id, setId] = useState(initialId);\n\n  useIsomorphicLayoutEffect(() => {\n    if (id === null) {\n      // Patch the ID after render. We do this in `useLayoutEffect` to avoid any\n      // rendering flicker, though it'll make the first render slower (unlikely\n      // to matter, but you're welcome to measure your app and let us know if\n      // it's a problem).\n      setId(genId());\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  useEffect(() => {\n    if (serverHandoffComplete === false) {\n      // Flag all future uses of `useId` to skip the update dance. This is in\n      // `useEffect` because it goes after `useLayoutEffect`, ensuring we don't\n      // accidentally bail out of the patch-up dance prematurely.\n      serverHandoffComplete = true;\n    }\n  }, []);\n\n  return providedId ?? id ?? undefined;\n}\n\nexport { useId, canUseDOM };\n", "import { Caption } from 'components/Caption';\nimport { Table } from 'components/Table';\nimport { useDayPicker } from 'contexts/DayPicker';\nimport { useNavigation } from 'contexts/Navigation';\nimport { useId } from 'hooks/useId';\n\n/** The props for the {@link Month} component. */\nexport interface MonthProps {\n  displayIndex: number;\n  displayMonth: Date;\n}\n\n/** Render a month. */\nexport function Month(props: MonthProps) {\n  const dayPicker = useDayPicker();\n  const { dir, classNames, styles, components } = dayPicker;\n  const { displayMonths } = useNavigation();\n\n  const captionId = useId(\n    dayPicker.id ? `${dayPicker.id}-${props.displayIndex}` : undefined\n  );\n\n  const tableId = dayPicker.id\n    ? `${dayPicker.id}-grid-${props.displayIndex}`\n    : undefined;\n\n  const className = [classNames.month];\n  let style = styles.month;\n\n  let isStart = props.displayIndex === 0;\n  let isEnd = props.displayIndex === displayMonths.length - 1;\n  const isCenter = !isStart && !isEnd;\n  if (dir === 'rtl') {\n    [isEnd, isStart] = [isStart, isEnd];\n  }\n\n  if (isStart) {\n    className.push(classNames.caption_start);\n    style = { ...style, ...styles.caption_start };\n  }\n  if (isEnd) {\n    className.push(classNames.caption_end);\n    style = { ...style, ...styles.caption_end };\n  }\n  if (isCenter) {\n    className.push(classNames.caption_between);\n    style = { ...style, ...styles.caption_between };\n  }\n\n  const CaptionComponent = components?.Caption ?? Caption;\n\n  return (\n    <div key={props.displayIndex} className={className.join(' ')} style={style}>\n      <CaptionComponent\n        id={captionId}\n        displayMonth={props.displayMonth}\n        displayIndex={props.displayIndex}\n      />\n      <Table\n        id={tableId}\n        aria-labelledby={captionId}\n        displayMonth={props.displayMonth}\n      />\n    </div>\n  );\n}\n", "import { ReactNode } from 'react';\n\nimport { useDayPicker } from 'contexts/DayPicker';\n\n/** The props for the {@link Months} component. */\nexport type MonthsProps = { children: ReactNode };\n\n/**\n * Render the wrapper for the month grids.\n */\nexport function Months(props: MonthsProps): JSX.Element {\n  const { classNames, styles } = useDayPicker();\n\n  return (\n    <div className={classNames.months} style={styles.months}>\n      {props.children}\n    </div>\n  );\n}\n", "import { useEffect, useState } from 'react';\n\nimport { DayPickerProps } from 'DayPicker';\n\nimport { Month } from 'components/Month';\nimport { Months } from 'components/Months';\nimport { useDayPicker } from 'contexts/DayPicker';\nimport { useFocusContext } from 'contexts/Focus';\nimport { useNavigation } from 'contexts/Navigation';\n\nfunction isDataAttributes(attrs: DayPickerProps): attrs is {\n  [key: string]: string | boolean | number | undefined;\n} {\n  return true;\n}\n\nexport interface RootProps {\n  initialProps: DayPickerProps;\n}\n\n/** Render the container with the months according to the number of months to display. */\nexport function Root({ initialProps }: RootProps): JSX.Element {\n  const dayPicker = useDayPicker();\n  const focusContext = useFocusContext();\n  const navigation = useNavigation();\n\n  const [hasInitialFocus, setHasInitialFocus] = useState(false);\n\n  // Focus the focus target when initialFocus is passed in\n  useEffect(() => {\n    if (!dayPicker.initialFocus) return;\n    if (!focusContext.focusTarget) return;\n    if (hasInitialFocus) return;\n\n    focusContext.focus(focusContext.focusTarget);\n    setHasInitialFocus(true);\n  }, [\n    dayPicker.initialFocus,\n    hasInitialFocus,\n    focusContext.focus,\n    focusContext.focusTarget,\n    focusContext\n  ]);\n\n  // Apply classnames according to props\n  const classNames = [dayPicker.classNames.root, dayPicker.className];\n  if (dayPicker.numberOfMonths > 1) {\n    classNames.push(dayPicker.classNames.multiple_months);\n  }\n  if (dayPicker.showWeekNumber) {\n    classNames.push(dayPicker.classNames.with_weeknumber);\n  }\n\n  const style = {\n    ...dayPicker.styles.root,\n    ...dayPicker.style\n  };\n\n  const dataAttributes = Object.keys(initialProps)\n    .filter((key) => key.startsWith('data-'))\n    .reduce((attrs, key) => {\n      if (!isDataAttributes(initialProps)) return attrs;\n      return {\n        ...attrs,\n        [key]: initialProps[key]\n      };\n    }, {});\n\n  const MonthsComponent = initialProps.components?.Months ?? Months;\n\n  return (\n    <div\n      className={classNames.join(' ')}\n      style={style}\n      dir={dayPicker.dir}\n      id={dayPicker.id}\n      nonce={initialProps.nonce}\n      title={initialProps.title}\n      lang={initialProps.lang}\n      {...dataAttributes}\n    >\n      <MonthsComponent>\n        {navigation.displayMonths.map((month, i) => (\n          <Month key={i} displayIndex={i} displayMonth={month} />\n        ))}\n      </MonthsComponent>\n    </div>\n  );\n}\n", "import { ReactNode } from 'react';\n\nimport { ModifiersProvider } from 'contexts/Modifiers/ModifiersContext';\n\nimport { DayPickerProvider } from './DayPicker';\nimport { FocusProvider } from './Focus';\nimport { NavigationProvider } from './Navigation';\nimport { SelectMultipleProvider } from './SelectMultiple';\nimport { SelectRangeProvider } from './SelectRange';\nimport { SelectSingleProvider } from './SelectSingle';\nimport { DayPickerDefaultProps } from 'types/DayPickerDefault';\nimport { DayPickerSingleProps } from 'types/DayPickerSingle';\nimport { DayPickerMultipleProps } from 'types/DayPickerMultiple';\nimport { DayPickerRangeProps } from 'types/DayPickerRange';\n\ntype RootContextProps =\n  | Partial<DayPickerDefaultProps>\n  | Partial<DayPickerSingleProps>\n  | Partial<DayPickerMultipleProps>\n  | Partial<DayPickerRangeProps>;\n\n/** The props of {@link RootProvider}. */\nexport type RootContext = RootContextProps & {\n  children?: ReactNode;\n};\n\n/** Provide the value for all the context providers. */\nexport function RootProvider(props: RootContext): JSX.Element {\n  const { children, ...initialProps } = props;\n\n  return (\n    <DayPickerProvider initialProps={initialProps}>\n      <NavigationProvider>\n        <SelectSingleProvider initialProps={initialProps}>\n          <SelectMultipleProvider initialProps={initialProps}>\n            <SelectRangeProvider initialProps={initialProps}>\n              <ModifiersProvider>\n                <FocusProvider>{children}</FocusProvider>\n              </ModifiersProvider>\n            </SelectRangeProvider>\n          </SelectMultipleProvider>\n        </SelectSingleProvider>\n      </NavigationProvider>\n    </DayPickerProvider>\n  );\n}\n", "import { DayPickerDefaultProps } from 'types/DayPickerDefault';\nimport { DayPickerMultipleProps } from 'types/DayPickerMultiple';\nimport { DayPickerRangeProps } from 'types/DayPickerRange';\nimport { DayPickerSingleProps } from 'types/DayPickerSingle';\n\nimport { Root } from './components/Root';\nimport { RootProvider } from './contexts/RootProvider';\n\nexport type DayPickerProps =\n  | DayPickerDefaultProps\n  | DayPickerSingleProps\n  | DayPickerMultipleProps\n  | DayPickerRangeProps;\n\n/**\n * DayPicker render a date picker component to let users pick dates from a\n * calendar. See http://react-day-picker.js.org for updated documentation and\n * examples.\n *\n * ### Customization\n *\n * DayPicker offers different customization props. For example,\n *\n * - show multiple months using `numberOfMonths`\n * - display a dropdown to navigate the months via `captionLayout`\n * - display the week numbers with `showWeekNumbers`\n * - disable or hide days with `disabled` or `hidden`\n *\n * ### Controlling the months\n *\n * Change the initially displayed month using the `defaultMonth` prop. The\n * displayed months are controlled by DayPicker and stored in its internal\n * state. To control the months yourself, use `month` instead of `defaultMonth`\n * and use the `onMonthChange` event to set it.\n *\n * To limit the months the user can navigate to, use\n * `fromDate`/`fromMonth`/`fromYear` or `toDate`/`toMonth`/`toYear`.\n *\n * ### Selection modes\n *\n * DayPicker supports different selection mode that can be toggled using the\n * `mode` prop:\n *\n * - `mode=\"single\"`: only one day can be selected. Use `required` to make the\n *   selection required. Use the `onSelect` event handler to get the selected\n *   days.\n * - `mode=\"multiple\"`: users can select one or more days. Limit the amount of\n *   days that can be selected with the `min` or the `max` props.\n * - `mode=\"range\"`: users can select a range of days. Limit the amount of days\n *   in the range with the `min` or the `max` props.\n * - `mode=\"default\"` (default): the built-in selections are disabled. Implement\n *   your own selection mode with `onDayClick`.\n *\n * The selection modes should cover the most common use cases. In case you\n * need a more refined way of selecting days, use `mode=\"default\"`. Use the\n * `selected` props and add the day event handlers to add/remove days from the\n * selection.\n *\n * ### Modifiers\n *\n * A _modifier_ represents different styles or states for the days displayed in\n * the calendar (like \"selected\" or \"disabled\"). Define custom modifiers using\n * the `modifiers` prop.\n *\n * ### Formatters and custom component\n *\n * You can customize how the content is displayed in the date picker by using\n * either the formatters or replacing the internal components.\n *\n * For the most common cases you want to use the `formatters` prop to change how\n * the content is formatted in the calendar. Use the `components` prop to\n * replace the internal components, like the navigation icons.\n *\n * ### Styling\n *\n * DayPicker comes with a default, basic style in `react-day-picker/style` – use\n * it as template for your own style.\n *\n * If you are using CSS modules, pass the imported styles object the\n * `classNames` props.\n *\n * You can also style the elements via inline styles using the `styles` prop.\n *\n * ### Form fields\n *\n * If you need to bind the date picker to a form field, you can use the\n * `useInput` hooks for a basic behavior. See the `useInput` source as an\n * example to bind the date picker with form fields.\n *\n * ### Localization\n *\n * To localize DayPicker, import the locale from `date-fns` package and use the\n * `locale` prop.\n *\n * For example, to use Spanish locale:\n *\n * ```\n * import { es } from 'date-fns/locale';\n * <DayPicker locale={es} />\n * ```\n */\nexport function DayPicker(\n  props:\n    | DayPickerDefaultProps\n    | DayPickerSingleProps\n    | DayPickerMultipleProps\n    | DayPickerRangeProps\n): JSX.Element {\n  return (\n    <RootProvider {...props}>\n      <Root initialProps={props} />\n    </RootProvider>\n  );\n}\n", "/** @private */\nexport function isValidDate(day: Date): boolean {\n  return !isNaN(day.getTime());\n}\n", "import {\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  FocusEventHandler,\n  InputHTMLAttributes,\n  useState\n} from 'react';\n\nimport { differenceInCalendarDays, format as _format, parse } from 'date-fns';\nimport { enUS } from 'date-fns/locale';\n\nimport { parseFromToProps } from 'contexts/DayPicker/utils';\nimport { DayPickerBase } from 'types/DayPickerBase';\nimport { DayPickerSingleProps } from 'types/DayPickerSingle';\nimport {\n  DayClickEventHandler,\n  MonthChangeEventHandler\n} from 'types/EventHandlers';\n\nimport { isValidDate } from './utils/isValidDate';\n\n/** The props to attach to the input field when using {@link useInput}. */\nexport type InputProps = Pick<\n  InputHTMLAttributes<HTMLInputElement>,\n  'onBlur' | 'onChange' | 'onFocus' | 'value' | 'placeholder'\n>;\n\n/** The props to attach to the DayPicker component when using {@link useInput}. */\nexport type InputDayPickerProps = Pick<\n  DayPickerSingleProps,\n  | 'fromDate'\n  | 'toDate'\n  | 'locale'\n  | 'month'\n  | 'onDayClick'\n  | 'onMonthChange'\n  | 'selected'\n  | 'today'\n>;\n\nexport interface UseInputOptions\n  extends Pick<\n    DayPickerBase,\n    | 'locale'\n    | 'fromDate'\n    | 'toDate'\n    | 'fromMonth'\n    | 'toMonth'\n    | 'fromYear'\n    | 'toYear'\n    | 'today'\n  > {\n  /** The initially selected date */\n  defaultSelected?: Date;\n  /**\n   * The format string for formatting the input field. See\n   * https://date-fns.org/docs/format for a list of format strings.\n   *\n   * @defaultValue PP\n   */\n  format?: string;\n  /** Make the selection required. */\n  required?: boolean;\n}\n\n/** Represent the value returned by {@link useInput}. */\nexport interface UseInputValue {\n  /** The props to pass to a DayPicker component. */\n  dayPickerProps: InputDayPickerProps;\n  /** The props to pass to an input field. */\n  inputProps: InputProps;\n  /** A function to reset to the initial state. */\n  reset: () => void;\n  /** A function to set the selected day. */\n  setSelected: (day: Date | undefined) => void;\n}\n\n/** Return props and setters for binding an input field to DayPicker. */\nexport function useInput(options: UseInputOptions = {}): UseInputValue {\n  const {\n    locale = enUS,\n    required,\n    format = 'PP',\n    defaultSelected,\n    today = new Date()\n  } = options;\n  const { fromDate, toDate } = parseFromToProps(options);\n\n  // Shortcut to the DateFns functions\n  const parseValue = (value: string) => parse(value, format, today, { locale });\n\n  // Initialize states\n  const [month, setMonth] = useState(defaultSelected ?? today);\n  const [selectedDay, setSelectedDay] = useState(defaultSelected);\n  const defaultInputValue = defaultSelected\n    ? _format(defaultSelected, format, { locale })\n    : '';\n  const [inputValue, setInputValue] = useState(defaultInputValue);\n\n  const reset = () => {\n    setSelectedDay(defaultSelected);\n    setMonth(defaultSelected ?? today);\n    setInputValue(defaultInputValue ?? '');\n  };\n\n  const setSelected = (date: Date | undefined) => {\n    setSelectedDay(date);\n    setMonth(date ?? today);\n    setInputValue(date ? _format(date, format, { locale }) : '');\n  };\n\n  const handleDayClick: DayClickEventHandler = (day, { selected }) => {\n    if (!required && selected) {\n      setSelectedDay(undefined);\n      setInputValue('');\n      return;\n    }\n    setSelectedDay(day);\n    setInputValue(day ? _format(day, format, { locale }) : '');\n  };\n\n  const handleMonthChange: MonthChangeEventHandler = (month) => {\n    setMonth(month);\n  };\n\n  // When changing the input field, save its value in state and check if the\n  // string is a valid date. If it is a valid day, set it as selected and update\n  // the calendar’s month.\n  const handleChange: ChangeEventHandler<HTMLInputElement> = (e) => {\n    setInputValue(e.target.value);\n    const day = parseValue(e.target.value);\n    const isBefore = fromDate && differenceInCalendarDays(fromDate, day) > 0;\n    const isAfter = toDate && differenceInCalendarDays(day, toDate) > 0;\n    if (!isValidDate(day) || isBefore || isAfter) {\n      setSelectedDay(undefined);\n      return;\n    }\n    setSelectedDay(day);\n    setMonth(day);\n  };\n\n  // Special case for _required_ fields: on blur, if the value of the input is not\n  // a valid date, reset the calendar and the input value.\n  const handleBlur: FocusEventHandler<HTMLInputElement> = (e) => {\n    const day = parseValue(e.target.value);\n    if (!isValidDate(day)) {\n      reset();\n    }\n  };\n\n  // When focusing, make sure DayPicker visualizes the month of the date in the\n  // input field.\n  const handleFocus: FocusEventHandler<HTMLInputElement> = (e) => {\n    if (!e.target.value) {\n      reset();\n      return;\n    }\n    const day = parseValue(e.target.value);\n    if (isValidDate(day)) {\n      setMonth(day);\n    }\n  };\n\n  const dayPickerProps: InputDayPickerProps = {\n    month: month,\n    onDayClick: handleDayClick,\n    onMonthChange: handleMonthChange,\n    selected: selectedDay,\n    locale,\n    fromDate,\n    toDate,\n    today\n  };\n\n  const inputProps: InputProps = {\n    onBlur: handleBlur,\n    onChange: handleChange,\n    onFocus: handleFocus,\n    value: inputValue,\n    placeholder: _format(new Date(), format, { locale })\n  };\n\n  return { dayPickerProps, inputProps, reset, setSelected };\n}\n", "import { DayPickerProps } from 'DayPicker';\n\nimport { DayPickerBase } from './DayPickerBase';\n\n/** The props for the {@link DayPicker} component when using `mode=\"default\"` or `undefined`. */\nexport interface DayPickerDefaultProps extends DayPickerBase {\n  mode?: undefined | 'default';\n}\n\n/** Returns true when the props are of type {@link DayPickerDefaultProps}. */\nexport function isDayPickerDefault(\n  props: DayPickerProps\n): props is DayPickerDefaultProps {\n  return props.mode === undefined || props.mode === 'default';\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BO,IAAI,WAAW,WAAW;AAC7B,aAAW,OAAO,UAAU,SAASA,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;IACvF;AACQ,WAAO;EACf;AACI,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;AAEO,SAAS,OAAO,GAAG,GAAG;AACzB,MAAI,IAAI,CAAA;AACR,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,UAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAChC;AACI,SAAO;AACX;AAiKO,SAAS,cAAc,IAAI,MAAM,MAAM;AAC1C,MAAI,QAAQ,UAAU,WAAW,EAAG,UAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACjF,QAAI,MAAM,EAAE,KAAK,OAAO;AACpB,UAAI,CAAC,GAAI,MAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,SAAG,CAAC,IAAI,KAAK,CAAC;IAC1B;EACA;AACI,SAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AAC3D;ACxMM,SAAU,oBACd,OAA6C;AAE7C,SAAO,MAAM,SAAS;AACxB;ACHM,SAAU,iBACd,OAA6C;AAE7C,SAAO,MAAM,SAAS;AACxB;ACPM,SAAU,kBACd,OAA6C;AAE7C,SAAO,MAAM,SAAS;AACxB;AClBO,IAAM,oBAA0C;EACrD,MAAM;EACN,iBAAiB;EACjB,iBAAiB;EACjB,SAAS;EACT,cAAc;EACd,QAAQ;EAER,SAAS;EAET,eAAe;EACf,aAAa;EACb,iBAAiB;EACjB,eAAe;EAEf,mBAAmB;EAEnB,UAAU;EACV,gBAAgB;EAChB,eAAe;EACf,eAAe;EAEf,QAAQ;EACR,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EAEP,MAAM;EACN,UAAU;EACV,WAAW;EAEX,KAAK;EACL,YAAY;EACZ,qBAAqB;EACrB,iBAAiB;EAEjB,UAAU;EAEV,KAAK;EACL,YAAY;EACZ,MAAM;EAEN,KAAK;EACL,WAAW;EACX,aAAa;EACb,cAAc;EACd,cAAc;EACd,YAAY;EACZ,iBAAiB;EACjB,eAAe;EACf,kBAAkB;;ACnDJ,SAAA,cACd,OACA,SAA6B;AAE7B,SAAO,OAAO,OAAO,UAAU,OAAO;AACxC;ACLgB,SAAA,UAAU,KAAW,SAA6B;AAChE,SAAO,OAAO,KAAK,KAAK,OAAO;AACjC;ACFgB,SAAA,mBACd,OACA,SAA6B;AAE7B,SAAO,OAAO,OAAO,QAAQ,OAAO;AACtC;ACPM,SAAU,iBAAiB,YAAkB;AACjD,SAAO,GAAA,OAAG,UAAU;AACtB;ACAgB,SAAA,kBACd,SACA,SAA6B;AAE7B,SAAO,OAAO,SAAS,UAAU,OAAO;AAC1C;ACLgB,SAAA,kBACd,MACA,SAEC;AAED,SAAO,OAAO,MAAM,QAAQ,OAAO;AACrC;;;;;;;;;;ACLO,IAAM,WAAqB,SAAC,KAAK,iBAAiB,SAAO;AAC9D,SAAO,OAAO,KAAK,kBAAkB,OAAO;AAC9C;ACNO,IAAM,qBAAqB,WAAA;AAChC,SAAO;AACT;ACAO,IAAM,YAA4B,WAAA;AACvC,SAAO;AACT;ACFO,IAAM,gBAAgC,WAAA;AAC3C,SAAO;AACT;ACAO,IAAM,eAA6B,SAAC,KAAK,SAAO;AACrD,SAAO,OAAO,KAAK,QAAQ,OAAO;AACpC;ACJO,IAAM,kBAAmC,SAAC,GAAC;AAChD,SAAO,WAAA,OAAW,CAAC;AACrB;ACJO,IAAM,oBAAoB,WAAA;AAC/B,SAAO;AACT;;;;;;;;;;;SCyBgB,0BAAuB;AACrC,MAAM,gBAA+B;AACrC,MAAM,aAAa;AACnB,MAAM,SAAS;AACf,MAAM,sBAAsB,CAAA;AAC5B,MAAM,YAAY,CAAA;AAClB,MAAM,iBAAiB;AACvB,MAAM,SAAS,CAAA;AACf,MAAM,QAAQ,oBAAI,KAAI;AAEtB,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM;;AAEV;AChDM,SAAU,iBACd,OAGC;AAEO,MAAA,WAAyC,MAAK,UAApC,SAA+B,MAAK,QAA5B,YAAuB,MAAd,WAAE,UAAY,MAAK;AAChD,MAAA,WAAqB,MAAK,UAAhB,SAAW,MAAK;AAEhC,MAAI,WAAW;AACb,eAAW,aAAa,SAAS;aACxB,UAAU;AACnB,eAAW,IAAI,KAAK,UAAU,GAAG,CAAC;;AAEpC,MAAI,SAAS;AACX,aAAS,WAAW,OAAO;aAClB,QAAQ;AACjB,aAAS,IAAI,KAAK,QAAQ,IAAI,EAAE;;AAGlC,SAAO;IACL,UAAU,WAAW,WAAW,QAAQ,IAAI;IAC5C,QAAQ,SAAS,WAAW,MAAM,IAAI;;AAE1C;IC2Ba,uBAAmB,4BAE9B,MAAS;AAYL,SAAU,kBAAkB,OAA6B;;AACrD,MAAA,eAAiB,MAAK;AAE9B,MAAM,uBAAuB,wBAAuB;AAE9C,MAAA,KAAuB,iBAAiB,YAAY,GAAlD,WAAQ,GAAA,UAAE,SAAM,GAAA;AAExB,MAAI,iBACF,KAAA,aAAa,mBAAiB,QAAA,OAAA,SAAA,KAAA,qBAAqB;AACrD,MAAI,kBAAkB,cAAc,CAAC,YAAY,CAAC,SAAS;AAEzD,oBAAgB;;AAGlB,MAAI;AACJ,MACE,kBAAkB,YAAY,KAC9B,oBAAoB,YAAY,KAChC,iBAAiB,YAAY,GAC7B;AACA,eAAW,aAAa;;AAG1B,MAAM,QAAK,SAAA,SAAA,SAAA,CAAA,GACN,oBAAoB,GACpB,YAAY,GACf,EAAA,eACA,YAAU,SAAA,SAAA,CAAA,GACL,qBAAqB,UAAU,GAC/B,aAAa,UAAU,GAE5B,YACK,SAAA,CAAA,GAAA,aAAa,UAAU,GAE5B,YACK,SAAA,SAAA,CAAA,GAAA,qBAAqB,UAAU,GAC/B,aAAa,UAAU,GAE5B,UACA,QAAM,SAAA,SAAA,CAAA,GACD,qBAAqB,MAAM,GAC3B,aAAa,MAAM,GAExB,MAAM,aAAa,QAAQ,qBAAqB,MAChD,WAAS,SAAA,SAAA,CAAA,GACJ,qBAAqB,SAAS,GAC9B,aAAa,SAAS,GAE3B,qBACK,SAAA,SAAA,CAAA,GAAA,qBAAqB,mBAAmB,GACxC,aAAa,mBAAmB,GAErC,UACA,QAAM,SAAA,SAAA,CAAA,GACD,qBAAqB,MAAM,GAC3B,aAAa,MAAM,GAExB,OAAM,CAAA;AAGR,aACEC,wBAAC,iBAAiB,UAAS,EAAA,OAAY,UACpC,MAAM,SAAQ,CAAA;AAGrB;SAQgB,eAAY;AAC1B,MAAM,cAAU,yBAAW,gBAAgB;AAC3C,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,uDAAuD;;AAEzE,SAAO;AACT;ACxIM,SAAU,aAAa,OAAwB;AAC7C,MAAA,KAKF,aAAY,GAJd,SAAM,GAAA,QACN,aAAU,GAAA,YACV,SAAM,GAAA,QACQC,iBAAa,GAAA,WAAA;AAE7B,aACED,wBACE,OAAA,EAAA,WAAW,WAAW,eACtB,OAAO,OAAO,eAAa,aACjB,UACV,MAAK,gBACL,IAAI,MAAM,IAET,UAAAC,eAAc,MAAM,cAAc,EAAE,OAAM,CAAE,EAAC,CAAA;AAGpD;AC1BM,SAAU,aAAa,OAAsB;AACjD,aACED,wBAAA,OAAA,SAAA,EACE,OAAM,OACN,QAAO,OACP,SAAQ,eAAa,eACT,eAAc,GACtB,OAAK,EAAA,cAETA,wBACE,QAAA,EAAA,GAAE,2hBACF,MAAK,gBACL,UAAS,UAAS,CACZ,EAAA,CAAA,CAAA;AAGd;ACQM,SAAU,SAAS,OAAoB;;AACnC,MAAA,WAAyD,MAAK,UAApD,QAA+C,MAA1C,OAAE,WAAwC,MAAK,UAAnC,UAA8B,MAAvB,SAAE,YAAqB,MAAK,WAAf,QAAU,MAAK;AACtE,MAAM,YAAY,aAAY;AAE9B,MAAM,yBACJ,MAAA,KAAA,UAAU,gBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,kBAAgB,QAAA,OAAA,SAAA,KAAA;AACxC,aACEE,yBAAAA,OAAAA,EAAK,WAAsB,OACzB,UAAA,KAAAF,wBAAA,QAAA,EAAM,WAAW,UAAU,WAAW,SAAO,UAC1C,MAAM,YAAY,EAAC,CACf,OACPA,wBAAAA,UAAAA,EACE,MAAM,MAAM,MAAI,cACJ,MAAM,YAAY,GAC9B,WAAW,UAAU,WAAW,UAChC,OAAO,UAAU,OAAO,UACxB,OACA,UAAkB,SAET,CAAA,OAEXE,yBAAA,OAAA,EACE,WAAW,UAAU,WAAW,eAChC,OAAO,UAAU,OAAO,eAAa,eACzB,QAEX,UAAA,CAAA,aAECF,wBAAC,uBAAqB,EACpB,WAAW,UAAU,WAAW,eAChC,OAAO,UAAU,OAAO,cAAa,CACrC,CAAA,EAAA,CAAA,CAEA,EACF,CAAA;AAEV;ACjDM,SAAU,eAAe,OAA0B;;AACjD,MAAA,KASF,aAAY,GARd,WAAQ,GAAA,UACR,SAAM,GAAA,QACN,SAAM,GAAA,QACN,SAAM,GAAA,QACQG,sBAAkB,GAAA,WAAA,oBAChC,aAAU,GAAA,YACV,aAAU,GAAA,YACAC,sBAAkB,GAAA,OAAA;AAI9B,MAAI,CAAC;AAAU,eAAOJ,wBAAAA,6BAAAA,CAAAA,CAAAA;AACtB,MAAI,CAAC;AAAQ,eAAOA,wBAAAA,6BAAAA,CAAAA,CAAAA;AAEpB,MAAM,iBAAyB,CAAA;AAE/B,MAAI,WAAW,UAAU,MAAM,GAAG;AAEhC,QAAM,OAAO,aAAa,QAAQ;AAClC,aAAS,QAAQ,SAAS,SAAQ,GAAI,SAAS,OAAO,SAAQ,GAAI,SAAS;AACzE,qBAAe,KAAK,SAAS,MAAM,KAAK,CAAC;;SAEtC;AAEL,QAAM,OAAO,aAAa,oBAAI,KAAI,CAAE;AACpC,aAAS,QAAQ,GAAG,SAAS,IAAI,SAAS;AACxC,qBAAe,KAAK,SAAS,MAAM,KAAK,CAAC;;;AAI7C,MAAM,eAAsD,SAAC,GAAC;AAC5D,QAAM,gBAAgB,OAAO,EAAE,OAAO,KAAK;AAC3C,QAAM,WAAW,SAAS,aAAa,MAAM,YAAY,GAAG,aAAa;AACzE,UAAM,SAAS,QAAQ;EACzB;AAEA,MAAM,qBAAoB,KAAA,eAAA,QAAA,eAAA,SAAA,SAAA,WAAY,cAAY,QAAA,OAAA,SAAA,KAAA;AAElD,aACEA,wBAAC,mBAAiB,EAChB,MAAK,UACO,cAAAI,oBAAkB,GAC9B,WAAW,WAAW,gBACtB,OAAO,OAAO,gBACd,UAAU,cACV,OAAO,MAAM,aAAa,SAAQ,GAClC,SAASD,oBAAmB,MAAM,cAAc,EAAE,OAAM,CAAE,GAAC,UAE1D,eAAe,IAAI,SAAC,GAAM;AAAA,eACzBH,wBAA2B,UAAA,EAAA,OAAO,EAAE,SAAQ,GAAE,UAC3CG,oBAAmB,GAAG,EAAE,OAAM,CAAE,EAAC,GADvB,EAAE,SAAQ,CAAE;EAG1B,CAAA,EAAC,CAAA;AAGR;ACnDM,SAAU,cAAc,OAAyB;;AAC7C,MAAA,eAAiB,MAAK;AACxB,MAAA,KASF,aAAY,GARd,WAAQ,GAAA,UACR,SAAM,GAAA,QACN,SAAM,GAAA,QACN,SAAM,GAAA,QACN,aAAU,GAAA,YACV,aAAU,GAAA,YACIE,qBAAiB,GAAA,WAAA,mBACrBC,qBAAiB,GAAA,OAAA;AAG7B,MAAM,QAAgB,CAAA;AAGtB,MAAI,CAAC;AAAU,eAAON,wBAAAA,6BAAAA,CAAAA,CAAAA;AACtB,MAAI,CAAC;AAAQ,eAAOA,wBAAAA,6BAAAA,CAAAA,CAAAA;AAEpB,MAAM,WAAW,SAAS,YAAW;AACrC,MAAM,SAAS,OAAO,YAAW;AACjC,WAAS,OAAO,UAAU,QAAQ,QAAQ,QAAQ;AAChD,UAAM,KAAK,QAAQ,YAAY,oBAAI,KAAI,CAAE,GAAG,IAAI,CAAC;;AAGnD,MAAM,eAAsD,SAAC,GAAC;AAC5D,QAAM,WAAW,QACf,aAAa,YAAY,GACzB,OAAO,EAAE,OAAO,KAAK,CAAC;AAExB,UAAM,SAAS,QAAQ;EACzB;AAEA,MAAM,qBAAoB,KAAA,eAAA,QAAA,eAAA,SAAA,SAAA,WAAY,cAAY,QAAA,OAAA,SAAA,KAAA;AAElD,aACEA,wBAAC,mBAAiB,EAChB,MAAK,SACO,cAAAM,mBAAiB,GAC7B,WAAW,WAAW,eACtB,OAAO,OAAO,eACd,UAAU,cACV,OAAO,aAAa,YAAW,GAC/B,SAASD,mBAAkB,cAAc,EAAE,OAAM,CAAE,GAElD,UAAA,MAAM,IAAI,SAACE,OAAI;AAAK,eACnBP,wBAAAA,UAAAA,EAAiC,OAAOO,MAAK,YAAW,GACrD,UAAAF,mBAAkBE,OAAM,EAAE,OAAM,CAAE,EAAC,GADzBA,MAAK,YAAW,CAAE;EAGhC,CAAA,EAAC,CAAA;AAGR;AC7DgB,SAAA,mBACd,cACA,iBAA8B;AAExB,MAAA,SAAgC,uBAAS,YAAY,GAApD,oBAAiB,GAAA,CAAA,GAAE,WAAQ,GAAA,CAAA;AAElC,MAAM,QACJ,oBAAoB,SAAY,oBAAoB;AAEtD,SAAO,CAAC,OAAO,QAAQ;AACzB;AClBM,SAAU,gBAAgB,SAAuC;AAC7D,MAAA,QAA+B,QAAO,OAA/B,eAAwB,QAAO,cAAjB,QAAU,QAAO;AAC9C,MAAI,eAAe,SAAS,gBAAgB,SAAS,oBAAI,KAAI;AAErD,MAAA,SAAyC,QAAO,QAAxC,WAAiC,QAAO,UAA9B,KAAuB,QAAL,gBAAlB,iBAAiB,OAAA,SAAA,IAAC;AAG5C,MAAI,UAAU,2BAA2B,QAAQ,YAAY,IAAI,GAAG;AAClE,QAAM,SAAS,MAAM,iBAAiB;AACtC,mBAAe,UAAU,QAAQ,MAAM;;AAGzC,MAAI,YAAY,2BAA2B,cAAc,QAAQ,IAAI,GAAG;AACtE,mBAAe;;AAEjB,SAAO,aAAa,YAAY;AAClC;SCNgB,qBAAkB;AAChC,MAAM,UAAU,aAAY;AAC5B,MAAM,eAAe,gBAAgB,OAAO;AACtC,MAAA,KAAoB,mBAAmB,cAAc,QAAQ,KAAK,GAAjE,QAAK,GAAA,CAAA,GAAEC,YAAQ,GAAA,CAAA;AAEtB,MAAM,YAAY,SAAC,MAAU;;AAC3B,QAAI,QAAQ;AAAmB;AAC/B,QAAMC,SAAQ,aAAa,IAAI;AAC/B,IAAAD,UAASC,MAAK;AACd,KAAAC,MAAA,QAAQ,mBAAgB,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,SAAAD,MAAK;EAC/B;AAEA,SAAO,CAAC,OAAO,SAAS;AAC1B;ACtBgB,SAAA,iBACd,OACA,IAMC;MALC,gBAAa,GAAA,eACb,iBAAc,GAAA;AAMhB,MAAM,QAAQ,aAAa,KAAK;AAChC,MAAM,MAAM,aAAa,UAAU,OAAO,cAAc,CAAC;AACzD,MAAM,aAAa,2BAA2B,KAAK,KAAK;AACxD,MAAI,SAAS,CAAA;AAEb,WAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,QAAM,YAAY,UAAU,OAAO,CAAC;AACpC,WAAO,KAAK,SAAS;;AAGvB,MAAI;AAAe,aAAS,OAAO,QAAO;AAC1C,SAAO;AACT;AChBgB,SAAA,aACd,eACA,SAOC;AAED,MAAI,QAAQ,mBAAmB;AAC7B,WAAO;;AAED,MAAA,SAAgD,QAAO,QAA/C,kBAAwC,QAAO,iBAA9B,KAAuB,QAAL,gBAAlB,iBAAiB,OAAA,SAAA,IAAC;AACnD,MAAM,SAAS,kBAAkB,iBAAiB;AAClD,MAAM,QAAQ,aAAa,aAAa;AAExC,MAAI,CAAC,QAAQ;AACX,WAAO,UAAU,OAAO,MAAM;;AAGhC,MAAM,aAAa,2BAA2B,QAAQ,aAAa;AAEnE,MAAI,aAAa,gBAAgB;AAC/B,WAAO;;AAIT,SAAO,UAAU,OAAO,MAAM;AAChC;AC7BgB,SAAA,iBACd,eACA,SAOC;AAED,MAAI,QAAQ,mBAAmB;AAC7B,WAAO;;AAED,MAAA,WAAkD,QAAO,UAA/C,kBAAwC,QAAO,iBAA9B,KAAuB,QAAL,gBAAlB,iBAAiB,OAAA,SAAA,IAAC;AACrD,MAAM,SAAS,kBAAkB,iBAAiB;AAClD,MAAM,QAAQ,aAAa,aAAa;AACxC,MAAI,CAAC,UAAU;AACb,WAAO,UAAU,OAAO,CAAC,MAAM;;AAEjC,MAAM,aAAa,2BAA2B,OAAO,QAAQ;AAE7D,MAAI,cAAc,GAAG;AACnB,WAAO;;AAIT,SAAO,UAAU,OAAO,CAAC,MAAM;AACjC;ICTa,wBAAoB,4BAE/B,MAAS;AAGL,SAAU,mBAAmB,OAElC;AACC,MAAM,YAAY,aAAY;AACxB,MAAA,KAA4B,mBAAkB,GAA7C,eAAY,GAAA,CAAA,GAAE,YAAS,GAAA,CAAA;AAE9B,MAAM,gBAAgB,iBAAiB,cAAc,SAAS;AAC9D,MAAM,YAAY,aAAa,cAAc,SAAS;AACtD,MAAM,gBAAgB,iBAAiB,cAAc,SAAS;AAE9D,MAAM,kBAAkB,SAAC,MAAU;AACjC,WAAO,cAAc,KAAK,SAAC,cAAY;AACrC,aAAA,YAAY,MAAM,YAAY;IAA9B,CAA+B;EAEnC;AAEA,MAAM,WAAW,SAAC,MAAY,SAAc;AAC1C,QAAI,gBAAgB,IAAI,GAAG;AACzB;;AAGF,QAAI,WAAW,SAAS,MAAM,OAAO,GAAG;AACtC,gBAAU,UAAU,MAAM,IAAI,UAAU,iBAAiB,EAAE,CAAC;WACvD;AACL,gBAAU,IAAI;;EAElB;AAEA,MAAM,QAAgC;IACpC;IACA;IACA;IACA;IACA;IACA;IACA;;AAGF,aACET,wBAAC,kBAAkB,UAAS,EAAA,OAAY,UACrC,MAAM,SAAQ,CAAA;AAGrB;SAQgB,gBAAa;AAC3B,MAAM,cAAU,yBAAW,iBAAiB;AAC5C,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,wDAAwD;;AAE1E,SAAO;AACT;ACjFM,SAAU,iBAAiB,OAAmB;;AAC5C,MAAA,KAAqC,aAAY,GAA/C,aAAU,GAAA,YAAE,SAAM,GAAA,QAAE,aAAU,GAAA;AAC9B,MAAA,YAAc,cAAa,EAAE;AAErC,MAAM,oBAA6C,SAAC,UAAQ;AAC1D,cACE,UAAU,UAAU,MAAM,eAAe,CAAC,MAAM,eAAe,CAAC,CAAC;EAErE;AACA,MAAM,yBAAwB,KAAA,eAAA,QAAA,eAAA,SAAA,SAAA,WAAY,kBAAgB,QAAA,OAAA,SAAA,KAAA;AAC1D,MAAM,mBACJA,wBAAC,uBAAqB,EAAC,IAAI,MAAM,IAAI,cAAc,MAAM,aAAY,CAAA;AAEvE,aACEE,yBACE,OAAA,EAAA,WAAW,WAAW,mBACtB,OAAO,OAAO,mBAGd,UAAA,KAAAF,wBAAA,OAAA,EAAK,WAAW,WAAW,SAAU,UAAA,aAAY,CAAO,OACxDA,wBAAC,gBAAc,EACb,UAAU,mBACV,cAAc,MAAM,aAAY,CAChC,OACFA,wBAAC,eAAa,EACZ,UAAU,mBACV,cAAc,MAAM,aAAY,CAAA,CAChC,EACE,CAAA;AAEV;ACtCM,SAAU,SAAS,OAAsB;AAC7C,aACEA,wBAAA,OAAA,SAAA,EAAK,OAAM,QAAO,QAAO,QAAO,SAAQ,cAAa,GAAK,OACxD,EAAA,cAAAA,wBAAA,QAAA,EACE,GAAE,mhBACF,MAAK,gBACL,UAAS,UAAS,CACZ,EAAA,CAAA,CAAA;AAGd;ACVM,SAAU,UAAU,OAAsB;AAC9C,aACEA,wBAAK,OAAA,SAAA,EAAA,OAAM,QAAO,QAAO,QAAO,SAAQ,cAAa,GAAK,OACxD,EAAA,cAAAA,wBAAA,QAAA,EACE,GAAE,shBACF,MAAK,eAAc,CAAA,EACb,CACJ,CAAA;AAEV;ICNa,aAAS,yBACpB,SAAC,OAAO,KAAG;AACH,MAAA,KAAyB,aAAY,GAAnC,aAAU,GAAA,YAAE,SAAM,GAAA;AAE1B,MAAM,gBAAgB,CAAC,WAAW,cAAc,WAAW,MAAM;AACjE,MAAI,MAAM,WAAW;AACnB,kBAAc,KAAK,MAAM,SAAS;;AAEpC,MAAM,YAAY,cAAc,KAAK,GAAG;AAExC,MAAM,QAAa,SAAA,SAAA,CAAA,GAAA,OAAO,YAAY,GAAK,OAAO,MAAM;AACxD,MAAI,MAAM,OAAO;AACf,WAAO,OAAO,OAAO,MAAM,KAAK;;AAGlC,aACEA,wBAAAA,UAAAA,SAAAA,CAAAA,GACM,OAAK,EACT,KACA,MAAK,UACL,WACA,MAAY,CACZ,CAAA;AAEN,CAAC;ACLG,SAAU,WAAW,OAAsB;;AACzC,MAAA,KAOF,aAAY,GANd,MAAG,GAAA,KACH,SAAM,GAAA,QACN,aAAU,GAAA,YACV,SAAM,GAAA,QACN,KAAoC,GAAA,QAA1BW,iBAAa,GAAA,eAAEC,aAAS,GAAA,WAClC,aAAU,GAAA;AAGZ,MAAI,CAAC,MAAM,aAAa,CAAC,MAAM,eAAe;AAC5C,eAAOZ,wBAAAA,6BAAAA,CAAAA,CAAAA;;AAGT,MAAM,gBAAgBW,eAAc,MAAM,eAAe,EAAE,OAAM,CAAE;AACnE,MAAM,oBAAoB;IACxB,WAAW;IACX,WAAW;EACZ,EAAC,KAAK,GAAG;AAEV,MAAM,YAAYC,WAAU,MAAM,WAAW,EAAE,OAAM,CAAE;AACvD,MAAM,gBAAgB;IACpB,WAAW;IACX,WAAW;EACZ,EAAC,KAAK,GAAG;AAEV,MAAM,sBAAqB,KAAA,eAAA,QAAA,eAAA,SAAA,SAAA,WAAY,eAAa,QAAA,OAAA,SAAA,KAAA;AACpD,MAAM,qBAAoB,KAAA,eAAA,QAAA,eAAA,SAAA,SAAA,WAAY,cAAY,QAAA,OAAA,SAAA,KAAA;AAClD,aACEV,yBAAK,OAAA,EAAA,WAAW,WAAW,KAAK,OAAO,OAAO,KAC3C,UAAA,CAAA,CAAC,MAAM,oBACNF,wBAAC,QAAM,EACL,MAAK,kBAAgB,cACT,eACZ,WAAW,mBACX,OAAO,OAAO,qBACd,UAAU,CAAC,MAAM,eACjB,SAAS,MAAM,iBAAe,UAE7B,QAAQ,YACPA,wBAAC,oBAAkB,EACjB,WAAW,WAAW,UACtB,OAAO,OAAO,SAAQ,CAAA,QAGxBA,wBAAC,mBAAiB,EAChB,WAAW,WAAW,UACtB,OAAO,OAAO,SAAQ,CAAA,EAEzB,CAAA,GAGJ,CAAC,MAAM,gBACNA,wBAAC,QACC,EAAA,MAAK,cAAY,cACL,WACZ,WAAW,eACX,OAAO,OAAO,iBACd,UAAU,CAAC,MAAM,WACjB,SAAS,MAAM,aAAW,UAEzB,QAAQ,YACPA,wBAAC,mBAAiB,EAChB,WAAW,WAAW,UACtB,OAAO,OAAO,SAAQ,CAAA,QAGxBA,wBAAC,oBACC,EAAA,WAAW,WAAW,UACtB,OAAO,OAAO,SAAQ,CACtB,EACH,CAAA,CAEJ,EAAA,CAAA;AAGP;AC3FM,SAAU,kBAAkB,OAAmB;AAC3C,MAAA,iBAAmB,aAAY,EAAE;AACnC,MAAA,KACJ,cAAa,GADP,gBAAa,GAAA,eAAE,YAAS,GAAA,WAAE,YAAS,GAAA,WAAE,gBAAa,GAAA;AAG1D,MAAM,eAAe,cAAc,UAAU,SAAC,OAAK;AACjD,WAAA,YAAY,MAAM,cAAc,KAAK;EAArC,CAAsC;AAGxC,MAAM,UAAU,iBAAiB;AACjC,MAAM,SAAS,iBAAiB,cAAc,SAAS;AAEvD,MAAM,WAAW,iBAAiB,MAAM,WAAW,CAAC;AACpD,MAAM,eAAe,iBAAiB,MAAM,UAAU,CAAC;AAEvD,MAAM,sBAAyC,WAAA;AAC7C,QAAI,CAAC;AAAe;AACpB,cAAU,aAAa;EACzB;AAEA,MAAM,kBAAqC,WAAA;AACzC,QAAI,CAAC;AAAW;AAChB,cAAU,SAAS;EACrB;AAEA,aACEA,wBAAC,YAAU,EACT,cAAc,MAAM,cACpB,UACA,cACA,WACA,eACA,iBAAiB,qBACjB,aAAa,gBAAe,CAAA;AAGlC;ACpBM,SAAU,QAAQ,OAAmB;;AACnC,MAAA,KACJ,aAAY,GADN,aAAU,GAAA,YAAE,oBAAiB,GAAA,mBAAE,SAAM,GAAA,QAAE,gBAAa,GAAA,eAAE,aAAU,GAAA;AAGxE,MAAM,yBAAwB,KAAA,eAAA,QAAA,eAAA,SAAA,SAAA,WAAY,kBAAgB,QAAA,OAAA,SAAA,KAAA;AAE1D,MAAI;AACJ,MAAI,mBAAmB;AACrB,kBACEA,wBAAC,uBAAsB,EAAA,IAAI,MAAM,IAAI,cAAc,MAAM,aAAY,CAAA;aAE9D,kBAAkB,YAAY;AACvC,kBACEA,wBAAC,kBAAiB,EAAA,cAAc,MAAM,cAAc,IAAI,MAAM,GAAE,CAAA;aAEzD,kBAAkB,oBAAoB;AAC/C,kBACEE,yBACEW,6BAAA,EAAA,UAAA,KAAAb,wBAAC,kBACC,EAAA,cAAc,MAAM,cACpB,cAAc,MAAM,cACpB,IAAI,MAAM,GAAE,CAAA,OAEdA,wBAAC,mBAAiB,EAChB,cAAc,MAAM,cACpB,cAAc,MAAM,cACpB,IAAI,MAAM,GAAE,CAAA,CACZ,EACD,CAAA;SAEA;AACL,kBACEE,yBAAAW,6BAAA,EAAA,UAAA,KACEb,wBAAC,uBAAqB,EACpB,IAAI,MAAM,IACV,cAAc,MAAM,cACpB,cAAc,MAAM,aAAY,CAAA,OAElCA,wBAAC,mBAAkB,EAAA,cAAc,MAAM,cAAc,IAAI,MAAM,GAAE,CAAI,CAAA,EAAA,CAAA;;AAK3E,aACEA,wBAAK,OAAA,EAAA,WAAW,WAAW,SAAS,OAAO,OAAO,SAAO,UACtD,QAAO,CAAA;AAGd;ACpEM,SAAU,OAAO,OAAkB;AACjC,MAAA,KAIF,aAAY,GAHd,SAAM,GAAA,QACN,SAAM,GAAA,QACQ,QAAK,GAAA,WAAA;AAErB,MAAI,CAAC;AAAQ,eAAOA,wBAAAA,6BAAAA,CAAAA,CAAAA;AACpB,aACEA,wBAAAA,SAAAA,EAAO,WAAW,OAAO,OAAO,OAAO,OAAK,cAC1CA,wBACE,MAAA,EAAA,cAAAA,wBAAA,MAAA,EAAI,SAAS,GAAC,UAAG,OAAM,CAAM,EAAA,CAAA,EAC1B,CACC;AAEZ;AChBM,SAAU,YACd,QAEA,cAEA,SAAiB;AAEjB,MAAM,QAAQ,UACV,eAAe,oBAAI,KAAI,CAAE,IACzB,YAAY,oBAAI,KAAI,GAAI,EAAE,QAAQ,aAAY,CAAE;AAEpD,MAAM,OAAO,CAAA;AACb,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,QAAM,MAAM,QAAQ,OAAO,CAAC;AAC5B,SAAK,KAAK,GAAG;;AAEf,SAAO;AACT;SChBgB,UAAO;AACf,MAAA,KASF,aAAY,GARd,aAAU,GAAA,YACV,SAAM,GAAA,QACN,iBAAc,GAAA,gBACd,SAAM,GAAA,QACN,eAAY,GAAA,cACZ,UAAO,GAAA,SACOc,qBAAiB,GAAA,WAAA,mBACrBC,gBAAY,GAAA,OAAA;AAGxB,MAAM,WAAW,YAAY,QAAQ,cAAc,OAAO;AAE1D,aACEb,yBAAI,MAAA,EAAA,OAAO,OAAO,UAAU,WAAW,WAAW,UAC/C,UAAA,CAAA,sBACCF,wBAAA,MAAA,EAAI,OAAO,OAAO,WAAW,WAAW,WAAW,UAAS,CAAA,GAE7D,SAAS,IAAI,SAAC,SAAS,GAAC;AAAK,eAC5BA,wBAAA,MAAA,EAEE,OAAM,OACN,WAAW,WAAW,WACtB,OAAO,OAAO,WACF,cAAAe,cAAa,SAAS,EAAE,OAAM,CAAE,GAAC,UAE5CD,mBAAkB,SAAS,EAAE,OAAM,CAAE,EAAC,GANlC,CAAC;EAQT,CAAA,CAAC,EAAA,CAAA;AAGR;SCnCgB,OAAI;;AACZ,MAAA,KAAqC,aAAY,GAA/C,aAAU,GAAA,YAAE,SAAM,GAAA,QAAE,aAAU,GAAA;AACtC,MAAM,oBAAmB,KAAA,eAAA,QAAA,eAAA,SAAA,SAAA,WAAY,aAAW,QAAA,OAAA,SAAA,KAAA;AAChD,aACEd,wBAAO,SAAA,EAAA,OAAO,OAAO,MAAM,WAAW,WAAW,MAC/C,cAAAA,wBAAC,kBAAmB,CAAA,CAAA,EAAA,CAAA;AAG1B;ACEM,SAAU,WAAW,OAAsB;AACzC,MAAA,KAGF,aAAY,GAFd,SAAM,GAAA,QACQgB,aAAS,GAAA,WAAA;AAGzB,aAAOhB,wBAAGa,6BAAA,EAAA,UAAAG,WAAU,MAAM,MAAM,EAAE,OAAM,CAAE,EAAC,CAAA;AAC7C;ICaa,4BAAwB,4BAEnC,MAAS;AAQL,SAAU,uBACd,OAAkC;AAElC,MAAI,CAAC,oBAAoB,MAAM,YAAY,GAAG;AAC5C,QAAM,oBAAgD;MACpD,UAAU;MACV,WAAW;QACT,UAAU,CAAA;MACX;;AAEH,eACEhB,wBAAC,sBAAsB,UAAS,EAAA,OAAO,mBAAiB,UACrD,MAAM,SAAQ,CAAA;;AAIrB,aACEA,wBAAC,gCACC,EAAA,cAAc,MAAM,cACpB,UAAU,MAAM,SAAQ,CAAA;AAG9B;AAQM,SAAU,+BAA+B,IAGT;MAFpC,eAAY,GAAA,cACZ,WAAQ,GAAA;AAEA,MAAA,WAAuB,aAAY,UAAzBiB,OAAa,aAAY,KAApBC,OAAQ,aAAY;AAE3C,MAAM,aAAmC,SAAC,KAAK,iBAAiB,GAAC;;AAC/D,KAAAR,MAAA,aAAa,gBAAa,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,cAAA,KAAK,iBAAiB,CAAC;AAEjD,QAAM,gBAAgB,QACpB,gBAAgB,YAAYO,SAAO,aAAQ,QAAR,aAAA,SAAA,SAAA,SAAU,YAAWA,IAAG;AAE7D,QAAI,eAAe;AACjB;;AAGF,QAAM,gBAAgB,QACpB,CAAC,gBAAgB,YAAYC,SAAO,aAAQ,QAAR,aAAA,SAAA,SAAA,SAAU,YAAWA,IAAG;AAE9D,QAAI,eAAe;AACjB;;AAGF,QAAM,eAAe,WAAe,cAAA,CAAA,GAAA,UAAU,IAAA,IAAE,CAAA;AAEhD,QAAI,gBAAgB,UAAU;AAC5B,UAAM,QAAQ,aAAa,UAAU,SAAC,aAAW;AAC/C,eAAA,UAAU,KAAK,WAAW;MAA1B,CAA2B;AAE7B,mBAAa,OAAO,OAAO,CAAC;WACvB;AACL,mBAAa,KAAK,GAAG;;AAEvB,KAAA,KAAA,aAAa,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,cAAG,cAAc,KAAK,iBAAiB,CAAC;EAC/D;AAEA,MAAM,YAAqC;IACzC,UAAU,CAAA;;AAGZ,MAAI,UAAU;AACZ,cAAU,SAAS,KAAK,SAAC,KAAS;AAChC,UAAM,gBAAgBA,QAAO,SAAS,SAASA,OAAM;AACrD,UAAM,aAAa,SAAS,KAAK,SAAC,aAAW;AAC3C,eAAA,UAAU,aAAa,GAAG;MAA1B,CAA2B;AAE7B,aAAO,QAAQ,iBAAiB,CAAC,UAAU;IAC7C,CAAC;;AAGH,MAAM,eAAe;IACnB;IACA;IACA;;AAGF,aACElB,wBAAC,sBAAsB,UAAQ,EAAC,OAAO,cAAY,SACxC,CAAA;AAGf;SAOgB,oBAAiB;AAC/B,MAAM,cAAU,yBAAW,qBAAqB;AAChD,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MACR,gEAAgE;;AAGpE,SAAO;AACT;AC5IgB,SAAA,WACd,KACA,OAAiB;AAEX,MAAA,KAAe,SAAS,CAAA,GAAtB,OAAI,GAAA,MAAE,KAAE,GAAA;AAChB,MAAI,QAAQ,IAAI;AACd,QAAI,UAAU,IAAI,GAAG,KAAK,UAAU,MAAM,GAAG,GAAG;AAC9C,aAAO;;AAET,QAAI,UAAU,IAAI,GAAG,GAAG;AACtB,aAAO,EAAE,MAAM,IAAI,IAAI,OAAS;;AAElC,QAAI,UAAU,MAAM,GAAG,GAAG;AACxB,aAAO;;AAET,QAAI,QAAQ,MAAM,GAAG,GAAG;AACtB,aAAO,EAAE,MAAM,KAAK,GAAE;;AAExB,WAAO,EAAE,MAAM,IAAI,IAAG;;AAExB,MAAI,IAAI;AACN,QAAI,QAAQ,KAAK,EAAE,GAAG;AACpB,aAAO,EAAE,MAAM,IAAI,IAAI,IAAG;;AAE5B,WAAO,EAAE,MAAM,KAAK,GAAE;;AAExB,MAAI,MAAM;AACR,QAAI,SAAS,KAAK,IAAI,GAAG;AACvB,aAAO,EAAE,MAAM,KAAK,IAAI,KAAI;;AAE9B,WAAO,EAAE,MAAM,IAAI,IAAG;;AAExB,SAAO,EAAE,MAAM,KAAK,IAAI,OAAS;AACnC;ICDa,yBAAqB,4BAEhC,MAAS;AAQL,SAAU,oBACd,OAA+B;AAE/B,MAAI,CAAC,iBAAiB,MAAM,YAAY,GAAG;AACzC,QAAM,oBAA6C;MACjD,UAAU;MACV,WAAW;QACT,aAAa,CAAA;QACb,WAAW,CAAA;QACX,cAAc,CAAA;QACd,UAAU,CAAA;MACX;;AAEH,eACEA,wBAAC,mBAAmB,UAAS,EAAA,OAAO,mBAAiB,UAClD,MAAM,SAAQ,CAAA;;AAIrB,aACEA,wBAAC,6BACC,EAAA,cAAc,MAAM,cACpB,UAAU,MAAM,SAAQ,CAAA;AAG9B;AAQM,SAAU,4BAA4B,IAGT;MAFjC,eAAY,GAAA,cACZ,WAAQ,GAAA;AAEA,MAAA,WAAa,aAAY;AAC3B,MAAA,KAAyC,YAAY,CAAA,GAA7C,eAAY,GAAA,MAAM,aAAU,GAAA;AAC1C,MAAMiB,OAAM,aAAa;AACzB,MAAMC,OAAM,aAAa;AAEzB,MAAM,aAAmC,SAAC,KAAK,iBAAiB,GAAC;;AAC/D,KAAAR,MAAA,aAAa,gBAAa,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,cAAA,KAAK,iBAAiB,CAAC;AACjD,QAAM,WAAW,WAAW,KAAK,QAAQ;AACzC,KAAAS,MAAA,aAAa,cAAQ,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,cAAG,UAAU,KAAK,iBAAiB,CAAC;EAC3D;AAEA,MAAM,YAAkC;IACtC,aAAa,CAAA;IACb,WAAW,CAAA;IACX,cAAc,CAAA;IACd,UAAU,CAAA;;AAGZ,MAAI,cAAc;AAChB,cAAU,cAAc,CAAC,YAAY;AACrC,QAAI,CAAC,YAAY;AACf,gBAAU,YAAY,CAAC,YAAY;WAC9B;AACL,gBAAU,YAAY,CAAC,UAAU;AACjC,UAAI,CAAC,UAAU,cAAc,UAAU,GAAG;AACxC,kBAAU,eAAe;UACvB;YACE,OAAO;YACP,QAAQ;UACT;;;;aAIE,YAAY;AACrB,cAAU,cAAc,CAAC,UAAU;AACnC,cAAU,YAAY,CAAC,UAAU;;AAGnC,MAAIF,MAAK;AACP,QAAI,gBAAgB,CAAC,YAAY;AAC/B,gBAAU,SAAS,KAAK;QACtB,OAAO,QAAQ,cAAcA,OAAM,CAAC;QACpC,QAAQ,QAAQ,cAAcA,OAAM,CAAC;MACtC,CAAA;;AAEH,QAAI,gBAAgB,YAAY;AAC9B,gBAAU,SAAS,KAAK;QACtB,OAAO;QACP,QAAQ,QAAQ,cAAcA,OAAM,CAAC;MACtC,CAAA;;AAEH,QAAI,CAAC,gBAAgB,YAAY;AAC/B,gBAAU,SAAS,KAAK;QACtB,OAAO,QAAQ,YAAYA,OAAM,CAAC;QAClC,QAAQ,QAAQ,YAAYA,OAAM,CAAC;MACpC,CAAA;;;AAGL,MAAIC,MAAK;AACP,QAAI,gBAAgB,CAAC,YAAY;AAC/B,gBAAU,SAAS,KAAK;QACtB,QAAQ,QAAQ,cAAc,CAACA,OAAM,CAAC;MACvC,CAAA;AACD,gBAAU,SAAS,KAAK;QACtB,OAAO,QAAQ,cAAcA,OAAM,CAAC;MACrC,CAAA;;AAEH,QAAI,gBAAgB,YAAY;AAC9B,UAAM,gBACJ,yBAAyB,YAAY,YAAY,IAAI;AACvD,UAAM,SAASA,OAAM;AACrB,gBAAU,SAAS,KAAK;QACtB,QAAQ,QAAQ,cAAc,MAAM;MACrC,CAAA;AACD,gBAAU,SAAS,KAAK;QACtB,OAAO,QAAQ,YAAY,MAAM;MAClC,CAAA;;AAEH,QAAI,CAAC,gBAAgB,YAAY;AAC/B,gBAAU,SAAS,KAAK;QACtB,QAAQ,QAAQ,YAAY,CAACA,OAAM,CAAC;MACrC,CAAA;AACD,gBAAU,SAAS,KAAK;QACtB,OAAO,QAAQ,YAAYA,OAAM,CAAC;MACnC,CAAA;;;AAIL,aACElB,wBAAC,mBAAmB,UAAS,EAAA,OAAO,EAAE,UAAU,YAAY,UAAS,GAClE,SAAQ,CACmB;AAElC;SAOgB,iBAAc;AAC5B,MAAM,cAAU,yBAAW,kBAAkB;AAC7C,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,0DAA0D;;AAE5E,SAAO;AACT;AChMM,SAAU,eACd,SAAwC;AAExC,MAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,WAAA,cAAA,CAAA,GAAW,SAAS,IAAA;aACX,YAAY,QAAW;AAChC,WAAO,CAAC,OAAO;SACV;AACL,WAAO,CAAA;;AAEX;ACRM,SAAU,mBACd,cAA0B;AAE1B,MAAM,kBAAmC,CAAA;AACzC,SAAO,QAAQ,YAAY,EAAE,QAAQ,SAAC,IAAmB;QAAlB,WAAQ,GAAA,CAAA,GAAE,UAAO,GAAA,CAAA;AACtD,oBAAgB,QAAQ,IAAI,eAAe,OAAO;EACpD,CAAC;AACD,SAAO;AACT;ICFY;CAAZ,SAAYoB,mBAAgB;AAC1B,EAAAA,kBAAA,SAAA,IAAA;AAEA,EAAAA,kBAAA,UAAA,IAAA;AAEA,EAAAA,kBAAA,UAAA,IAAA;AAEA,EAAAA,kBAAA,QAAA,IAAA;AAEA,EAAAA,kBAAA,OAAA,IAAA;AAEA,EAAAA,kBAAA,YAAA,IAAA;AAEA,EAAAA,kBAAA,UAAA,IAAA;AAEA,EAAAA,kBAAA,aAAA,IAAA;AACF,GAhBY,qBAAA,mBAgBX,CAAA,EAAA;ACjBC,IAAA,WAQE,iBARM;AAAR,IACA,WAOE,iBAPM;AADR,IAEA,SAME,iBAAgB;AARlB,IAGA,QAKE,iBAAgB;AARlB,IAIA,WAIE,iBAAgB;AARlB,IAKA,cAGE,iBAHS;AALX,IAMA,aAEE,iBAFQ;AANV,IAOA,UACE,iBAAgB;SAGJ,qBACd,WACA,gBACA,aAAoC;;AAEpC,MAAM,qBAAiB,KAAA,CAAA,GACrB,GAAC,QAAQ,IAAG,eAAe,UAAU,QAAQ,GAC7C,GAAC,QAAQ,IAAG,eAAe,UAAU,QAAQ,GAC7C,GAAC,MAAM,IAAG,eAAe,UAAU,MAAM,GACzC,GAAC,KAAK,IAAG,CAAC,UAAU,KAAK,GACzB,GAAC,QAAQ,IAAG,CAAA,GACZ,GAAC,WAAW,IAAG,CAAA,GACf,GAAC,UAAU,IAAG,CAAA,GACd,GAAC,OAAO,IAAG,CAAA;AAGb,MAAI,UAAU,UAAU;AACtB,sBAAkB,QAAQ,EAAE,KAAK,EAAE,QAAQ,UAAU,SAAQ,CAAE;;AAEjE,MAAI,UAAU,QAAQ;AACpB,sBAAkB,QAAQ,EAAE,KAAK,EAAE,OAAO,UAAU,OAAM,CAAE;;AAG9D,MAAI,oBAAoB,SAAS,GAAG;AAClC,sBAAkB,QAAQ,IAAI,kBAAkB,QAAQ,EAAE,OACxD,eAAe,UAAU,QAAQ,CAAC;aAE3B,iBAAiB,SAAS,GAAG;AACtC,sBAAkB,QAAQ,IAAI,kBAAkB,QAAQ,EAAE,OACxD,YAAY,UAAU,QAAQ,CAAC;AAEjC,sBAAkB,UAAU,IAAI,YAAY,UAAU,UAAU;AAChE,sBAAkB,WAAW,IAAI,YAAY,UAAU,WAAW;AAClE,sBAAkB,QAAQ,IAAI,YAAY,UAAU,QAAQ;;AAE9D,SAAO;AACT;AC9CO,IAAM,uBAAmB,4BAAqC,MAAS;AAKxE,SAAU,kBAAkB,OAA6B;AAC7D,MAAM,YAAY,aAAY;AAC9B,MAAM,iBAAiB,kBAAiB;AACxC,MAAM,cAAc,eAAc;AAElC,MAAM,oBAAuC,qBAC3C,WACA,gBACA,WAAW;AAGb,MAAM,kBAAmC,mBACvC,UAAU,SAAS;AAGrB,MAAM,YACD,SAAA,SAAA,CAAA,GAAA,iBAAiB,GACjB,eAAe;AAGpB,aACEpB,wBAAC,iBAAiB,UAAS,EAAA,OAAO,WAAS,UACxC,MAAM,SAAQ,CAAA;AAGrB;SASgB,eAAY;AAC1B,MAAM,cAAU,yBAAW,gBAAgB;AAC3C,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,sDAAsD;;AAExE,SAAO;AACT;ACsBM,SAAU,eAAe,SAAgB;AAC7C,SAAO,QACL,WACE,OAAO,YAAY,YACnB,YAAY,WACZ,WAAW,OAAO;AAExB;AAGM,SAAU,YAAY,OAAc;AACxC,SAAO,QAAQ,SAAS,OAAO,UAAU,YAAY,UAAU,KAAK;AACtE;AAGM,SAAU,gBAAgB,OAAc;AAC5C,SAAO,QAAQ,SAAS,OAAO,UAAU,YAAY,WAAW,KAAK;AACvE;AAGM,SAAU,iBAAiB,OAAc;AAC7C,SAAO,QAAQ,SAAS,OAAO,UAAU,YAAY,YAAY,KAAK;AACxE;AAGM,SAAU,gBAAgB,OAAc;AAC5C,SAAO,QAAQ,SAAS,OAAO,UAAU,YAAY,eAAe,KAAK;AAC3E;ACpGgB,SAAA,cAAc,MAAY,OAAgB;;AAClD,MAAA,OAAa,MAAK,MAAZ,KAAO,MAAK;AACxB,MAAI,QAAQ,IAAI;AACd,QAAM,kBAAkB,yBAAyB,IAAI,IAAI,IAAI;AAC7D,QAAI,iBAAiB;AACnB,WAAa,CAAC,IAAI,IAAI,GAArB,OAAI,GAAA,CAAA,GAAE,KAAE,GAAA,CAAA;;AAEX,QAAM,YACJ,yBAAyB,MAAM,IAAI,KAAK,KACxC,yBAAyB,IAAI,IAAI,KAAK;AACxC,WAAO;;AAET,MAAI,IAAI;AACN,WAAO,UAAU,IAAI,IAAI;;AAE3B,MAAI,MAAM;AACR,WAAO,UAAU,MAAM,IAAI;;AAE7B,SAAO;AACT;ACVA,SAAS,WAAW,OAAc;AAChC,SAAO,OAAO,KAAK;AACrB;AAGA,SAAS,eAAe,OAAc;AACpC,SAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,MAAM,MAAM;AACnD;AAmBgB,SAAA,QAAQ,KAAW,UAAmB;AACpD,SAAO,SAAS,KAAK,SAAC,SAAgB;AACpC,QAAI,OAAO,YAAY,WAAW;AAChC,aAAO;;AAET,QAAI,WAAW,OAAO,GAAG;AACvB,aAAO,UAAU,KAAK,OAAO;;AAE/B,QAAI,eAAe,OAAO,GAAG;AAC3B,aAAO,QAAQ,SAAS,GAAG;;AAE7B,QAAI,YAAY,OAAO,GAAG;AACxB,aAAO,cAAc,KAAK,OAAO;;AAEnC,QAAI,gBAAgB,OAAO,GAAG;AAC5B,aAAO,QAAQ,UAAU,SAAS,IAAI,OAAM,CAAE;;AAEhD,QAAI,eAAe,OAAO,GAAG;AAC3B,UAAM,aAAa,yBAAyB,QAAQ,QAAQ,GAAG;AAC/D,UAAM,YAAY,yBAAyB,QAAQ,OAAO,GAAG;AAC7D,UAAM,cAAc,aAAa;AACjC,UAAM,aAAa,YAAY;AAC/B,UAAM,mBAAmB,QAAQ,QAAQ,QAAQ,QAAQ,KAAK;AAC9D,UAAI,kBAAkB;AACpB,eAAO,cAAc;aAChB;AACL,eAAO,eAAe;;;AAG1B,QAAI,gBAAgB,OAAO,GAAG;AAC5B,aAAO,yBAAyB,KAAK,QAAQ,KAAK,IAAI;;AAExD,QAAI,iBAAiB,OAAO,GAAG;AAC7B,aAAO,yBAAyB,QAAQ,QAAQ,GAAG,IAAI;;AAEzD,QAAI,OAAO,YAAY,YAAY;AACjC,aAAO,QAAQ,GAAG;;AAEpB,WAAO;EACT,CAAC;AACH;ACzEM,SAAU,mBACd,KAEA,WAEA,cAAmB;AAEnB,MAAM,mBAAmB,OAAO,KAAK,SAAS,EAAE,OAC9C,SAAC,QAAkB,KAAW;AAC5B,QAAM,WAAW,UAAU,GAAG;AAC9B,QAAI,QAAQ,KAAK,QAAQ,GAAG;AAC1B,aAAO,KAAK,GAAG;;AAEjB,WAAO;KAET,CAAA,CAAE;AAEJ,MAAM,kBAAmC,CAAA;AACzC,mBAAiB,QAAQ,SAAC,UAAQ;AAAK,WAAC,gBAAgB,QAAQ,IAAI;EAA7B,CAAkC;AAEzE,MAAI,gBAAgB,CAAC,YAAY,KAAK,YAAY,GAAG;AACnD,oBAAgB,UAAU;;AAG5B,SAAO;AACT;ACnBgB,SAAA,sBACd,eACA,WAAoB;AAEpB,MAAM,kBAAkB,aAAa,cAAc,CAAC,CAAC;AACrD,MAAM,iBAAiB,WAAW,cAAc,cAAc,SAAS,CAAC,CAAC;AAGzE,MAAI;AACJ,MAAI;AACJ,MAAI,OAAO;AACX,SAAO,QAAQ,gBAAgB;AAC7B,QAAM,kBAAkB,mBAAmB,MAAM,SAAS;AAC1D,QAAM,cAAc,CAAC,gBAAgB,YAAY,CAAC,gBAAgB;AAClE,QAAI,CAAC,aAAa;AAChB,aAAO,QAAQ,MAAM,CAAC;AACtB;;AAEF,QAAI,gBAAgB,UAAU;AAC5B,aAAO;;AAET,QAAI,gBAAgB,SAAS,CAAC,OAAO;AACnC,cAAQ;;AAEV,QAAI,CAAC,mBAAmB;AACtB,0BAAoB;;AAEtB,WAAO,QAAQ,MAAM,CAAC;;AAExB,MAAI,OAAO;AACT,WAAO;SACF;AACL,WAAO;;AAEX;ACLA,IAAM,YAAY;AAGF,SAAA,aAAa,YAAkB,SAAwB;AAEnE,MAAA,SAKE,QALI,QACN,YAIE,QAJO,WACT,UAGE,QAHK,SACP,YAEE,QAFO,WACT,KACE,QAAO,OADT,QAAK,OAAA,SAAG,EAAE,OAAO,GAAG,aAAa,WAAU,IAAE;AAEvC,MAAA,eAA2C,QAAO,cAApC,WAA6B,QAAO,UAA1B,SAAmB,QAAb,QAAE,SAAW,QAAO;AAE1D,MAAM,UAAU;IACd,KAAK;IACL,MAAM;IACN,OAAO;IACP,MAAM;IACN,aAAa,SAAC,MAAU;AACtB,aAAA,QAAQ,UACJ,eAAe,IAAI,IACnB,YAAY,MAAM,EAAE,QAAQ,aAAY,CAAE;;IAChD,WAAW,SAAC,MAAU;AACpB,aAAA,QAAQ,UACJ,aAAa,IAAI,IACjB,UAAU,MAAM,EAAE,QAAQ,aAAY,CAAE;;;AAGhD,MAAI,gBAAgB,QAAQ,MAAM,EAChC,YACA,cAAc,UAAU,IAAI,EAAE;AAGhC,MAAI,cAAc,YAAY,UAAU;AACtC,oBAAgB,IAAI,CAAC,UAAU,aAAa,CAAC;aACpC,cAAc,WAAW,QAAQ;AAC1C,oBAAgB,IAAI,CAAC,QAAQ,aAAa,CAAC;;AAE7C,MAAI,cAAc;AAElB,MAAI,WAAW;AACb,QAAM,kBAAkB,mBAAmB,eAAe,SAAS;AACnE,kBAAc,CAAC,gBAAgB,YAAY,CAAC,gBAAgB;;AAE9D,MAAI,aAAa;AACf,WAAO;SACF;AACL,QAAI,MAAM,QAAQ,WAAW;AAC3B,aAAO,MAAM;;AAEf,WAAO,aAAa,eAAe;MACjC;MACA;MACA;MACA;MACA,OAAK,SAAA,SAAA,CAAA,GACA,KAAK,GAAA,EACR,OAAO,MAAM,QAAQ,EAAC,CACvB;IACF,CAAA;;AAEL;ICnDa,mBAAe,4BAC1B,MAAS;AAML,SAAU,cAAc,OAAyB;AACrD,MAAM,aAAa,cAAa;AAChC,MAAM,YAAY,aAAY;AAExB,MAAA,SAA8B,uBAAQ,GAArC,aAAU,GAAA,CAAA,GAAE,gBAAa,GAAA,CAAA;AAC1B,MAAA,SAAgC,uBAAQ,GAAvC,cAAW,GAAA,CAAA,GAAE,iBAAc,GAAA,CAAA;AAElC,MAAM,qBAAqB,sBACzB,WAAW,eACX,SAAS;AAIX,MAAM,eACJ,eAAA,QAAA,eAAU,SAAV,aAAe,eAAe,WAAW,gBAAgB,WAAW,KAChE,cACA;AAEN,MAAM,OAAO,WAAA;AACX,mBAAe,UAAU;AACzB,kBAAc,MAAS;EACzB;AACA,MAAM,QAAQ,SAAC,MAAU;AACvB,kBAAc,IAAI;EACpB;AAEA,MAAM,UAAU,aAAY;AAE5B,MAAM,YAAY,SAAC,QAAqB,WAA6B;AACnE,QAAI,CAAC;AAAY;AACjB,QAAM,cAAc,aAAa,YAAY;MAC3C;MACA;MACA;MACA;IACD,CAAA;AACD,QAAI,UAAU,YAAY,WAAW;AAAG,aAAO;AAC/C,eAAW,SAAS,aAAa,UAAU;AAC3C,UAAM,WAAW;EACnB;AAEA,MAAM,QAA2B;IAC/B;IACA;IACA;IACA;IACA,eAAe,WAAM;AAAA,aAAA,UAAU,OAAO,OAAO;IAAC;IAC9C,gBAAgB,WAAM;AAAA,aAAA,UAAU,OAAO,QAAQ;IAAC;IAChD,gBAAgB,WAAM;AAAA,aAAA,UAAU,QAAQ,OAAO;IAAC;IAChD,iBAAiB,WAAM;AAAA,aAAA,UAAU,QAAQ,QAAQ;IAAC;IAClD,kBAAkB,WAAM;AAAA,aAAA,UAAU,SAAS,QAAQ;IAAC;IACpD,iBAAiB,WAAM;AAAA,aAAA,UAAU,SAAS,OAAO;IAAC;IAClD,iBAAiB,WAAM;AAAA,aAAA,UAAU,QAAQ,QAAQ;IAAC;IAClD,gBAAgB,WAAM;AAAA,aAAA,UAAU,QAAQ,OAAO;IAAC;IAChD,kBAAkB,WAAM;AAAA,aAAA,UAAU,eAAe,QAAQ;IAAC;IAC1D,gBAAgB,WAAM;AAAA,aAAA,UAAU,aAAa,OAAO;IAAC;;AAGvD,aACEA,wBAAC,aAAa,UAAS,EAAA,OAAY,UAChC,MAAM,SAAQ,CAAA;AAGrB;SAQgB,kBAAe;AAC7B,MAAM,cAAU,yBAAW,YAAY;AACvC,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,qDAAqD;;AAEvE,SAAO;AACT;AC7HM,SAAU,mBACd,KAKA,cAAmB;AAEnB,MAAM,YAAY,aAAY;AAC9B,MAAM,kBAAkB,mBAAmB,KAAK,WAAW,YAAY;AACvE,SAAO;AACT;ICFa,0BAAsB,4BAEjC,MAAS;AAQL,SAAU,qBACd,OAAgC;AAEhC,MAAI,CAAC,kBAAkB,MAAM,YAAY,GAAG;AAC1C,QAAM,oBAA8C;MAClD,UAAU;;AAEZ,eACEA,wBAAC,oBAAoB,UAAS,EAAA,OAAO,mBAAiB,UACnD,MAAM,SAAQ,CAAA;;AAIrB,aACEA,wBAAC,8BACC,EAAA,cAAc,MAAM,cACpB,UAAU,MAAM,SAAQ,CAAA;AAG9B;AAQM,SAAU,6BAA6B,IAGd;MAF7B,eAAY,GAAA,cACZ,WAAQ,GAAA;AAER,MAAM,aAAmC,SAAC,KAAK,iBAAiB,GAAC;;AAC/D,KAAAU,MAAA,aAAa,gBAAa,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,cAAA,KAAK,iBAAiB,CAAC;AAEjD,QAAI,gBAAgB,YAAY,CAAC,aAAa,UAAU;AACtD,OAAA,KAAA,aAAa,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,cAAG,QAAW,KAAK,iBAAiB,CAAC;AAC1D;;AAEF,KAAA,KAAA,aAAa,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,cAAG,KAAK,KAAK,iBAAiB,CAAC;EACtD;AAEA,MAAM,eAAyC;IAC7C,UAAU,aAAa;IACvB;;AAEF,aACEV,wBAAC,oBAAoB,UAAQ,EAAC,OAAO,cAAY,SACtC,CAAA;AAGf;SAOgB,kBAAe;AAC7B,MAAM,cAAU,yBAAW,mBAAmB;AAC9C,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MACR,4DAA4D;;AAGhE,SAAO;AACT;ACxBgB,SAAA,oBACd,MACA,iBAAgC;AAEhC,MAAM,YAAY,aAAY;AAC9B,MAAM,SAAS,gBAAe;AAC9B,MAAM,WAAW,kBAAiB;AAClC,MAAM,QAAQ,eAAc;AACtB,MAAA,KAaF,gBAAe,GAZjB,gBAAa,GAAA,eACb,iBAAc,GAAA,gBACd,iBAAc,GAAA,gBACd,kBAAe,GAAA,iBACf,OAAI,GAAA,MACJ,QAAK,GAAA,OACL,mBAAgB,GAAA,kBAChB,kBAAe,GAAA,iBACf,kBAAe,GAAA,iBACf,iBAAc,GAAA,gBACd,mBAAgB,GAAA,kBAChB,iBAAc,GAAA;AAGhB,MAAM,UAA6B,SAAC,GAAC;;AACnC,QAAI,kBAAkB,SAAS,GAAG;AAChC,OAAAU,MAAA,OAAO,gBAAa,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,QAAA,MAAM,iBAAiB,CAAC;eACnC,oBAAoB,SAAS,GAAG;AACzC,OAAA,KAAA,SAAS,gBAAa,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,UAAA,MAAM,iBAAiB,CAAC;eACrC,iBAAiB,SAAS,GAAG;AACtC,OAAA,KAAA,MAAM,gBAAa,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,OAAA,MAAM,iBAAiB,CAAC;WACtC;AACL,OAAA,KAAA,UAAU,gBAAa,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,WAAA,MAAM,iBAAiB,CAAC;;EAEnD;AAEA,MAAM,UAA6B,SAAC,GAAC;;AACnC,UAAM,IAAI;AACV,KAAAA,MAAA,UAAU,gBAAa,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,WAAA,MAAM,iBAAiB,CAAC;EACjD;AAEA,MAAM,SAA4B,SAAC,GAAC;;AAClC,SAAI;AACJ,KAAAA,MAAA,UAAU,eAAY,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,WAAA,MAAM,iBAAiB,CAAC;EAChD;AAEA,MAAM,eAAkC,SAAC,GAAC;;AACxC,KAAAA,MAAA,UAAU,qBAAkB,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,WAAA,MAAM,iBAAiB,CAAC;EACtD;AACA,MAAM,eAAkC,SAAC,GAAC;;AACxC,KAAAA,MAAA,UAAU,qBAAkB,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,WAAA,MAAM,iBAAiB,CAAC;EACtD;AACA,MAAM,iBAAsC,SAAC,GAAC;;AAC5C,KAAAA,MAAA,UAAU,uBAAoB,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,WAAA,MAAM,iBAAiB,CAAC;EACxD;AACA,MAAM,iBAAsC,SAAC,GAAC;;AAC5C,KAAAA,MAAA,UAAU,uBAAoB,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,WAAA,MAAM,iBAAiB,CAAC;EACxD;AACA,MAAM,gBAAmC,SAAC,GAAC;;AACzC,KAAAA,MAAA,UAAU,sBAAmB,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,WAAA,MAAM,iBAAiB,CAAC;EACvD;AACA,MAAM,aAAgC,SAAC,GAAC;;AACtC,KAAAA,MAAA,UAAU,mBAAgB,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,WAAA,MAAM,iBAAiB,CAAC;EACpD;AACA,MAAM,cAAiC,SAAC,GAAC;;AACvC,KAAAA,MAAA,UAAU,oBAAiB,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,WAAA,MAAM,iBAAiB,CAAC;EACrD;AACA,MAAM,eAAkC,SAAC,GAAC;;AACxC,KAAAA,MAAA,UAAU,qBAAkB,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,WAAA,MAAM,iBAAiB,CAAC;EACtD;AAEA,MAAM,UAAgC,SAAC,GAAC;;AACtC,KAAAA,MAAA,UAAU,gBAAa,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,WAAA,MAAM,iBAAiB,CAAC;EACjD;AAEA,MAAM,YAAkC,SAAC,GAAC;;AACxC,YAAQ,EAAE,KAAG;MACX,KAAK;AACH,UAAE,eAAc;AAChB,UAAE,gBAAe;AACjB,kBAAU,QAAQ,QAAQ,cAAa,IAAK,eAAc;AAC1D;MACF,KAAK;AACH,UAAE,eAAc;AAChB,UAAE,gBAAe;AACjB,kBAAU,QAAQ,QAAQ,eAAc,IAAK,cAAa;AAC1D;MACF,KAAK;AACH,UAAE,eAAc;AAChB,UAAE,gBAAe;AACjB,uBAAc;AACd;MACF,KAAK;AACH,UAAE,eAAc;AAChB,UAAE,gBAAe;AACjB,wBAAe;AACf;MACF,KAAK;AACH,UAAE,eAAc;AAChB,UAAE,gBAAe;AACjB,UAAE,WAAW,gBAAe,IAAK,iBAAgB;AACjD;MACF,KAAK;AACH,UAAE,eAAc;AAChB,UAAE,gBAAe;AACjB,UAAE,WAAW,eAAc,IAAK,gBAAe;AAC/C;MACF,KAAK;AACH,UAAE,eAAc;AAChB,UAAE,gBAAe;AACjB,yBAAgB;AAChB;MACF,KAAK;AACH,UAAE,eAAc;AAChB,UAAE,gBAAe;AACjB,uBAAc;AACd;;AAEJ,KAAAA,MAAA,UAAU,kBAAe,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,WAAA,MAAM,iBAAiB,CAAC;EACnD;AAEA,MAAM,gBAAkC;IACtC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;AAGF,SAAO;AACT;SC9LgB,kBAAe;AAC7B,MAAM,YAAY,aAAY;AAC9B,MAAM,SAAS,gBAAe;AAC9B,MAAM,WAAW,kBAAiB;AAClC,MAAM,QAAQ,eAAc;AAE5B,MAAM,eAAe,kBAAkB,SAAS,IAC5C,OAAO,WACP,oBAAoB,SAAS,IAC3B,SAAS,WACT,iBAAiB,SAAS,IACxB,MAAM,WACN;AAER,SAAO;AACT;AC9BA,SAAS,mBAAmB,UAAgB;AAC1C,SAAO,OAAO,OAAO,gBAAgB,EAAE,SAAS,QAA4B;AAC9E;AASgB,SAAA,iBACd,WACA,iBAAgC;AAEhC,MAAM,aAAuB,CAAC,UAAU,WAAW,GAAG;AACtD,SAAO,KAAK,eAAe,EAAE,QAAQ,SAAC,UAAQ;AAC5C,QAAM,kBAAkB,UAAU,oBAAoB,QAAQ;AAC9D,QAAI,iBAAiB;AACnB,iBAAW,KAAK,eAAe;eACtB,mBAAmB,QAAQ,GAAG;AACvC,UAAM,oBAAoB,UAAU,WAAW,OAAO,OAAA,QAAQ,CAAE;AAChE,UAAI,mBAAmB;AACrB,mBAAW,KAAK,iBAAiB;;;EAGvC,CAAC;AACD,SAAO;AACT;ACzBgB,SAAA,YACd,WACA,iBAAgC;AAEhC,MAAI,QAAK,SAAA,CAAA,GACJ,UAAU,OAAO,GAAG;AAEzB,SAAO,KAAK,eAAe,EAAE,QAAQ,SAAC,UAAQ;;AAC5C,YAAK,SAAA,SAAA,CAAA,GACA,KAAK,IACL,KAAA,UAAU,qBAAkB,QAAA,OAAA,SAAA,SAAA,GAAA,QAAQ,CAAC;EAE5C,CAAC;AACD,SAAO;AACT;SCsBgB,aAEd,KAEA,cAEA,WAAuC;;;AAEvC,MAAM,YAAY,aAAY;AAC9B,MAAM,eAAe,gBAAe;AACpC,MAAM,kBAAkB,mBAAmB,KAAK,YAAY;AAC5D,MAAM,gBAAgB,oBAAoB,KAAK,eAAe;AAC9D,MAAM,eAAe,gBAAe;AACpC,MAAM,WAAW,QACf,UAAU,cAAc,UAAU,SAAS,SAAS;AAItD,8BAAU,WAAA;;AACR,QAAI,gBAAgB;AAAS;AAC7B,QAAI,CAAC,aAAa;AAAY;AAC9B,QAAI,CAAC;AAAU;AACf,QAAI,UAAU,aAAa,YAAY,GAAG,GAAG;AAC3C,OAAAA,MAAA,UAAU,aAAS,QAAAA,QAAA,SAAA,SAAAA,IAAA,MAAK;;EAE5B,GAAG;IACD,aAAa;IACb;IACA;IACA;IACA,gBAAgB;EACjB,CAAA;AAED,MAAM,YAAY,iBAAiB,WAAW,eAAe,EAAE,KAAK,GAAG;AACvE,MAAM,QAAQ,YAAY,WAAW,eAAe;AACpD,MAAM,WAAW,QACd,gBAAgB,WAAW,CAAC,UAAU,mBACrC,gBAAgB,MAAM;AAG1B,MAAM,uBAAsB,MAAA,KAAA,UAAU,gBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,gBAAc,QAAA,OAAA,SAAA,KAAA;AAChE,MAAM,eACJV,wBAAC,qBAAmB,EAClB,MAAM,KACN,cACA,gBAAgC,CAAA;AAIpC,MAAM,WAAW;IACf;IACA;IACA;IACA,MAAM;;AAGR,MAAM,gBACJ,aAAa,eACb,UAAU,aAAa,aAAa,GAAG,KACvC,CAAC,gBAAgB;AAEnB,MAAM,YACJ,aAAa,cAAc,UAAU,aAAa,YAAY,GAAG;AAEnE,MAAM,cACD,SAAA,SAAA,SAAA,CAAA,GAAA,QAAQ,IACX,KAAA,EAAA,UAAU,gBAAgB,UAC1B,MAAM,WAAU,GACf,GAAA,eAAe,IAAG,gBAAgB,UACnC,GAAA,WAAU,aAAa,gBAAgB,IAAI,IACxC,GAAA,GAAA,aAAa;AAGlB,MAAM,YAAuB;IAC3B;IACA;IACA;IACA;IACA;IACA;;AAGF,SAAO;AACT;AC3GM,SAAU,IAAI,OAAe;AACjC,MAAM,gBAAY,qBAA0B,IAAI;AAChD,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,cAAc,SAAS;AAExE,MAAI,UAAU,UAAU;AACtB,eAAOA,wBAAK,OAAA,EAAA,MAAK,WAAU,CAAA;;AAE7B,MAAI,CAAC,UAAU,UAAU;AACvB,eAAOA,wBAAS,OAAA,SAAA,CAAA,GAAA,UAAU,QAAQ,CAAA;;AAEpC,aAAOA,wBAAC,QAAO,SAAA,EAAA,MAAK,OAAM,KAAK,UAAS,GAAM,UAAU,WAAW,CAAA;AACrE;ACTM,SAAU,WAAW,OAAsB;AACvC,MAAQ,aAAsB,MAAK,QAAf,QAAU,MAAK;AACrC,MAAA,KAOF,aAAY,GANd,oBAAiB,GAAA,mBACjB,SAAM,GAAA,QACN,aAAU,GAAA,YACV,SAAM,GAAA,QACIqB,mBAAe,GAAA,OAAA,iBACXC,oBAAgB,GAAA,WAAA;AAGhC,MAAM,UAAUA,kBAAiB,OAAO,UAAU,GAAG,EAAE,OAAM,CAAE;AAE/D,MAAI,CAAC,mBAAmB;AACtB,eACEtB,wBAAM,QAAA,EAAA,WAAW,WAAW,YAAY,OAAO,OAAO,YAAU,UAC7D,QAAO,CAAA;;AAKd,MAAM,QAAQqB,iBAAgB,OAAO,UAAU,GAAG,EAAE,OAAM,CAAE;AAE5D,MAAM,cAAiC,SAAU,GAAC;AAChD,sBAAkB,YAAY,OAAO,CAAC;EACxC;AAEA,aACErB,wBAAC,QAAM,EACL,MAAK,eAAa,cACN,OACZ,WAAW,WAAW,YACtB,OAAO,OAAO,YACd,SAAS,aAAW,UAEnB,QAAO,CAAA;AAGd;ACvCM,SAAU,IAAI,OAAe;;AAC3B,MAAA,KAAqD,aAAY,GAA/D,SAAM,GAAA,QAAE,aAAU,GAAA,YAAE,iBAAc,GAAA,gBAAE,aAAU,GAAA;AAEtD,MAAM,gBAAe,KAAA,eAAA,QAAA,eAAA,SAAA,SAAA,WAAY,SAAO,QAAA,OAAA,SAAA,KAAA;AACxC,MAAM,uBAAsB,KAAA,eAAA,QAAA,eAAA,SAAA,SAAA,WAAY,gBAAc,QAAA,OAAA,SAAA,KAAA;AAEtD,MAAI;AACJ,MAAI,gBAAgB;AAClB,yBACEA,wBAAI,MAAA,EAAA,WAAW,WAAW,MAAM,OAAO,OAAO,MAC5C,cAAAA,wBAAC,qBAAmB,EAAC,QAAQ,MAAM,YAAY,OAAO,MAAM,MAAK,CAAI,EAAA,CAAA;;AAK3E,aACEE,yBAAA,MAAA,EAAI,WAAW,WAAW,KAAK,OAAO,OAAO,KAAG,UAAA,CAC7C,gBACA,MAAM,MAAM,IAAI,SAAC,MAAI;AAAK,eACzBF,wBACE,MAAA,EAAA,WAAW,WAAW,MACtB,OAAO,OAAO,MAEd,MAAK,gBAEL,cAAAA,wBAAC,cAAY,EAAC,cAAc,MAAM,cAAc,KAAU,CAAI,EAAA,GAHzD,YAAY,IAAI,CAAC;EAJC,CAS1B,CAAC,EAAA,CAAA;AAGR;SClCgB,iBACd,UACA,QACA,SAKC;AAED,MAAM,UAAS,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WACpB,aAAa,MAAM,IACnB,UAAU,QAAQ,OAAO;AAC7B,MAAM,YAAW,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WACtB,eAAe,QAAQ,IACvB,YAAY,UAAU,OAAO;AAEjC,MAAM,UAAU,yBAAyB,QAAQ,QAAQ;AACzD,MAAM,OAAe,CAAA;AAErB,WAAS,IAAI,GAAG,KAAK,SAAS,KAAK;AACjC,SAAK,KAAK,QAAQ,UAAU,CAAC,CAAC;;AAGhC,MAAM,eAAe,KAAK,OAAO,SAAC,QAAqB,MAAI;AACzD,QAAM,cAAa,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WACxB,WAAW,IAAI,IACf,QAAQ,MAAM,OAAO;AAEzB,QAAM,eAAe,OAAO,KAC1B,SAAC,OAAK;AAAK,aAAA,MAAM,eAAe;IAArB,CAA+B;AAE5C,QAAI,cAAc;AAChB,mBAAa,MAAM,KAAK,IAAI;AAC5B,aAAO;;AAET,WAAO,KAAK;MACV;MACA,OAAO,CAAC,IAAI;IACb,CAAA;AACD,WAAO;KACN,CAAA,CAAE;AAEL,SAAO;AACT;ACrCgB,SAAA,cACd,OACA,SAMC;AAED,MAAM,eAA4B,iBAChC,aAAa,KAAK,GAClB,WAAW,KAAK,GAChB,OAAO;AAGT,MAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,eAAe;AAE1B,QAAM,iBAAiB,gBAAgB,OAAO,OAAO;AACrD,QAAI,iBAAiB,GAAG;AACtB,UAAM,WAAW,aAAa,aAAa,SAAS,CAAC;AACrD,UAAM,WAAW,SAAS,MAAM,SAAS,MAAM,SAAS,CAAC;AACzD,UAAM,SAAS,SAAS,UAAU,IAAI,cAAc;AACpD,UAAM,aAAa,iBACjB,SAAS,UAAU,CAAC,GACpB,QACA,OAAO;AAET,mBAAa,KAAI,MAAjB,cAAqB,UAAU;;;AAGnC,SAAO;AACT;ACpCM,SAAU,MAAM,OAAiB;;AAC/B,MAAA,KAUF,aAAY,GATd,SAAM,GAAA,QACN,aAAU,GAAA,YACV,SAAM,GAAA,QACN,WAAQ,GAAA,UACR,aAAU,GAAA,YACV,aAAU,GAAA,YACV,eAAY,GAAA,cACZ,wBAAqB,GAAA,uBACrB,UAAO,GAAA;AAGT,MAAM,QAAQ,cAAc,MAAM,cAAc;IAC9C,eAAe,QAAQ,UAAU;IACjC;IACA;IACA;IACA;EACD,CAAA;AAED,MAAM,iBAAgB,KAAA,eAAA,QAAA,eAAA,SAAA,SAAA,WAAY,UAAQ,QAAA,OAAA,SAAA,KAAA;AAC1C,MAAM,gBAAe,KAAA,eAAA,QAAA,eAAA,SAAA,SAAA,WAAY,SAAO,QAAA,OAAA,SAAA,KAAA;AACxC,MAAM,mBAAkB,KAAA,eAAA,QAAA,eAAA,SAAA,SAAA,WAAY,YAAU,QAAA,OAAA,SAAA,KAAA;AAC9C,aACEE,yBAAAA,SAAAA,EACE,IAAI,MAAM,IACV,WAAW,WAAW,OACtB,OAAO,OAAO,OACd,MAAK,QAAM,mBACM,MAAM,iBAAiB,GAEvC,UAAA,CAAA,CAAC,gBAAYF,wBAAC,eAAa,CAAA,CAAA,OAC5BA,wBAAA,SAAA,EAAO,WAAW,WAAW,OAAO,OAAO,OAAO,OAC/C,UAAA,MAAM,IAAI,SAAC,MAAI;AAAK,eACnBA,wBAAC,cAAY,EACX,cAAc,MAAM,cAEpB,OAAO,KAAK,OACZ,YAAY,KAAK,WAAU,GAFtB,KAAK,UAAU;EAIvB,CAAA,EAAC,CACI,OACRA,wBAAC,iBAAe,EAAC,cAAc,MAAM,aAAY,CAAA,CAAI,EAC/C,CAAA;AAEZ;ACOA,SAAS,YAAS;AAChB,SAAO,CAAC,EACN,OAAO,WAAW,eAClB,OAAO,YACP,OAAO,SAAS;AAEpB;AAyBA,IAAM,4BAA4B,UAAS,IAAK,+BAAkB;AAElE,IAAI,wBAAwB;AAC5B,IAAI,KAAK;AACT,SAAS,QAAK;AACZ,SAAO,oBAAoB,OAAA,EAAE,EAAE;AACjC;AAyBA,SAAS,MAAM,YAA+C;;AAM5D,MAAI,YAAY,eAAU,QAAV,eAAA,SAAA,aAAe,wBAAwB,MAAK,IAAK;AAC7D,MAAA,SAAc,uBAAS,SAAS,GAA/BuB,MAAE,GAAA,CAAA,GAAE,QAAK,GAAA,CAAA;AAEd,4BAA0B,WAAA;AACxB,QAAIA,QAAO,MAAM;AAKf,YAAM,MAAK,CAAE;;KAGd,CAAA,CAAE;AAEL,8BAAU,WAAA;AACR,QAAI,0BAA0B,OAAO;AAInC,8BAAwB;;KAEzB,CAAA,CAAE;AAEL,UAAO,KAAA,eAAU,QAAV,eAAU,SAAV,aAAcA,SAAM,QAAA,OAAA,SAAA,KAAA;AAC7B;ACtJM,SAAU,MAAM,OAAiB;;;AACrC,MAAM,YAAY,aAAY;AACtB,MAAA,MAAwC,UAAS,KAA5C,aAAmC,UAAS,YAAhC,SAAuB,UAAjB,QAAE,aAAe,UAAS;AACjD,MAAA,gBAAkB,cAAa,EAAE;AAEzC,MAAM,YAAY,MAChB,UAAU,KAAK,GAAG,OAAA,UAAU,IAAE,GAAA,EAAA,OAAI,MAAM,YAAY,IAAK,MAAS;AAGpE,MAAM,UAAU,UAAU,KACtB,GAAA,OAAG,UAAU,IAAW,QAAA,EAAA,OAAA,MAAM,YAAY,IAC1C;AAEJ,MAAM,YAAY,CAAC,WAAW,KAAK;AACnC,MAAI,QAAQ,OAAO;AAEnB,MAAI,UAAU,MAAM,iBAAiB;AACrC,MAAI,QAAQ,MAAM,iBAAiB,cAAc,SAAS;AAC1D,MAAM,WAAW,CAAC,WAAW,CAAC;AAC9B,MAAI,QAAQ,OAAO;AACjB,SAAmB,CAAC,SAAS,KAAK,GAAjC,QAAK,GAAA,CAAA,GAAE,UAAO,GAAA,CAAA;;AAGjB,MAAI,SAAS;AACX,cAAU,KAAK,WAAW,aAAa;AACvC,YAAK,SAAA,SAAA,CAAA,GAAQ,KAAK,GAAK,OAAO,aAAa;;AAE7C,MAAI,OAAO;AACT,cAAU,KAAK,WAAW,WAAW;AACrC,YAAK,SAAA,SAAA,CAAA,GAAQ,KAAK,GAAK,OAAO,WAAW;;AAE3C,MAAI,UAAU;AACZ,cAAU,KAAK,WAAW,eAAe;AACzC,YAAK,SAAA,SAAA,CAAA,GAAQ,KAAK,GAAK,OAAO,eAAe;;AAG/C,MAAM,oBAAmB,KAAA,eAAA,QAAA,eAAA,SAAA,SAAA,WAAY,aAAW,QAAA,OAAA,SAAA,KAAA;AAEhD,aACErB,yBAA8B,OAAA,EAAA,WAAW,UAAU,KAAK,GAAG,GAAG,OAC5D,UAAA,KAAAF,wBAAC,kBAAgB,EACf,IAAI,WACJ,cAAc,MAAM,cACpB,cAAc,MAAM,aAAY,CAChC,OACFA,wBAAC,OACC,EAAA,IAAI,SACa,mBAAA,WACjB,cAAc,MAAM,aAAY,CAAA,CAChC,EAVM,GAAA,MAAM,YAAY;AAahC;ACvDM,SAAU,OAAO,OAAkB;AACjC,MAAA,KAAyB,aAAY,GAAnC,aAAU,GAAA,YAAE,SAAM,GAAA;AAE1B,aACEA,wBAAK,OAAA,EAAA,WAAW,WAAW,QAAQ,OAAO,OAAO,QAC9C,UAAA,MAAM,SAAQ,CAAA;AAGrB;ACGM,SAAU,KAAK,IAA2B;;AAAzB,MAAA,eAAY,GAAA;AACjC,MAAM,YAAY,aAAY;AAC9B,MAAM,eAAe,gBAAe;AACpC,MAAM,aAAa,cAAa;AAE1B,MAAA,SAAwC,uBAAS,KAAK,GAArD,kBAAe,GAAA,CAAA,GAAE,qBAAkB,GAAA,CAAA;AAG1C,8BAAU,WAAA;AACR,QAAI,CAAC,UAAU;AAAc;AAC7B,QAAI,CAAC,aAAa;AAAa;AAC/B,QAAI;AAAiB;AAErB,iBAAa,MAAM,aAAa,WAAW;AAC3C,uBAAmB,IAAI;EACzB,GAAG;IACD,UAAU;IACV;IACA,aAAa;IACb,aAAa;IACb;EACD,CAAA;AAGD,MAAM,aAAa,CAAC,UAAU,WAAW,MAAM,UAAU,SAAS;AAClE,MAAI,UAAU,iBAAiB,GAAG;AAChC,eAAW,KAAK,UAAU,WAAW,eAAe;;AAEtD,MAAI,UAAU,gBAAgB;AAC5B,eAAW,KAAK,UAAU,WAAW,eAAe;;AAGtD,MAAM,QAAK,SAAA,SAAA,CAAA,GACN,UAAU,OAAO,IAAI,GACrB,UAAU,KAAK;AAGpB,MAAM,iBAAiB,OAAO,KAAK,YAAY,EAC5C,OAAO,SAAC,KAAG;AAAK,WAAA,IAAI,WAAW,OAAO;EAAC,CAAA,EACvC,OAAO,SAAC,OAAO,KAAG;;AAEjB,WACK,SAAA,SAAA,CAAA,GAAA,KAAK,IAAAU,MAAA,CAAA,GAAAA,IACP,GAAG,IAAG,aAAa,GAAG,GACvBA,IAAA;KACD,CAAA,CAAE;AAEP,MAAM,mBAAkB,MAAA,KAAA,aAAa,gBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,YAAU,QAAA,OAAA,SAAA,KAAA;AAE3D,aACEV,wBACE,OAAA,SAAA,EAAA,WAAW,WAAW,KAAK,GAAG,GAC9B,OACA,KAAK,UAAU,KACf,IAAI,UAAU,IACd,OAAO,aAAa,OACpB,OAAO,aAAa,OACpB,MAAM,aAAa,KAAI,GACnB,gBAAc,EAAA,cAElBA,wBAAC,iBAAe,EAAA,UACb,WAAW,cAAc,IAAI,SAAC,OAAO,GAAC;AAAK,eAC1CA,wBAAC,OAAK,EAAS,cAAc,GAAG,cAAc,MAAK,GAAvC,CAAC;EAD6B,CAE3C,EAAC,CACc,EAAA,CAAA,CAAA;AAGxB;AC7DM,SAAU,aAAa,OAAkB;AACrC,MAAA,WAA8B,MAAtB,UAAK,eAAY,OAAK,OAAhC,CAA6B,UAAA,CAAA;AAEnC,aACEA,wBAAC,mBAAiB,EAAC,cAA0B,cAC3CA,wBAAC,oBAAkB,EAAA,cACjBA,wBAAC,sBAAoB,EAAC,cACpB,cAAAA,wBAAC,wBAAsB,EAAC,cACtB,cAAAA,wBAAC,qBAAoB,EAAA,cAA0B,cAC7CA,wBAAC,mBACC,EAAA,cAAAA,wBAAC,eAAe,EAAA,SAAQ,CAAA,EAAiB,CACvB,EAAA,CAAA,EACA,CAAA,EACC,CACJ,EAAA,CAAA,EACJ,CACH;AAExB;ACwDM,SAAU,UACd,OAIuB;AAEvB,aACEA,wBAAC,cAAY,SAAA,CAAA,GAAK,OAChB,EAAA,cAAAA,wBAAC,MAAI,EAAC,cAAc,MAAK,CAAI,EAAA,CAAA,CAAA;AAGnC;AChHM,SAAU,YAAY,KAAS;AACnC,SAAO,CAAC,MAAM,IAAI,QAAO,CAAE;AAC7B;AC0EM,SAAU,SAAS,SAA6B;AAA7B,MAAA,YAAA,QAAA;AAAA,cAA6B,CAAA;EAAA;AAElD,MAAA,KAKE,QAAO,QALT,SAAM,OAAA,SAAG,OAAI,IACb,WAIE,QAAO,UAHT,KAGE,QAAO,QAHTwB,WAAS,OAAA,SAAA,OAAI,IACb,kBAEE,QAAO,iBADT,KACE,QAAO,OADT,QAAK,OAAA,SAAG,oBAAI,KAAI,IAAE;AAEd,MAAA,KAAuB,iBAAiB,OAAO,GAA7C,WAAQ,GAAA,UAAE,SAAM,GAAA;AAGxB,MAAM,aAAa,SAAC,OAAa;AAAK,WAAA,MAAM,OAAOA,UAAQ,OAAO,EAAE,OAAM,CAAE;EAAtC;AAGhC,MAAA,SAAoB,uBAAS,oBAAe,QAAf,oBAAA,SAAA,kBAAmB,KAAK,GAApD,QAAK,GAAA,CAAA,GAAEhB,YAAQ,GAAA,CAAA;AAChB,MAAA,SAAgC,uBAAS,eAAe,GAAvD,cAAW,GAAA,CAAA,GAAE,iBAAc,GAAA,CAAA;AAClC,MAAM,oBAAoB,kBACtBiB,OAAQ,iBAAiBD,UAAQ,EAAE,OAAM,CAAE,IAC3C;AACE,MAAA,SAA8B,uBAAS,iBAAiB,GAAvD,aAAU,GAAA,CAAA,GAAE,gBAAa,GAAA,CAAA;AAEhC,MAAM,QAAQ,WAAA;AACZ,mBAAe,eAAe;AAC9B,IAAAhB,UAAS,oBAAA,QAAA,oBAAe,SAAf,kBAAmB,KAAK;AACjC,kBAAc,sBAAA,QAAA,sBAAiB,SAAjB,oBAAqB,EAAE;EACvC;AAEA,MAAM,cAAc,SAAC,MAAsB;AACzC,mBAAe,IAAI;AACnB,IAAAA,UAAS,SAAA,QAAA,SAAI,SAAJ,OAAQ,KAAK;AACtB,kBAAc,OAAOiB,OAAQ,MAAMD,UAAQ,EAAE,OAAM,CAAE,IAAI,EAAE;EAC7D;AAEA,MAAM,iBAAuC,SAAC,KAAKd,KAAY;AAAV,QAAA,WAAQA,IAAA;AAC3D,QAAI,CAAC,YAAY,UAAU;AACzB,qBAAe,MAAS;AACxB,oBAAc,EAAE;AAChB;;AAEF,mBAAe,GAAG;AAClB,kBAAc,MAAMe,OAAQ,KAAKD,UAAQ,EAAE,OAAM,CAAE,IAAI,EAAE;EAC3D;AAEA,MAAM,oBAA6C,SAACf,QAAK;AACvD,IAAAD,UAASC,MAAK;EAChB;AAKA,MAAM,eAAqD,SAAC,GAAC;AAC3D,kBAAc,EAAE,OAAO,KAAK;AAC5B,QAAM,MAAM,WAAW,EAAE,OAAO,KAAK;AACrC,QAAMiB,YAAW,YAAY,yBAAyB,UAAU,GAAG,IAAI;AACvE,QAAMC,WAAU,UAAU,yBAAyB,KAAK,MAAM,IAAI;AAClE,QAAI,CAAC,YAAY,GAAG,KAAKD,aAAYC,UAAS;AAC5C,qBAAe,MAAS;AACxB;;AAEF,mBAAe,GAAG;AAClB,IAAAnB,UAAS,GAAG;EACd;AAIA,MAAM,aAAkD,SAAC,GAAC;AACxD,QAAM,MAAM,WAAW,EAAE,OAAO,KAAK;AACrC,QAAI,CAAC,YAAY,GAAG,GAAG;AACrB,YAAK;;EAET;AAIA,MAAM,cAAmD,SAAC,GAAC;AACzD,QAAI,CAAC,EAAE,OAAO,OAAO;AACnB,YAAK;AACL;;AAEF,QAAM,MAAM,WAAW,EAAE,OAAO,KAAK;AACrC,QAAI,YAAY,GAAG,GAAG;AACpB,MAAAA,UAAS,GAAG;;EAEhB;AAEA,MAAM,iBAAsC;IAC1C;IACA,YAAY;IACZ,eAAe;IACf,UAAU;IACV;IACA;IACA;IACA;;AAGF,MAAM,aAAyB;IAC7B,QAAQ;IACR,UAAU;IACV,SAAS;IACT,OAAO;IACP,aAAaiB,OAAQ,oBAAI,KAAI,GAAID,UAAQ,EAAE,OAAM,CAAE;;AAGrD,SAAO,EAAE,gBAAgB,YAAY,OAAO,YAAW;AACzD;AC5KM,SAAU,mBACd,OAAqB;AAErB,SAAO,MAAM,SAAS,UAAa,MAAM,SAAS;AACpD;", "names": ["__assign", "_jsx", "formatCaption", "_jsxs", "formatMonthCaption", "labelMonthDropdown", "formatYearCaption", "labelYearDropdown", "year", "setMonth", "month", "_a", "labelPrevious", "labelNext", "_Fragment", "formatWeekdayName", "labelWeekday", "formatDay", "min", "max", "_b", "InternalModifier", "labelWeekNumber", "formatWeekNumber", "id", "format", "_format", "isBefore", "isAfter"]}