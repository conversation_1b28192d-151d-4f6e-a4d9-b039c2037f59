{"name": "react-quill", "version": "2.0.0", "description": "The Quill rich-text editor as a React component.", "author": "zenoamaro <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/zenoamaro/react-quill", "bugs": "https://github.com/zenoamaro/react-quill/issues", "repository": "https://github.com/zenoamaro/react-quill.git", "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"build": "npm run build:lib && npm run build:dist && npm run build:css", "build:lib": "tsc", "build:dist": "webpack", "build:css": "cpx 'node_modules/quill/dist/quill.*.css' dist", "watch": "tsc --watch", "pretest": "npm run build", "test": "npm run test:unit && npm run test:coverage && npm run test:browser", "test:unit": "mocha --recursive --require=./test/setup.js -R spec test/index", "test:coverage": "mocha --recursive --require=./test/setup.js -R mocha-text-cov test/index", "test:browser": "cypress run", "test:browser:interactive": "cypress open", "demo": "superstatic demo", "clean": "<PERSON><PERSON><PERSON> lib dist", "prepublishOnly": "npm run build"}, "keywords": ["react", "react-component", "rich", "text", "rich-text", "textarea", "quill"], "files": ["dist/", "lib/", "types.d.ts", "README.md", "CHANGELOG.md", "LICENSE"], "dependencies": {"@types/quill": "^1.3.10", "lodash": "^4.17.4", "quill": "^1.3.7"}, "peerDependencies": {"react": "^16 || ^17 || ^18", "react-dom": "^16 || ^17 || ^18"}, "devDependencies": {"@types/chai": "^4.2.11", "@types/lodash": "^4.14.146", "@types/mocha": "^7.0.2", "@types/react": "^16.9.11", "@types/react-dom": "^16.9.4", "@types/sinon": "^7.5.2", "blanket": "^1.2.3", "chai": "^4.2.0", "chai-enzyme": "^1.0.0-beta.1", "cheerio": "^1.0.0-rc.3", "cpx": "^1.5.0", "cypress": "^4.3.0", "enzyme": "^3.10.0", "enzyme-adapter-react-16": "^1.15.1", "jsdom": "^11.0.0", "jsdom-global": "^3.0.2", "mocha": "^6.2.2", "mocha-text-cov": "^0.1.1", "react": "^16.11.0", "react-dom": "^16.11.0", "react-test-renderer": "^16.11.0", "rimraf": "^3.0.0", "should": "^13.2.3", "sinon": "^7.5.0", "superstatic": "^6.0.4", "travis-cov": "^0.2.5", "ts-loader": "^6.2.1", "typescript": "^3.7.2", "webpack": "^4.41.2", "webpack-cli": "^3.3.10"}}