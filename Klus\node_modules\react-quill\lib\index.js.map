{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.tsx"], "names": [], "mappings": ";AAAA;;;EAGE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEF,gDAA0B;AAC1B,wDAAiC;AACjC,2DAAqC;AAErC,gDAOe;AA2Ef;IAAyB,8BAAiD;IAyFxE,oBAAY,KAAsB;QAAlC,YACE,kBAAM,KAAK,CAAC,SAGb;QApFD;;;UAGE;QACF,gBAAU,GAA8B;YACtC,SAAS;YACT,SAAS;YACT,QAAQ;YACR,OAAO;YACP,UAAU;SACX,CAAA;QAED;;;UAGE;QACF,gBAAU,GAA8B;YACtC,IAAI;YACJ,WAAW;YACX,OAAO;YACP,aAAa;YACb,UAAU;YACV,UAAU;YACV,mBAAmB;YACnB,SAAS;YACT,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;SACV,CAAA;QAQD,WAAK,GAAoB;YACvB,UAAU,EAAE,CAAC;SACd,CAAA;QAiBD;;UAEE;QACF,eAAS,GAAU,IAAI,CAAA;QA2UvB,oBAAc,GAAG,UACf,SAA6C,EAC7C,YAAiC,EACjC,eAAoC,EACpC,MAAe;;YAEf,IAAI,SAAS,KAAK,aAAa,EAAE;gBAC/B,MAAA,MAAA,KAAI,EAAC,kBAAkB,mDACrB,KAAI,CAAC,MAAO,CAAC,IAAI,CAAC,SAAS,EAC3B,YAA2B,EAC3B,MAAM,EACN,KAAI,CAAC,kBAAmB,EACxB;aACH;iBAAM,IAAI,SAAS,KAAK,kBAAkB,EAAE;gBAC3C,MAAA,MAAA,KAAI,EAAC,uBAAuB,mDAC1B,YAA2B,EAC3B,MAAM,EACN,KAAI,CAAC,kBAAmB,EACxB;aACH;QACH,CAAC,CAAC;QAxUA,IAAM,KAAK,GAAG,KAAI,CAAC,YAAY,EAAE,CAAA,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC;QACpE,KAAI,CAAC,KAAK,IAAG,KAAK,aAAL,KAAK,cAAL,KAAK,GAAI,EAAE,CAAA,CAAC;;IAC3B,CAAC;IAED,kCAAa,GAAb,UAAc,KAAsB;;QAClC,IAAI,eAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC;YAAE,MAAM,IAAI,KAAK,CAC3D,wEAAwE,CACzE,CAAC;QAEF,IAAI,eAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;YACxC,IAAM,KAAK,GAAG,eAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAClD,IAAI,OAAA,KAAK,0CAAE,IAAI,MAAK,UAAU;gBAAE,MAAM,IAAI,KAAK,CAC7C,sEAAsE,CACvE,CAAC;SACH;QAED,IACE,IAAI,CAAC,kBAAkB;YACvB,KAAK,CAAC,KAAK,KAAK,IAAI,CAAC,kBAAkB;YACvC,MAAM,IAAI,KAAK,CACf,oEAAoE;gBACpE,qEAAqE;gBACrE,4DAA4D,CAC7D,CAAC;IACJ,CAAC;IAED,0CAAqB,GAArB,UAAsB,SAA0B,EAAE,SAA0B;QAA5E,iBAkCC;;QAjCC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAE9B,wEAAwE;QACxE,iDAAiD;QACjD,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,KAAK,SAAS,CAAC,UAAU,EAAE;YAClE,OAAO,IAAI,CAAC;SACb;QAED,gCAAgC;QAChC,IAAI,OAAO,IAAI,SAAS,EAAE;YACxB,IAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC9C,IAAM,YAAY,SAAG,SAAS,CAAC,KAAK,uCAAI,EAAE,EAAA,CAAC;YAE3C,wEAAwE;YACxE,yEAAyE;YACzE,mEAAmE;YACnE,qDAAqD;YACrD,yEAAyE;YACzE,wEAAwE;YACxE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC,EAAE;gBAClD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;aACnD;SACF;QAED,oCAAoC;QACpC,IAAI,SAAS,CAAC,QAAQ,KAAK,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YAC9C,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,QAAS,CAAC,CAAC;SAC1D;QAED,yCAAyC;QACzC,OAAO,eAAI,IAAI,CAAC,UAAU,EAAK,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAC,IAAI;YACxD,OAAO,CAAC,iBAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,8CAAyB,GAAzB,UAA0B,SAA0B;QAApD,iBAKC;QAJC,oEAAoE;QACpE,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAC,IAAI;YAC/B,OAAO,CAAC,iBAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,sCAAiB,GAAjB;QACE,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAO,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;IACjE,CAAC;IAED,yCAAoB,GAApB;QACE,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED,uCAAkB,GAAlB,UAAmB,SAA0B,EAAE,SAA0B;QAAzE,iBAuBC;QAtBC,2EAA2E;QAC3E,4EAA4E;QAC5E,0EAA0E;QAC1E,oDAAoD;QACpD,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,EAAE;YAC5D,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YACxC,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAC7C,IAAI,CAAC,oBAAoB,GAAG,EAAC,KAAK,OAAA,EAAE,SAAS,WAAA,EAAC,CAAC;YAC/C,IAAI,CAAC,QAAQ,CAAC,EAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,EAAC,CAAC,CAAC;YACvD,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;QAED,yEAAyE;QACzE,yEAAyE;QACzE,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,KAAK,SAAS,CAAC,UAAU,EAAE;YAC5C,IAAA,8BAA+C,EAA9C,gBAAK,EAAE,0BAAuC,CAAC;YACtD,OAAO,IAAI,CAAC,oBAAoB,CAAC;YACjC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAM,QAAM,GAAG,IAAI,CAAC,MAAO,CAAC;YAC5B,QAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAC1B,QAAQ,CAAC,cAAM,OAAA,KAAI,CAAC,kBAAkB,CAAC,QAAM,EAAE,WAAS,CAAC,EAA1C,CAA0C,CAAC,CAAC;SAC5D;IACH,CAAC;IAED,sCAAiB,GAAjB;QACE,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC9B;aAAM;YACL,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAC7B,IAAI,CAAC,cAAc,EAAE,EACrB,IAAI,CAAC,eAAe,EAAE,CACvB,CAAC;SACH;IACH,CAAC;IAED,kCAAa,GAAb;QACE,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO;QACzB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC;IAED;;MAEE;IACF,iCAAY,GAAZ;QACE,OAAO,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC;IAC/B,CAAC;IAED,oCAAe,GAAf;QACE,OAAO;YACL,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,kBAAkB,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB;YACjD,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;SACxB,CAAC;IACJ,CAAC;IAED,8BAAS,GAAT;QACE,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvE,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;;MAGE;IACF,iCAAY,GAAZ,UAAa,OAAgB,EAAE,MAAoB;QACjD,IAAM,MAAM,GAAG,IAAI,eAAK,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC1C,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,EAAE;YAC3B,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;SACjD;QACD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACxB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,+BAAU,GAAV,UAAW,MAAa;QACtB,sEAAsE;QACtE,kEAAkE;QAClE,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;QAC9D,yEAAyE;QACzE,qBAAqB;QACrB,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IAClD,CAAC;IAED,iCAAY,GAAZ,UAAa,MAAa;QACxB,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IACnD,CAAC;IAED,sCAAiB,GAAjB;QACE,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,uCAAkB,GAAlB;QACE,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;MAEE;IACF,4BAAO,GAAP,UAAQ,KAAU;QAChB,OAAO,KAAK,IAAI,KAAK,CAAC,GAAG,CAAC;IAC5B,CAAC;IAED;;MAEE;IACF,iCAAY,GAAZ,UAAa,KAAU,EAAE,SAAc;QACrC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YAClD,OAAO,iBAAO,CAAC,KAAK,CAAC,GAAG,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC;SAC1C;aAAM;YACL,OAAO,iBAAO,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;SAClC;IACH,CAAC;IAED;;;MAGE;IACF,sCAAiB,GAAjB,UAAkB,MAAa,EAAE,KAAY;QAA7C,iBASC;QARC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACtC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;SACrD;aAAM;YACL,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SAC3B;QACD,QAAQ,CAAC,cAAM,OAAA,KAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,GAAG,CAAC,EAApC,CAAoC,CAAC,CAAC;IACvD,CAAC;IAED,uCAAkB,GAAlB,UAAmB,MAAa,EAAE,KAAY;QAC5C,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,KAAK,EAAE;YACT,mCAAmC;YACnC,IAAM,QAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YAClC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,QAAM,GAAC,CAAC,CAAC,CAAC,CAAC;YAC3D,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,QAAM,GAAC,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YAC7E,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;SAC5B;IACH,CAAC;IAED,sCAAiB,GAAjB,UAAkB,MAAa,EAAE,QAAgB;;QAC/C,gBAAI,MAAM,0CAAE,MAAM,0CAAE,OAAO,EAAE;YAC1B,MAAM,CAAC,MAAM,CAAC,OAAuB,CAAC,QAAQ,GAAG,QAAQ,CAAC;SAC5D;IACH,CAAC;IAED,sCAAiB,GAAjB,UAAkB,MAAa,EAAE,KAAc;QAC7C,IAAI,KAAK,EAAE;YACT,MAAM,CAAC,OAAO,EAAE,CAAC;SAClB;aAAM;YACL,MAAM,CAAC,MAAM,EAAE,CAAC;SACjB;IACH,CAAC;IAED;;;MAGE;IACF,2CAAsB,GAAtB,UAAuB,MAAa;QAClC,IAAM,CAAC,GAAG,MAAM,CAAC;QACjB,OAAO;YACL,OAAO,EAAO,cAAM,OAAA,CAAC,CAAC,IAAI,CAAC,SAAS,EAAhB,CAAgB;YACpC,SAAS,EAAK,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;YACjC,OAAO,EAAO,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;YAC/B,WAAW,EAAG,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;YACnC,YAAY,EAAE,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;YACpC,SAAS,EAAK,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;SAClC,CAAC;IACJ,CAAC;IAED,mCAAc,GAAd;QACE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;SAC1D;QACD,IAAM,OAAO,GAAG,mBAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACvD,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;SACzD;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;SACvD;QACD,OAAO,OAAkB,CAAC;IAC5B,CAAC;IAED;;MAEE;IACF,sCAAiB,GAAjB;QAAA,iBAqBC;QApBO,IAAA,eAA2C,EAA1C,sBAAQ,EAAE,0CAAgC,CAAC;QAC3C,IAAA,kCAAU,CAAe;QAEhC,IAAM,UAAU,GAAG;YACjB,GAAG,EAAE,UAAU;YACf,GAAG,EAAE,UAAC,QAAoC;gBACxC,KAAI,CAAC,WAAW,GAAG,QAAQ,CAAA;YAC7B,CAAC;SACF,CAAC;QAEF,IAAI,eAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;YAClC,OAAO,eAAK,CAAC,YAAY,CACvB,eAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAE,EAC9B,UAAU,CACX,CAAC;SACH;QAED,OAAO,kBAAkB,CAAC,CAAC;YACzB,kDAAS,UAAU,EAAG,CAAC,CAAC;YACxB,kDAAS,UAAU,EAAG,CAAC;IAC3B,CAAC;IAED,2BAAM,GAAN;;QACE,OAAO,CACL,uCACE,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,EACjB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EACvB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,EAC1B,SAAS,EAAE,iBAAS,IAAI,CAAC,KAAK,CAAC,SAAS,uCAAI,EAAE,EAAE,EAChD,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,EACjC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAC/B,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,IAE1B,IAAI,CAAC,iBAAiB,EAAE,CACrB,CACP,CAAC;IACJ,CAAC;IAwBD,uCAAkB,GAAlB,UACE,KAAa,EACb,KAAkB,EAClB,MAAe,EACf,MAA0B;;QAE1B,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO;QAEzB,oEAAoE;QACpE,iEAAiE;QACjE,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;YAC3C,CAAC,CAAC,MAAM,CAAC,WAAW,EAAE;YACtB,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QAErB,IAAI,YAAY,KAAK,IAAI,CAAC,iBAAiB,EAAE,EAAE;YAC7C,kEAAkE;YAClE,kEAAkE;YAClE,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;YAEhC,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC;YAC1B,MAAA,MAAA,IAAI,CAAC,KAAK,EAAC,QAAQ,mDAAG,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE;SACrD;IACH,CAAC;IAED,4CAAuB,GAAvB,UACE,aAA0B,EAC1B,MAAe,EACf,MAA0B;;QAE1B,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO;QACzB,IAAM,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACnD,IAAM,cAAc,GAAG,CAAC,gBAAgB,IAAI,aAAa,CAAC;QAC1D,IAAM,YAAY,GAAG,gBAAgB,IAAI,CAAC,aAAa,CAAC;QAExD,IAAI,iBAAO,CAAC,aAAa,EAAE,gBAAgB,CAAC;YAAE,OAAO;QAErD,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC;QAC/B,MAAA,MAAA,IAAI,CAAC,KAAK,EAAC,iBAAiB,mDAAG,aAAa,EAAE,MAAM,EAAE,MAAM,EAAE;QAE9D,IAAI,cAAc,EAAE;YAClB,MAAA,MAAA,IAAI,CAAC,KAAK,EAAC,OAAO,mDAAG,aAAa,EAAE,MAAM,EAAE,MAAM,EAAE;SACrD;aAAM,IAAI,YAAY,EAAE;YACvB,MAAA,MAAA,IAAI,CAAC,KAAK,EAAC,MAAM,mDAAG,gBAAgB,EAAE,MAAM,EAAE,MAAM,EAAE;SACvD;IACH,CAAC;IAED,0BAAK,GAAL;QACE,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO;QACzB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IACtB,CAAC;IAED,yBAAI,GAAJ;QACE,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO;QACzB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;IACrB,CAAC;IA1dM,sBAAW,GAAG,aAAa,CAAA;IAElC;;MAEE;IACK,gBAAK,GAAG,eAAK,CAAC;IAiCd,uBAAY,GAAG;QACpB,KAAK,EAAE,MAAM;QACb,OAAO,EAAE,EAAE;QACX,QAAQ,EAAE,KAAK;KAChB,CAAA;IAibH,iBAAC;CAAA,AA7dD,CAAyB,eAAK,CAAC,SAAS,GA6dvC;AAED;;EAEE;AACF,SAAS,QAAQ,CAAC,EAAyB;IACzC,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC7B,CAAC;AAID,iBAAS,UAAU,CAAC", "sourcesContent": ["/*\nReact-Quill\nhttps://github.com/zenoamaro/react-quill\n*/\n\nimport React from 'react';\nimport ReactDOM from 'react-dom';\nimport isEqual from 'lodash/isEqual';\n\nimport Quill, {\n  QuillOptionsStatic,\n  DeltaStatic,\n  RangeStatic,\n  BoundsStatic,\n  StringMap,\n  Sources,\n} from 'quill';\n\n// Merged namespace hack to export types along with default object\n// See: https://github.com/Microsoft/TypeScript/issues/2719\nnamespace ReactQuill {\n  export type Value = string | DeltaStatic;\n  export type Range = RangeStatic | null;\n\n  export interface QuillOptions extends QuillOptionsStatic {\n    tabIndex?: number,\n  }\n\n  export interface ReactQuillProps {\n    bounds?: string | HTMLElement,\n    children?: React.ReactElement<any>,\n    className?: string,\n    defaultValue?: Value,\n    formats?: string[],\n    id?: string,\n    modules?: StringMap,\n    onChange?(\n      value: string,\n      delta: DeltaStatic,\n      source: Sources,\n      editor: UnprivilegedEditor,\n    ): void,\n    onChangeSelection?(\n      selection: Range,\n      source: Sources,\n      editor: UnprivilegedEditor,\n    ): void,\n    onFocus?(\n      selection: Range,\n      source: Sources,\n      editor: UnprivilegedEditor,\n    ): void,\n    onBlur?(\n      previousSelection: Range,\n      source: Sources,\n      editor: UnprivilegedEditor,\n    ): void,\n    onKeyDown?: React.EventHandler<any>,\n    onKeyPress?: React.EventHandler<any>,\n    onKeyUp?: React.EventHandler<any>,\n    placeholder?: string,\n    preserveWhitespace?: boolean,\n    readOnly?: boolean,\n    scrollingContainer?: string | HTMLElement,\n    style?: React.CSSProperties,\n    tabIndex?: number,\n    theme?: string,\n    value?: Value,\n  }\n\n  export interface UnprivilegedEditor {\n    getLength(): number;\n    getText(index?: number, length?: number): string;\n    getHTML(): string;\n    getBounds(index: number, length?: number): BoundsStatic;\n    getSelection(focus?: boolean): RangeStatic;\n    getContents(index?: number, length?: number): DeltaStatic;\n  }\n}\n\n// Re-import everything from namespace into scope for comfort\nimport Value = ReactQuill.Value;\nimport Range = ReactQuill.Range;\nimport QuillOptions = ReactQuill.QuillOptions;\nimport ReactQuillProps = ReactQuill.ReactQuillProps;\nimport UnprivilegedEditor = ReactQuill.UnprivilegedEditor;\n\ninterface ReactQuillState {\n  generation: number,\n}\n\nclass ReactQuill extends React.Component<ReactQuillProps, ReactQuillState> {\n\n  static displayName = 'React Quill'\n\n  /*\n  Export Quill to be able to call `register`\n  */\n  static Quill = Quill;\n\n  /*\n  Changing one of these props should cause a full re-render and a\n  re-instantiation of the Quill editor.\n  */\n  dirtyProps: (keyof ReactQuillProps)[] = [\n    'modules',\n    'formats',\n    'bounds',\n    'theme',\n    'children',\n  ]\n\n  /*\n  Changing one of these props should cause a regular update. These are mostly\n  props that act on the container, rather than the quillized editing area.\n  */\n  cleanProps: (keyof ReactQuillProps)[] = [\n    'id',\n    'className',\n    'style',\n    'placeholder',\n    'tabIndex',\n    'onChange',\n    'onChangeSelection',\n    'onFocus',\n    'onBlur',\n    'onKeyPress',\n    'onKeyDown',\n    'onKeyUp',\n  ]\n\n  static defaultProps = {\n    theme: 'snow',\n    modules: {},\n    readOnly: false,\n  }\n\n  state: ReactQuillState = {\n    generation: 0,\n  }\n\n  /*\n  The Quill Editor instance.\n  */\n  editor?: Quill\n\n  /*\n  Reference to the element holding the Quill editing area.\n  */\n  editingArea?: React.ReactInstance | null\n\n  /*\n  Tracks the internal value of the Quill editor\n  */\n  value: Value\n\n  /*\n  Tracks the internal selection of the Quill editor\n  */\n  selection: Range = null\n\n  /*\n  Used to compare whether deltas from `onChange` are being used as `value`.\n  */\n  lastDeltaChangeSet?: DeltaStatic\n\n  /*\n  Stores the contents of the editor to be restored after regeneration.\n  */\n  regenerationSnapshot?: {\n    delta: DeltaStatic,\n    selection: Range,\n  }\n\n  /*\n  A weaker, unprivileged proxy for the editor that does not allow accidentally\n  modifying editor state.\n  */\n  unprivilegedEditor?: UnprivilegedEditor\n\n  constructor(props: ReactQuillProps) {\n    super(props);\n    const value = this.isControlled()? props.value : props.defaultValue;\n    this.value = value ?? '';\n  }\n\n  validateProps(props: ReactQuillProps): void {\n    if (React.Children.count(props.children) > 1) throw new Error(\n      'The Quill editing area can only be composed of a single React element.'\n    );\n\n    if (React.Children.count(props.children)) {\n      const child = React.Children.only(props.children);\n      if (child?.type === 'textarea') throw new Error(\n        'Quill does not support editing on a <textarea>. Use a <div> instead.'\n      );\n    }\n\n    if (\n      this.lastDeltaChangeSet &&\n      props.value === this.lastDeltaChangeSet\n    ) throw new Error(\n      'You are passing the `delta` object from the `onChange` event back ' +\n      'as `value`. You most probably want `editor.getContents()` instead. ' +\n      'See: https://github.com/zenoamaro/react-quill#using-deltas'\n    );\n  }\n\n  shouldComponentUpdate(nextProps: ReactQuillProps, nextState: ReactQuillState) {\n    this.validateProps(nextProps);\n\n    // If the editor hasn't been instantiated yet, or the component has been\n    // regenerated, we already know we should update.\n    if (!this.editor || this.state.generation !== nextState.generation) {\n      return true;\n    }\n\n    // Handle value changes in-place\n    if ('value' in nextProps) {\n      const prevContents = this.getEditorContents();\n      const nextContents = nextProps.value ?? '';\n\n      // NOTE: Seeing that Quill is missing a way to prevent edits, we have to\n      //       settle for a hybrid between controlled and uncontrolled mode. We\n      //       can't prevent the change, but we'll still override content\n      //       whenever `value` differs from current state.\n      // NOTE: Comparing an HTML string and a Quill Delta will always trigger a\n      //       change, regardless of whether they represent the same document.\n      if (!this.isEqualValue(nextContents, prevContents)) {\n        this.setEditorContents(this.editor, nextContents);\n      }\n    }\n\n    // Handle read-only changes in-place\n    if (nextProps.readOnly !== this.props.readOnly) {\n      this.setEditorReadOnly(this.editor, nextProps.readOnly!);\n    }\n\n    // Clean and Dirty props require a render\n    return [...this.cleanProps, ...this.dirtyProps].some((prop) => {\n      return !isEqual(nextProps[prop], this.props[prop]);\n    });\n  }\n\n  shouldComponentRegenerate(nextProps: ReactQuillProps): boolean {\n    // Whenever a `dirtyProp` changes, the editor needs reinstantiation.\n    return this.dirtyProps.some((prop) => {\n      return !isEqual(nextProps[prop], this.props[prop]);\n    });\n  }\n\n  componentDidMount() {\n    this.instantiateEditor();\n    this.setEditorContents(this.editor!, this.getEditorContents());\n  }\n\n  componentWillUnmount() {\n    this.destroyEditor();\n  }\n\n  componentDidUpdate(prevProps: ReactQuillProps, prevState: ReactQuillState) {\n    // If we're changing one of the `dirtyProps`, the entire Quill Editor needs\n    // to be re-instantiated. Regenerating the editor will cause the whole tree,\n    // including the container, to be cleaned up and re-rendered from scratch.\n    // Store the contents so they can be restored later.\n    if (this.editor && this.shouldComponentRegenerate(prevProps)) {\n      const delta = this.editor.getContents();\n      const selection = this.editor.getSelection();\n      this.regenerationSnapshot = {delta, selection};\n      this.setState({generation: this.state.generation + 1});\n      this.destroyEditor();\n    }\n\n    // The component has been regenerated, so it must be re-instantiated, and\n    // its content must be restored to the previous values from the snapshot.\n    if (this.state.generation !== prevState.generation) {\n      const {delta, selection} = this.regenerationSnapshot!;\n      delete this.regenerationSnapshot;\n      this.instantiateEditor();\n      const editor = this.editor!;\n      editor.setContents(delta);\n      postpone(() => this.setEditorSelection(editor, selection));\n    }\n  }\n\n  instantiateEditor(): void {\n    if (this.editor) {\n      this.hookEditor(this.editor);\n    } else {\n      this.editor = this.createEditor(\n        this.getEditingArea(),\n        this.getEditorConfig()\n      );\n    }\n  }\n\n  destroyEditor(): void {\n    if (!this.editor) return;\n    this.unhookEditor(this.editor);\n  }\n\n  /*\n  We consider the component to be controlled if `value` is being sent in props.\n  */\n  isControlled(): boolean {\n    return 'value' in this.props;\n  }\n\n  getEditorConfig(): QuillOptions {\n    return {\n      bounds: this.props.bounds,\n      formats: this.props.formats,\n      modules: this.props.modules,\n      placeholder: this.props.placeholder,\n      readOnly: this.props.readOnly,\n      scrollingContainer: this.props.scrollingContainer,\n      tabIndex: this.props.tabIndex,\n      theme: this.props.theme,\n    };\n  }\n\n  getEditor(): Quill {\n    if (!this.editor) throw new Error('Accessing non-instantiated editor');\n    return this.editor;\n  }\n\n  /**\n  Creates an editor on the given element. The editor will be passed the\n  configuration, have its events bound,\n  */\n  createEditor(element: Element, config: QuillOptions) {\n    const editor = new Quill(element, config);\n    if (config.tabIndex != null) {\n      this.setEditorTabIndex(editor, config.tabIndex);\n    }\n    this.hookEditor(editor);\n    return editor;\n  }\n\n  hookEditor(editor: Quill) {\n    // Expose the editor on change events via a weaker, unprivileged proxy\n    // object that does not allow accidentally modifying editor state.\n    this.unprivilegedEditor = this.makeUnprivilegedEditor(editor);\n    // Using `editor-change` allows picking up silent updates, like selection\n    // changes on typing.\n    editor.on('editor-change', this.onEditorChange);\n  }\n\n  unhookEditor(editor: Quill) {\n    editor.off('editor-change', this.onEditorChange);\n  }\n\n  getEditorContents(): Value {\n    return this.value;\n  }\n\n  getEditorSelection(): Range {\n    return this.selection;\n  }\n\n  /*\n  True if the value is a Delta instance or a Delta look-alike.\n  */\n  isDelta(value: any): boolean {\n    return value && value.ops;\n  }\n\n  /*\n  Special comparison function that knows how to compare Deltas.\n  */\n  isEqualValue(value: any, nextValue: any): boolean {\n    if (this.isDelta(value) && this.isDelta(nextValue)) {\n      return isEqual(value.ops, nextValue.ops);\n    } else {\n      return isEqual(value, nextValue);\n    }\n  }\n\n  /*\n  Replace the contents of the editor, but keep the previous selection hanging\n  around so that the cursor won't move.\n  */\n  setEditorContents(editor: Quill, value: Value) {\n    this.value = value;\n    const sel = this.getEditorSelection();\n    if (typeof value === 'string') {\n      editor.setContents(editor.clipboard.convert(value));\n    } else {\n      editor.setContents(value);\n    }\n    postpone(() => this.setEditorSelection(editor, sel));\n  }\n\n  setEditorSelection(editor: Quill, range: Range) {\n    this.selection = range;\n    if (range) {\n      // Validate bounds before applying.\n      const length = editor.getLength();\n      range.index = Math.max(0, Math.min(range.index, length-1));\n      range.length = Math.max(0, Math.min(range.length, (length-1) - range.index));\n      editor.setSelection(range);\n    }\n  }\n\n  setEditorTabIndex(editor: Quill, tabIndex: number) {\n    if (editor?.scroll?.domNode) {\n      (editor.scroll.domNode as HTMLElement).tabIndex = tabIndex;\n    }\n  }\n\n  setEditorReadOnly(editor: Quill, value: boolean) {\n    if (value) {\n      editor.disable();\n    } else {\n      editor.enable();\n    }\n  }\n\n  /*\n  Returns a weaker, unprivileged proxy object that only exposes read-only\n  accessors found on the editor instance, without any state-modifying methods.\n  */\n  makeUnprivilegedEditor(editor: Quill) {\n    const e = editor;\n    return {\n      getHTML:      () => e.root.innerHTML,\n      getLength:    e.getLength.bind(e),\n      getText:      e.getText.bind(e),\n      getContents:  e.getContents.bind(e),\n      getSelection: e.getSelection.bind(e),\n      getBounds:    e.getBounds.bind(e),\n    };\n  }\n\n  getEditingArea(): Element {\n    if (!this.editingArea) {\n      throw new Error('Instantiating on missing editing area');\n    }\n    const element = ReactDOM.findDOMNode(this.editingArea);\n    if (!element) {\n      throw new Error('Cannot find element for editing area');\n    }\n    if (element.nodeType === 3) {\n      throw new Error('Editing area cannot be a text node');\n    }\n    return element as Element;\n  }\n\n  /*\n  Renders an editor area, unless it has been provided one to clone.\n  */\n  renderEditingArea(): JSX.Element {\n    const {children, preserveWhitespace} = this.props;\n    const {generation} = this.state;\n\n    const properties = {\n      key: generation,\n      ref: (instance: React.ReactInstance | null) => {\n        this.editingArea = instance\n      },\n    };\n\n    if (React.Children.count(children)) {\n      return React.cloneElement(\n        React.Children.only(children)!,\n        properties\n      );\n    }\n\n    return preserveWhitespace ?\n      <pre {...properties}/> :\n      <div {...properties}/>;\n  }\n\n  render() {\n    return (\n      <div\n        id={this.props.id}\n        style={this.props.style}\n        key={this.state.generation}\n        className={`quill ${this.props.className ?? ''}`}\n        onKeyPress={this.props.onKeyPress}\n        onKeyDown={this.props.onKeyDown}\n        onKeyUp={this.props.onKeyUp}\n      >\n        {this.renderEditingArea()}\n      </div>\n    );\n  }\n\n  onEditorChange = (\n    eventName: 'text-change' | 'selection-change',\n    rangeOrDelta: Range | DeltaStatic,\n    oldRangeOrDelta: Range | DeltaStatic,\n    source: Sources,\n  ) => {\n    if (eventName === 'text-change') {\n      this.onEditorChangeText?.(\n        this.editor!.root.innerHTML,\n        rangeOrDelta as DeltaStatic,\n        source,\n        this.unprivilegedEditor!\n      );\n    } else if (eventName === 'selection-change') {\n      this.onEditorChangeSelection?.(\n        rangeOrDelta as RangeStatic,\n        source,\n        this.unprivilegedEditor!\n      );\n    }\n  };\n\n  onEditorChangeText(\n    value: string,\n    delta: DeltaStatic,\n    source: Sources,\n    editor: UnprivilegedEditor,\n  ): void {\n    if (!this.editor) return;\n\n    // We keep storing the same type of value as what the user gives us,\n    // so that value comparisons will be more stable and predictable.\n    const nextContents = this.isDelta(this.value)\n      ? editor.getContents()\n      : editor.getHTML();\n\n    if (nextContents !== this.getEditorContents()) {\n      // Taint this `delta` object, so we can recognize whether the user\n      // is trying to send it back as `value`, preventing a likely loop.\n      this.lastDeltaChangeSet = delta;\n\n      this.value = nextContents;\n      this.props.onChange?.(value, delta, source, editor);\n    }\n  }\n\n  onEditorChangeSelection(\n    nextSelection: RangeStatic,\n    source: Sources,\n    editor: UnprivilegedEditor,\n  ): void {\n    if (!this.editor) return;\n    const currentSelection = this.getEditorSelection();\n    const hasGainedFocus = !currentSelection && nextSelection;\n    const hasLostFocus = currentSelection && !nextSelection;\n\n    if (isEqual(nextSelection, currentSelection)) return;\n\n    this.selection = nextSelection;\n    this.props.onChangeSelection?.(nextSelection, source, editor);\n\n    if (hasGainedFocus) {\n      this.props.onFocus?.(nextSelection, source, editor);\n    } else if (hasLostFocus) {\n      this.props.onBlur?.(currentSelection, source, editor);\n    }\n  }\n\n  focus(): void {\n    if (!this.editor) return;\n    this.editor.focus();\n  }\n\n  blur(): void {\n    if (!this.editor) return;\n    this.selection = null;\n    this.editor.blur();\n  }\n}\n\n/*\nSmall helper to execute a function in the next micro-tick.\n*/\nfunction postpone(fn: (value: void) => void) {\n  Promise.resolve().then(fn);\n}\n\n// Compatibility Export to avoid `require(...).default` on CommonJS.\n// See: https://github.com/Microsoft/TypeScript/issues/2719\nexport = ReactQuill;\n"]}