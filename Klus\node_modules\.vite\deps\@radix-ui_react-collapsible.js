"use client";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Content,
  Root,
  Trigger,
  createCollapsibleScope
} from "./chunk-7BXAAT4Y.js";
import "./chunk-T76HMGKU.js";
import "./chunk-ZT7XZNYZ.js";
import "./chunk-XFXV3TVQ.js";
import "./chunk-OD433RWB.js";
import "./chunk-ZIOYVGC2.js";
import "./chunk-QAIB2WTN.js";
import "./chunk-S5OO3TVJ.js";
import "./chunk-NFC5BX5N.js";
import "./chunk-JI7DTWDM.js";
import "./chunk-7BUGFXDR.js";
import "./chunk-CMM6OKGN.js";
import "./chunk-OL46QLBJ.js";
export {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Content,
  Root,
  Trigger,
  createCollapsibleScope
};
//# sourceMappingURL=@radix-ui_react-collapsible.js.map
