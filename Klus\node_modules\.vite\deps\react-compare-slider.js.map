{"version": 3, "sources": ["../../react-compare-slider/src/ReactCompareSlider.tsx", "../../react-compare-slider/src/Container.tsx", "../../react-compare-slider/src/ReactCompareSliderHandle.tsx", "../../react-compare-slider/src/utils.ts", "../../react-compare-slider/src/ReactCompareSliderImage.tsx", "../../react-compare-slider/src/useReactCompareSliderRef.ts"], "sourcesContent": ["import React, {\n  forwardRef,\n  useCallback,\n  useEffect,\n  useImperativeHandle,\n  useRef,\n  useState,\n} from 'react';\nimport type { CSSProperties, ReactElement } from 'react';\n\nimport { ContainerClip, ContainerHandle } from './Container';\nimport { ReactCompareSliderHandle } from './ReactCompareSliderHandle';\nimport type { ReactCompareSliderDetailedProps, UseReactCompareSliderRefReturn } from './types';\nimport type { UseResizeObserverHandlerProps } from './utils';\nimport { usePrevious } from './utils';\nimport { KeyboardEventKeys, useEventListener, useResizeObserver } from './utils';\n\n/** Properties for internal `updateInternalPosition` callback. */\ninterface UpdateInternalPositionProps {\n  /** X coordinate to update to (landscape). */\n  x: number;\n  /** Y coordinate to update to (portrait). */\n  y: number;\n  /** Whether to calculate using page X and Y offsets (required for pointer events). */\n  isOffset?: boolean;\n}\n\nconst EVENT_PASSIVE_PARAMS = { capture: false, passive: true };\nconst EVENT_CAPTURE_PARAMS = { capture: true, passive: false };\n\n/**\n * Handler for the `handle` container element.\n */\nconst handleContainerClick = (ev: PointerEvent): void => {\n  ev.preventDefault();\n  (ev.currentTarget as HTMLButtonElement).focus();\n};\n\n/** Root Comparison slider. */\nexport const ReactCompareSlider = forwardRef<\n  UseReactCompareSliderRefReturn,\n  ReactCompareSliderDetailedProps\n>(\n  (\n    {\n      boundsPadding = 0,\n      browsingContext = globalThis,\n      changePositionOnHover = false,\n      disabled = false,\n      handle,\n      itemOne,\n      itemTwo,\n      keyboardIncrement = '5%',\n      onlyHandleDraggable = false,\n      onPositionChange,\n      portrait = false,\n      position = 50,\n      style,\n      transition,\n      ...props\n    },\n    ref,\n  ): ReactElement => {\n    /** DOM node of the root element. */\n    const rootContainerRef = useRef<HTMLDivElement>(null);\n    /** DOM node of the item that is clipped. */\n    const clipContainerRef = useRef<HTMLDivElement>(null);\n    /** DOM node of the handle container. */\n    const handleContainerRef = useRef<HTMLButtonElement>(null);\n    /** Current position as a percentage value (initially negative to sync bounds on mount). */\n    const internalPosition = useRef(position);\n    /** Whether user is currently dragging. */\n    const [isDragging, setIsDragging] = useState(false);\n    /** Whether the `transition` property can be applied. */\n    const [canTransition, setCanTransition] = useState(true);\n    /** Whether component has a `window` event binding. */\n    const hasBrowsingContextBinding = useRef(false);\n    /** Target container for pointer events. */\n    const [interactiveTarget, setInteractiveTarget] = useState<HTMLElement | null>();\n    /** The `position` value at *previous* render. */\n    const previousPosition = usePrevious(position);\n\n    /** Sync the internal position and trigger position change callback if defined. */\n    const updateInternalPosition = useCallback(\n      function updateInternal({ x, y, isOffset }: UpdateInternalPositionProps) {\n        const rootElement = rootContainerRef.current as HTMLDivElement;\n        const handleElement = handleContainerRef.current as HTMLButtonElement;\n        const clipElement = clipContainerRef.current as HTMLDivElement;\n        const { width, height, left, top } = rootElement.getBoundingClientRect();\n\n        // Early out when component has zero bounds.\n        if (width === 0 || height === 0) {\n          return;\n        }\n\n        const pixelPosition = portrait\n          ? isOffset\n            ? y - top - browsingContext.scrollY\n            : y\n          : isOffset\n          ? x - left - browsingContext.scrollX\n          : x;\n\n        /** Next position as percentage. */\n        const nextPosition = Math.min(\n          Math.max((pixelPosition / (portrait ? height : width)) * 100, 0),\n          100,\n        );\n\n        const zoomScale = portrait\n          ? height / (rootElement.offsetHeight || 1)\n          : width / (rootElement.offsetWidth || 1);\n\n        const boundsPaddingPercentage =\n          ((boundsPadding * zoomScale) / (portrait ? height : width)) * 100;\n\n        const nextPositionWithBoundsPadding = Math.min(\n          Math.max(nextPosition, boundsPaddingPercentage * zoomScale),\n          100 - boundsPaddingPercentage * zoomScale,\n        );\n\n        internalPosition.current = nextPosition;\n        handleElement.setAttribute('aria-valuenow', `${Math.round(internalPosition.current)}`);\n        handleElement.style.top = portrait ? `${nextPositionWithBoundsPadding}%` : '0';\n        handleElement.style.left = portrait ? '0' : `${nextPositionWithBoundsPadding}%`;\n        clipElement.style.clipPath = portrait\n          ? `inset(${nextPositionWithBoundsPadding}% 0 0 0)`\n          : `inset(0 0 0 ${nextPositionWithBoundsPadding}%)`;\n\n        if (onPositionChange) {\n          onPositionChange(internalPosition.current);\n        }\n      },\n      [boundsPadding, onPositionChange, portrait, browsingContext],\n    );\n\n    // Update internal position when other user controllable props change.\n    useEffect(() => {\n      const { width, height } = (\n        rootContainerRef.current as HTMLDivElement\n      ).getBoundingClientRect();\n\n      // Use current internal position if `position` hasn't changed.\n      const nextPosition = position === previousPosition ? internalPosition.current : position;\n\n      updateInternalPosition({\n        x: (width / 100) * nextPosition,\n        y: (height / 100) * nextPosition,\n      });\n    }, [boundsPadding, position, portrait, previousPosition, updateInternalPosition]);\n\n    /** Handle mouse/touch down. */\n    const handlePointerDown = useCallback(\n      (ev: PointerEvent) => {\n        ev.preventDefault();\n\n        // Only handle left mouse button (touch events also use 0).\n        if (disabled || ev.button !== 0) return;\n\n        updateInternalPosition({ isOffset: true, x: ev.pageX, y: ev.pageY });\n        setIsDragging(true);\n        setCanTransition(true);\n      },\n      [disabled, updateInternalPosition],\n    );\n\n    /** Handle mouse/touch move. */\n    const handlePointerMove = useCallback(\n      function moveCall(ev: PointerEvent) {\n        updateInternalPosition({ isOffset: true, x: ev.pageX, y: ev.pageY });\n        setCanTransition(false);\n      },\n      [updateInternalPosition],\n    );\n\n    /** Handle mouse/touch up. */\n    const handlePointerUp = useCallback(() => {\n      setIsDragging(false);\n      setCanTransition(true);\n    }, []);\n\n    /** Resync internal position on resize. */\n    const handleResize: (resizeProps: UseResizeObserverHandlerProps) => void = useCallback(\n      ({ width, height }) => {\n        const { width: scaledWidth, height: scaledHeight } = (\n          rootContainerRef.current as HTMLDivElement\n        ).getBoundingClientRect();\n\n        updateInternalPosition({\n          x: ((width / 100) * internalPosition.current * scaledWidth) / width,\n          y: ((height / 100) * internalPosition.current * scaledHeight) / height,\n        });\n      },\n      [updateInternalPosition],\n    );\n\n    /** Handle keyboard movment. */\n    const handleKeydown = useCallback(\n      (ev: KeyboardEvent) => {\n        if (!Object.values(KeyboardEventKeys).includes(ev.key as KeyboardEventKeys)) {\n          return;\n        }\n\n        ev.preventDefault();\n        setCanTransition(true);\n\n        const { top, left } = (\n          handleContainerRef.current as HTMLButtonElement\n        ).getBoundingClientRect();\n\n        const { width, height } = (\n          rootContainerRef.current as HTMLDivElement\n        ).getBoundingClientRect();\n\n        const isPercentage = typeof keyboardIncrement === 'string';\n        const incrementPercentage = isPercentage\n          ? parseFloat(keyboardIncrement)\n          : (keyboardIncrement / width) * 100;\n\n        const isIncrement = portrait\n          ? ev.key === KeyboardEventKeys.ARROW_LEFT || ev.key === KeyboardEventKeys.ARROW_DOWN\n          : ev.key === KeyboardEventKeys.ARROW_RIGHT || ev.key === KeyboardEventKeys.ARROW_UP;\n\n        const nextPosition = Math.min(\n          Math.max(\n            isIncrement\n              ? internalPosition.current + incrementPercentage\n              : internalPosition.current - incrementPercentage,\n            0,\n          ),\n          100,\n        );\n\n        updateInternalPosition({\n          x: portrait ? left : (width * nextPosition) / 100,\n          y: portrait ? (height * nextPosition) / 100 : top,\n        });\n      },\n      [keyboardIncrement, portrait, updateInternalPosition],\n    );\n\n    // Set target container for pointer events.\n    useEffect(() => {\n      setInteractiveTarget(\n        onlyHandleDraggable ? handleContainerRef.current : rootContainerRef.current,\n      );\n    }, [onlyHandleDraggable]);\n\n    // Handle hover events on the container.\n    useEffect(() => {\n      const containerRef = rootContainerRef.current as HTMLDivElement;\n\n      const handlePointerLeave = (): void => {\n        if (isDragging) return;\n        handlePointerUp();\n      };\n\n      if (changePositionOnHover) {\n        containerRef.addEventListener('pointermove', handlePointerMove, EVENT_PASSIVE_PARAMS);\n        containerRef.addEventListener('pointerleave', handlePointerLeave, EVENT_PASSIVE_PARAMS);\n      }\n\n      return () => {\n        containerRef.removeEventListener('pointermove', handlePointerMove);\n        containerRef.removeEventListener('pointerleave', handlePointerLeave);\n      };\n    }, [changePositionOnHover, handlePointerMove, handlePointerUp, isDragging]);\n\n    // Allow drag outside of container while pointer is still down.\n    useEffect(() => {\n      if (isDragging && !hasBrowsingContextBinding.current) {\n        browsingContext.addEventListener('pointermove', handlePointerMove, EVENT_PASSIVE_PARAMS);\n        browsingContext.addEventListener('pointerup', handlePointerUp, EVENT_PASSIVE_PARAMS);\n        hasBrowsingContextBinding.current = true;\n      }\n\n      return (): void => {\n        if (hasBrowsingContextBinding.current) {\n          browsingContext.removeEventListener('pointermove', handlePointerMove);\n          browsingContext.removeEventListener('pointerup', handlePointerUp);\n          hasBrowsingContextBinding.current = false;\n        }\n      };\n    }, [handlePointerMove, handlePointerUp, isDragging, browsingContext]);\n\n    useImperativeHandle(\n      ref,\n      () => {\n        return {\n          rootContainer: rootContainerRef.current,\n          handleContainer: handleContainerRef.current,\n          setPosition(nextPosition): void {\n            const { width, height } = (\n              rootContainerRef.current as HTMLDivElement\n            ).getBoundingClientRect();\n\n            updateInternalPosition({\n              x: (width / 100) * nextPosition,\n              y: (height / 100) * nextPosition,\n            });\n          },\n        };\n      },\n      [updateInternalPosition],\n    );\n\n    // Bind resize observer to container.\n    useResizeObserver(rootContainerRef, handleResize);\n\n    useEventListener(\n      'keydown',\n      handleKeydown,\n      handleContainerRef.current as HTMLButtonElement,\n      EVENT_CAPTURE_PARAMS,\n    );\n\n    useEventListener(\n      'click',\n      handleContainerClick,\n      handleContainerRef.current as HTMLButtonElement,\n      EVENT_CAPTURE_PARAMS,\n    );\n\n    useEventListener(\n      'pointerdown',\n      handlePointerDown,\n      interactiveTarget as HTMLDivElement,\n      EVENT_CAPTURE_PARAMS,\n    );\n\n    // Use custom handle if requested.\n    const Handle = handle || <ReactCompareSliderHandle disabled={disabled} portrait={portrait} />;\n    const appliedTransition = canTransition ? transition : undefined;\n\n    const rootStyle: CSSProperties = {\n      position: 'relative',\n      display: 'flex',\n      overflow: 'hidden',\n      cursor: isDragging ? (portrait ? 'ns-resize' : 'ew-resize') : undefined,\n      touchAction: 'none',\n      userSelect: 'none',\n      KhtmlUserSelect: 'none',\n      msUserSelect: 'none',\n      MozUserSelect: 'none',\n      WebkitUserSelect: 'none',\n      ...style,\n    };\n\n    return (\n      <div {...props} ref={rootContainerRef} style={rootStyle} data-rcs=\"root\">\n        {itemOne}\n        <ContainerClip ref={clipContainerRef} transition={appliedTransition}>\n          {itemTwo}\n        </ContainerClip>\n\n        <ContainerHandle\n          disabled={disabled}\n          portrait={portrait}\n          position={Math.round(internalPosition.current)}\n          ref={handleContainerRef}\n          transition={appliedTransition}\n        >\n          {Handle}\n        </ContainerHandle>\n      </div>\n    );\n  },\n);\n\nReactCompareSlider.displayName = 'ReactCompareSlider';\n", "import React, { forwardRef } from 'react';\nimport type { CSSProperties, HTMLProps, ReactElement } from 'react';\n\nimport type { ReactCompareSliderCommonProps } from './types';\n\n/** Container for clipped item. */\nexport const ContainerClip = forwardRef<\n  HTMLDivElement,\n  HTMLProps<HTMLDivElement> & Pick<ReactCompareSliderCommonProps, 'transition'>\n>(({ transition, ...props }, ref): ReactElement => {\n  const style: CSSProperties = {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    transition: transition ? `clip-path ${transition}` : undefined,\n    userSelect: 'none',\n    willChange: 'clip-path, transition',\n    KhtmlUserSelect: 'none',\n    MozUserSelect: 'none',\n    WebkitUserSelect: 'none',\n  };\n\n  return <div {...props} style={style} data-rcs=\"clip-item\" ref={ref} />;\n});\n\nContainerClip.displayName = 'ContainerClip';\n\n/** Container to control the handle's position. */\nexport const ContainerHandle = forwardRef<\n  HTMLButtonElement,\n  HTMLProps<HTMLButtonElement> & ReactCompareSliderCommonProps\n>(({ children, disabled, portrait, position, transition }, ref): ReactElement => {\n  const targetAxis = portrait ? 'top' : 'left';\n\n  const style: CSSProperties = {\n    position: 'absolute',\n    top: 0,\n    width: portrait ? '100%' : undefined,\n    height: portrait ? undefined : '100%',\n    background: 'none',\n    border: 0,\n    padding: 0,\n    pointerEvents: 'all',\n    appearance: 'none',\n    WebkitAppearance: 'none',\n    MozAppearance: 'none',\n    outline: 0,\n    transform: portrait ? `translate3d(0, -50% ,0)` : `translate3d(-50%, 0, 0)`,\n    transition: transition ? `${targetAxis} ${transition}` : undefined,\n  };\n\n  return (\n    <button\n      ref={ref}\n      aria-label=\"Drag to move or focus and use arrow keys\"\n      aria-orientation={portrait ? 'vertical' : 'horizontal'}\n      aria-valuemin={0}\n      aria-valuemax={100}\n      aria-valuenow={position}\n      data-rcs=\"handle-container\"\n      disabled={disabled}\n      role=\"slider\"\n      style={style}\n    >\n      {children}\n    </button>\n  );\n});\n\nContainerHandle.displayName = 'ThisHandleContainer';\n", "import React from 'react';\nimport type { CSSProperties, FC, HtmlHTMLAttributes, ReactElement } from 'react';\n\nimport type { ReactCompareSliderCommonProps } from './types';\n\ninterface ThisArrowProps {\n  /** Whether to flip the arrow direction. */\n  flip?: boolean;\n}\n\nconst ThisArrow: FC<ThisArrowProps> = ({ flip }) => {\n  const style: CSSProperties = {\n    width: 0,\n    height: 0,\n    borderTop: '8px solid transparent',\n    borderRight: '10px solid',\n    borderBottom: '8px solid transparent',\n    transform: flip ? 'rotate(180deg)' : undefined,\n  };\n\n  return <div className=\"__rcs-handle-arrow\" style={style} />;\n};\n\n/** Props for `ReactCompareSliderHandle`. */\nexport interface ReactCompareSliderHandleProps\n  extends Pick<ReactCompareSliderCommonProps, 'disabled' | 'portrait'> {\n  /** Optional styles for handle the button. */\n  buttonStyle?: CSSProperties;\n  /** Optional styles for lines either side of the handle button. */\n  linesStyle?: CSSProperties;\n  /** Optional styles for the handle root. */\n  style?: CSSProperties;\n}\n\n/** Default `handle`. */\nexport const ReactCompareSliderHandle: FC<\n  ReactCompareSliderHandleProps & HtmlHTMLAttributes<HTMLDivElement>\n> = ({\n  className = '__rcs-handle-root',\n  disabled,\n  buttonStyle,\n  linesStyle,\n  portrait,\n  style,\n  ...props\n}): ReactElement => {\n  const _style: CSSProperties = {\n    display: 'flex',\n    flexDirection: portrait ? 'row' : 'column',\n    placeItems: 'center',\n    height: '100%',\n    cursor: disabled ? 'not-allowed' : portrait ? 'ns-resize' : 'ew-resize',\n    pointerEvents: 'none',\n    color: '#fff',\n    ...style,\n  };\n\n  const _linesStyle: CSSProperties = {\n    flexGrow: 1,\n    height: portrait ? 2 : '100%',\n    width: portrait ? '100%' : 2,\n    backgroundColor: 'currentColor',\n    pointerEvents: 'auto',\n    boxShadow: '0 0 4px rgba(0,0,0,.5)',\n    ...linesStyle,\n  };\n\n  const _buttonStyle: CSSProperties = {\n    display: 'grid',\n    gridAutoFlow: 'column',\n    gap: 8,\n    placeContent: 'center',\n    flexShrink: 0,\n    width: 56,\n    height: 56,\n    borderRadius: '50%',\n    borderStyle: 'solid',\n    borderWidth: 2,\n    pointerEvents: 'auto',\n    backdropFilter: 'blur(7px)',\n    WebkitBackdropFilter: 'blur(7px)', // For Safari.\n    backgroundColor: 'rgba(0, 0, 0, 0.125)',\n    boxShadow: '0 0 4px rgba(0,0,0,.35)',\n    transform: portrait ? 'rotate(90deg)' : undefined,\n    ...buttonStyle,\n  };\n\n  return (\n    <div {...props} className={className} style={_style}>\n      <div className=\"__rcs-handle-line\" style={_linesStyle} />\n      <div className=\"__rcs-handle-button\" style={_buttonStyle}>\n        <ThisArrow />\n        <ThisArrow flip />\n      </div>\n      <div className=\"__rcs-handle-line\" style={_linesStyle} />\n    </div>\n  );\n};\n", "import type { CSSProperties, RefObject } from 'react';\nimport { useCallback, useEffect, useLayoutEffect, useRef } from 'react';\n\n/** Keyboard `key` events to trigger slider movement. */\nexport enum KeyboardEventKeys {\n  ARROW_LEFT = 'ArrowLeft',\n  ARROW_RIGHT = 'ArrowRight',\n  ARROW_UP = 'ArrowUp',\n  ARROW_DOWN = 'ArrowDown',\n}\n\n/**\n * Stand-alone CSS utility to make replaced elements (`img`, `video`, etc.) fit their container.\n */\nexport const styleFitContainer = ({\n  boxSizing = 'border-box',\n  objectFit = 'cover',\n  objectPosition = 'center center',\n  ...props\n}: CSSProperties = {}): CSSProperties => ({\n  display: 'block',\n  width: '100%',\n  height: '100%',\n  maxWidth: '100%',\n  boxSizing,\n  objectFit,\n  objectPosition,\n  ...props,\n});\n\n/** Store the previous supplied value. */\nexport const usePrevious = <T>(value: T): T => {\n  const ref = useRef<T>(value);\n\n  useEffect(() => {\n    ref.current = value;\n  });\n\n  return ref.current;\n};\n\n/**\n * Event listener binding hook.\n * @param eventName      - Event to bind to.\n * @param handler        - Callback handler.\n * @param element        - Element to bind to.\n * @param handlerOptions - Event handler options.\n */\nexport const useEventListener = (\n  eventName: EventListener['name'],\n  handler: EventListener['caller'],\n  element: EventTarget,\n  handlerOptions: AddEventListenerOptions,\n): void => {\n  const savedHandler = useRef<EventListener['caller']>();\n\n  useEffect(() => {\n    savedHandler.current = handler;\n  }, [handler]);\n\n  useEffect(() => {\n    // Make sure element supports addEventListener.\n    if (!(element && element.addEventListener)) return;\n\n    // Create event listener that calls handler function stored in ref.\n    const eventListener: EventListener = (event) =>\n      savedHandler.current && savedHandler.current(event);\n\n    element.addEventListener(eventName, eventListener, handlerOptions);\n\n    return (): void => {\n      element.removeEventListener(eventName, eventListener, handlerOptions);\n    };\n  }, [eventName, element, handlerOptions]);\n};\n\n/**\n * Conditionally use `useLayoutEffect` for client *or* `useEffect` for SSR.\n * @see https://github.com/reduxjs/react-redux/blob/89a86805f2fcf9e8fbd2d1dae345ec791de4a71f/src/utils/useIsomorphicLayoutEffect.ts\n */\nconst useIsomorphicLayoutEffect =\n  typeof window !== 'undefined' &&\n  typeof window.document !== 'undefined' &&\n  typeof window.document.createElement !== 'undefined'\n    ? useLayoutEffect\n    : useEffect;\n\n/** Params passed to `useResizeObserver` `handler` function. */\nexport type UseResizeObserverHandlerProps = DOMRect;\n\n/**\n * Bind resize observer callback to element.\n * @param ref       - Element to bind to.\n * @param handler   - Callback for handling entry's bounding rect.\n */\nexport const useResizeObserver = (\n  ref: RefObject<Element>,\n  handler: (entry: UseResizeObserverHandlerProps) => void,\n): void => {\n  const observer = useRef<ResizeObserver>();\n\n  const observe = useCallback(() => {\n    if (ref.current && observer.current) observer.current.observe(ref.current);\n  }, [ref]);\n\n  // Bind/rebind observer when `handler` changes.\n  useIsomorphicLayoutEffect(() => {\n    observer.current = new ResizeObserver(([entry]) => handler(entry!.contentRect));\n    observe();\n\n    return (): void => {\n      if (observer.current) observer.current.disconnect();\n    };\n  }, [handler, observe]);\n};\n", "import React from 'react';\nimport type { CSSProperties, ImgHTMLAttributes, ReactElement } from 'react';\nimport { forwardRef } from 'react';\n\nimport { styleFitContainer } from './utils';\n\n/** Props for `ReactCompareSliderImage`. */\nexport type ReactCompareSliderImageProps = ImgHTMLAttributes<HTMLImageElement>;\n\n/** `Img` element with defaults from `styleFitContainer` applied. */\nexport const ReactCompareSliderImage = forwardRef<HTMLImageElement, ReactCompareSliderImageProps>(\n  ({ style, ...props }, ref): ReactElement => {\n    const rootStyle: CSSProperties = styleFitContainer(style);\n\n    return <img ref={ref} {...props} style={rootStyle} data-rcs=\"image\" />;\n  },\n);\n\nReactCompareSliderImage.displayName = 'ReactCompareSliderImage';\n", "import type { MutableRefObject } from 'react';\nimport { useRef } from 'react';\n\nimport type { UseReactCompareSliderRefReturn } from './types';\n\n/**\n * Control the position and access or modify the DOM elements of the slider.\n */\nexport const useReactCompareSliderRef = (): MutableRefObject<UseReactCompareSliderRefReturn> =>\n  useRef<UseReactCompareSliderRefReturn>({\n    rootContainer: null,\n    handleContainer: null,\n    setPosition: () =>\n      // eslint-disable-next-line no-console\n      console.warn(\n        '[react-compare-slider] `setPosition` cannot be used until the component has mounted.',\n      ),\n  });\n"], "mappings": ";;;;;;;;;;;;AAAA,mBAOO;ACPP,IAAAA,gBAAkC;AAwBzB,yBAAA;ACJA,IAAAC,sBAAA;ACnBT,IAAAD,gBAAgE;AH0UnC,IAAAC,sBAAA;AIzU7B,IAAAD,gBAA2B;AAYhB,IAAAC,sBAAA;ACbX,IAAAD,gBAAuB;AJKhB,IAAME,QAAgBC,cAAAA,YAG3B,CAAC,EAAE,YAAAC,GAAY,GAAGC,EAAM,GAAGC,MAAsB;AACjD,MAAMC,IAAuB,EAC3B,UAAU,YACV,KAAK,GACL,MAAM,GACN,OAAO,QACP,QAAQ,QACR,YAAYH,IAAa,aAAaA,CAAU,KAAK,QACrD,YAAY,QACZ,YAAY,yBACZ,iBAAiB,QACjB,eAAe,QACf,kBAAkB,OACpB;AAEA,aAAOI,mBAAAA,KAAC,OAAA,EAAK,GAAGH,GAAO,OAAOE,GAAO,YAAS,aAAY,KAAKD,EAAAA,CAAK;AACtE,CAAC;AAEDJ,EAAc,cAAc;AAGrB,IAAMO,QAAkBN,cAAAA,YAG7B,CAAC,EAAE,UAAAO,GAAU,UAAAC,GAAU,UAAAC,GAAU,UAAAC,GAAU,YAAAT,EAAW,GAAGE,MAAsB;AAG/E,MAAMC,IAAuB,EAC3B,UAAU,YACV,KAAK,GACL,OAAOK,IAAW,SAAS,QAC3B,QAAQA,IAAW,SAAY,QAC/B,YAAY,QACZ,QAAQ,GACR,SAAS,GACT,eAAe,OACf,YAAY,QACZ,kBAAkB,QAClB,eAAe,QACf,SAAS,GACT,WAAWA,IAAW,4BAA4B,2BAClD,YAAYR,IAAa,GAhBRQ,IAAW,QAAQ,MAgBE,IAAIR,CAAU,KAAK,OAC3D;AAEA,aACEI,mBAAAA,KAAC,UAAA,EACC,KAAKF,GACL,cAAW,4CACX,oBAAkBM,IAAW,aAAa,cAC1C,iBAAe,GACf,iBAAe,KACf,iBAAeC,GACf,YAAS,oBACT,UAAUF,GACV,MAAK,UACL,OAAOJ,GAEN,UAAAG,EAAAA,CACH;AAEJ,CAAC;AAEDD,EAAgB,cAAc;AC7D9B,IAAMK,KAAgC,CAAC,EAAE,MAAAC,EAAK,UAUrCP,oBAAAA,KAAC,OAAA,EAAI,WAAU,sBAAqB,OATd,EAC3B,OAAO,GACP,QAAQ,GACR,WAAW,yBACX,aAAa,cACb,cAAc,yBACd,WAAWO,IAAO,mBAAmB,OACvC,EAAA,CAEyD;AAV3D,IAyBaC,IAET,CAAC,EACH,WAAAC,IAAY,qBACZ,UAAAN,GACA,aAAAO,GACA,YAAAC,GACA,UAAAP,GACA,OAAAL,GACA,GAAGF,EACL,MAAoB;AAClB,MAAMe,IAAwB,EAC5B,SAAS,QACT,eAAeR,IAAW,QAAQ,UAClC,YAAY,UACZ,QAAQ,QACR,QAAQD,IAAW,gBAAgBC,IAAW,cAAc,aAC5D,eAAe,QACf,OAAO,QACP,GAAGL,EACL,GAEMc,IAA6B,EACjC,UAAU,GACV,QAAQT,IAAW,IAAI,QACvB,OAAOA,IAAW,SAAS,GAC3B,iBAAiB,gBACjB,eAAe,QACf,WAAW,0BACX,GAAGO,EACL,GAEMG,IAA8B,EAClC,SAAS,QACT,cAAc,UACd,KAAK,GACL,cAAc,UACd,YAAY,GACZ,OAAO,IACP,QAAQ,IACR,cAAc,OACd,aAAa,SACb,aAAa,GACb,eAAe,QACf,gBAAgB,aAChB,sBAAsB,aACtB,iBAAiB,wBACjB,WAAW,2BACX,WAAWV,IAAW,kBAAkB,QACxC,GAAGM,EACL;AAEA,aACEK,oBAAAA,MAAC,OAAA,EAAK,GAAGlB,GAAO,WAAWY,GAAW,OAAOG,GAC3C,UAAA,KAAAZ,oBAAAA,KAAC,OAAA,EAAI,WAAU,qBAAoB,OAAOa,EAAAA,CAAa,OACvDE,oBAAAA,MAAC,OAAA,EAAI,WAAU,uBAAsB,OAAOD,GAC1C,UAAA,KAAAd,oBAAAA,KAACM,IAAA,CAAA,CAAU,OACXN,oBAAAA,KAACM,IAAA,EAAU,MAAI,KAAA,CAAC,CAAA,EAAA,CAClB,OACAN,oBAAAA,KAAC,OAAA,EAAI,WAAU,qBAAoB,OAAOa,EAAAA,CAAa,CAAA,EAAA,CACzD;AAEJ;AC7FO,IAAKG,KAAAA,QACVA,EAAA,aAAa,aACbA,EAAA,cAAc,cACdA,EAAA,WAAW,WACXA,EAAA,aAAa,aAJHA,IAAAA,KAAA,CAAA,CAAA;AAAL,IAUMC,IAAoB,CAAC,EAChC,WAAAC,IAAY,cACZ,WAAAC,IAAY,SACZ,gBAAAC,IAAiB,iBACjB,GAAGvB,EACL,IAAmB,CAAC,OAAsB,EACxC,SAAS,SACT,OAAO,QACP,QAAQ,QACR,UAAU,QACV,WAAAqB,GACA,WAAAC,GACA,gBAAAC,GACA,GAAGvB,EACL;AAxBO,IA2BMwB,KAAkBC,OAAgB;AAC7C,MAAMxB,QAAMyB,cAAAA,QAAUD,CAAK;AAE3B,aAAAE,cAAAA,WAAU,MAAM;AACd1B,MAAI,UAAUwB;EAChB,CAAC,GAEMxB,EAAI;AACb;AAnCO,IA4CM2B,IAAmB,CAC9BC,GACAC,GACAC,GACAC,MACS;AACT,MAAMC,QAAeP,cAAAA,QAAgC;AAErDC,oBAAAA,WAAU,MAAM;AACdM,MAAa,UAAUH;EACzB,GAAG,CAACA,CAAO,CAAC,OAEZH,cAAAA,WAAU,MAAM;AAEd,QAAI,EAAEI,KAAWA,EAAQ,kBAAmB;AAG5C,QAAMG,IAAgCC,OACpCF,EAAa,WAAWA,EAAa,QAAQE,CAAK;AAEpD,WAAAJ,EAAQ,iBAAiBF,GAAWK,GAAeF,CAAc,GAE1D,MAAY;AACjBD,QAAQ,oBAAoBF,GAAWK,GAAeF,CAAc;IACtE;EACF,GAAG,CAACH,GAAWE,GAASC,CAAc,CAAC;AACzC;AAtEO,IA4EDI,KACJ,OAAO,SAAW,OAClB,OAAO,OAAO,WAAa,OAC3B,OAAO,OAAO,SAAS,gBAAkB,MACrCC,cAAAA,kBACAV,cAAAA;AAjFC,IA2FMW,KAAoB,CAC/BrC,GACA6B,MACS;AACT,MAAMS,QAAWb,cAAAA,QAAuB,GAElCc,QAAUC,cAAAA,aAAY,MAAM;AAC5BxC,MAAI,WAAWsC,EAAS,WAASA,EAAS,QAAQ,QAAQtC,EAAI,OAAO;EAC3E,GAAG,CAACA,CAAG,CAAC;AAGRmC,KAA0B,OACxBG,EAAS,UAAU,IAAI,eAAe,CAAC,CAACG,CAAK,MAAMZ,EAAQY,EAAO,WAAW,CAAC,GAC9EF,EAAQ,GAED,MAAY;AACbD,MAAS,WAASA,EAAS,QAAQ,WAAW;EACpD,IACC,CAACT,GAASU,CAAO,CAAC;AACvB;AHvFA,IAAMG,IAAuB,EAAE,SAAS,OAAO,SAAS,KAAK;AAA7D,IACMC,IAAuB,EAAE,SAAS,MAAM,SAAS,MAAM;AAD7D,IAMMC,KAAwBC,OAA2B;AACvDA,IAAG,eAAe,GACjBA,EAAG,cAAoC,MAAM;AAChD;AATA,IAYaC,SAAqBjD,aAAAA,YAIhC,CACE,EACE,eAAAkD,IAAgB,GAChB,iBAAAC,IAAkB,YAClB,uBAAAC,IAAwB,OACxB,UAAA5C,IAAW,OACX,QAAA6C,GACA,SAAAC,GACA,SAAAC,GACA,mBAAAC,IAAoB,MACpB,qBAAAC,IAAsB,OACtB,kBAAAC,GACA,UAAAjD,IAAW,OACX,UAAAC,IAAW,IACX,OAAAN,IACA,YAAAH,IACA,GAAGC,GACL,GACAC,OACiB;AAEjB,MAAMwD,QAAmB/B,aAAAA,QAAuB,IAAI,GAE9CgC,QAAmBhC,aAAAA,QAAuB,IAAI,GAE9CiC,QAAqBjC,aAAAA,QAA0B,IAAI,GAEnDkC,QAAmBlC,aAAAA,QAAOlB,CAAQ,GAElC,CAACqD,GAAYC,CAAa,QAAIC,aAAAA,UAAS,KAAK,GAE5C,CAACC,IAAeC,CAAgB,QAAIF,aAAAA,UAAS,IAAI,GAEjDG,QAA4BxC,aAAAA,QAAO,KAAK,GAExC,CAACyC,IAAmBC,EAAoB,QAAIL,aAAAA,UAA6B,GAEzEM,IAAmB7C,GAAYhB,CAAQ,GAGvC8D,QAAyB7B,aAAAA,aAC7B,SAAwB,EAAE,GAAA8B,GAAG,GAAAC,GAAG,UAAAC,EAAS,GAAgC;AACvE,QAAMC,IAAcjB,EAAiB,SAC/BkB,IAAgBhB,EAAmB,SACnCiB,IAAclB,EAAiB,SAC/B,EAAE,OAAAmB,GAAO,QAAAC,GAAQ,MAAAC,IAAM,KAAAC,GAAI,IAAIN,EAAY,sBAAsB;AAGvE,QAAIG,MAAU,KAAKC,MAAW,EAC5B;AAGF,QAAMG,KAAgB1E,IAClBkE,IACED,IAAIQ,KAAM/B,EAAgB,UAC1BuB,IACFC,IACAF,IAAIQ,KAAO9B,EAAgB,UAC3BsB,GAGEW,IAAe,KAAK,IACxB,KAAK,IAAKD,MAAiB1E,IAAWuE,IAASD,KAAU,KAAK,CAAC,GAC/D,GACF,GAEMM,IAAY5E,IACduE,KAAUJ,EAAY,gBAAgB,KACtCG,KAASH,EAAY,eAAe,IAElCU,IACFpC,IAAgBmC,KAAc5E,IAAWuE,IAASD,KAAU,KAE1DQ,IAAgC,KAAK,IACzC,KAAK,IAAIH,GAAcE,IAA0BD,CAAS,GAC1D,MAAMC,IAA0BD,CAClC;AAEAvB,MAAiB,UAAUsB,GAC3BP,EAAc,aAAa,iBAAiB,GAAG,KAAK,MAAMf,EAAiB,OAAO,CAAC,EAAE,GACrFe,EAAc,MAAM,MAAMpE,IAAW,GAAG8E,CAA6B,MAAM,KAC3EV,EAAc,MAAM,OAAOpE,IAAW,MAAM,GAAG8E,CAA6B,KAC5ET,EAAY,MAAM,WAAWrE,IACzB,SAAS8E,CAA6B,aACtC,eAAeA,CAA6B,MAE5C7B,KACFA,EAAiBI,EAAiB,OAAO;EAE7C,GACA,CAACZ,GAAeQ,GAAkBjD,GAAU0C,CAAe,CAC7D;AAGAtB,mBAAAA,WAAU,MAAM;AACd,QAAM,EAAE,OAAAkD,GAAO,QAAAC,EAAO,IACpBrB,EAAiB,QACjB,sBAAsB,GAGlByB,IAAe1E,MAAa6D,IAAmBT,EAAiB,UAAUpD;AAEhF8D,MAAuB,EACrB,GAAIO,IAAQ,MAAOK,GACnB,GAAIJ,IAAS,MAAOI,EACtB,CAAC;EACH,GAAG,CAAClC,GAAexC,GAAUD,GAAU8D,GAAkBC,CAAsB,CAAC;AAGhF,MAAMgB,SAAoB7C,aAAAA,aACvBK,OAAqB;AACpBA,MAAG,eAAe,GAGd,EAAAxC,KAAYwC,EAAG,WAAW,OAE9BwB,EAAuB,EAAE,UAAU,MAAM,GAAGxB,EAAG,OAAO,GAAGA,EAAG,MAAM,CAAC,GACnEgB,EAAc,IAAI,GAClBG,EAAiB,IAAI;EACvB,GACA,CAAC3D,GAAUgE,CAAsB,CACnC,GAGMiB,QAAoB9C,aAAAA,aACxB,SAAkBK,GAAkB;AAClCwB,MAAuB,EAAE,UAAU,MAAM,GAAGxB,EAAG,OAAO,GAAGA,EAAG,MAAM,CAAC,GACnEmB,EAAiB,KAAK;EACxB,GACA,CAACK,CAAsB,CACzB,GAGMkB,QAAkB/C,aAAAA,aAAY,MAAM;AACxCqB,MAAc,KAAK,GACnBG,EAAiB,IAAI;EACvB,GAAG,CAAC,CAAC,GAGCwB,SAAqEhD,aAAAA,aACzE,CAAC,EAAE,OAAAoC,GAAO,QAAAC,EAAO,MAAM;AACrB,QAAM,EAAE,OAAOY,GAAa,QAAQC,EAAa,IAC/ClC,EAAiB,QACjB,sBAAsB;AAExBa,MAAuB,EACrB,GAAKO,IAAQ,MAAOjB,EAAiB,UAAU8B,IAAeb,GAC9D,GAAKC,IAAS,MAAOlB,EAAiB,UAAU+B,IAAgBb,EAClE,CAAC;EACH,GACA,CAACR,CAAsB,CACzB,GAGMsB,SAAgBnD,aAAAA,aACnBK,OAAsB;AACrB,QAAI,CAAC,OAAO,OAAO3B,CAAiB,EAAE,SAAS2B,EAAG,GAAwB,EACxE;AAGFA,MAAG,eAAe,GAClBmB,EAAiB,IAAI;AAErB,QAAM,EAAE,KAAAe,GAAK,MAAAD,EAAK,IAChBpB,EAAmB,QACnB,sBAAsB,GAElB,EAAE,OAAAkB,GAAO,QAAAC,EAAO,IACpBrB,EAAiB,QACjB,sBAAsB,GAGlBoC,IADe,OAAOvC,KAAsB,WAE9C,WAAWA,CAAiB,IAC3BA,IAAoBuB,IAAS,KAE5BiB,IAAcvF,IAChBuC,EAAG,QAAA,eAAwCA,EAAG,QAAA,cAC9CA,EAAG,QAAA,gBAAyCA,EAAG,QAAA,WAE7CoC,IAAe,KAAK,IACxB,KAAK,IACHY,IACIlC,EAAiB,UAAUiC,IAC3BjC,EAAiB,UAAUiC,GAC/B,CACF,GACA,GACF;AAEAvB,MAAuB,EACrB,GAAG/D,IAAWwE,IAAQF,IAAQK,IAAgB,KAC9C,GAAG3E,IAAYuE,IAASI,IAAgB,MAAMF,EAChD,CAAC;EACH,GACA,CAAC1B,GAAmB/C,GAAU+D,CAAsB,CACtD;AAGA3C,mBAAAA,WAAU,MAAM;AACdyC,OACEb,IAAsBI,EAAmB,UAAUF,EAAiB,OACtE;EACF,GAAG,CAACF,CAAmB,CAAC,OAGxB5B,aAAAA,WAAU,MAAM;AACd,QAAMoE,IAAetC,EAAiB,SAEhCuC,IAAqB,MAAY;AACjCnC,WACJ2B,EAAgB;IAClB;AAEA,WAAItC,MACF6C,EAAa,iBAAiB,eAAeR,GAAmB5C,CAAoB,GACpFoD,EAAa,iBAAiB,gBAAgBC,GAAoBrD,CAAoB,IAGjF,MAAM;AACXoD,QAAa,oBAAoB,eAAeR,CAAiB,GACjEQ,EAAa,oBAAoB,gBAAgBC,CAAkB;IACrE;EACF,GAAG,CAAC9C,GAAuBqC,GAAmBC,GAAiB3B,CAAU,CAAC,OAG1ElC,aAAAA,WAAU,OACJkC,KAAc,CAACK,EAA0B,YAC3CjB,EAAgB,iBAAiB,eAAesC,GAAmB5C,CAAoB,GACvFM,EAAgB,iBAAiB,aAAauC,GAAiB7C,CAAoB,GACnFuB,EAA0B,UAAU,OAG/B,MAAY;AACbA,MAA0B,YAC5BjB,EAAgB,oBAAoB,eAAesC,CAAiB,GACpEtC,EAAgB,oBAAoB,aAAauC,CAAe,GAChEtB,EAA0B,UAAU;EAExC,IACC,CAACqB,GAAmBC,GAAiB3B,GAAYZ,CAAe,CAAC,OAEpEgD,aAAAA,qBACEhG,IACA,OACS,EACL,eAAewD,EAAiB,SAChC,iBAAiBE,EAAmB,SACpC,YAAYuB,GAAoB;AAC9B,QAAM,EAAE,OAAAL,GAAO,QAAAC,EAAO,IACpBrB,EAAiB,QACjB,sBAAsB;AAExBa,MAAuB,EACrB,GAAIO,IAAQ,MAAOK,GACnB,GAAIJ,IAAS,MAAOI,EACtB,CAAC;EACH,EACF,IAEF,CAACZ,CAAsB,CACzB,GAGAhC,GAAkBmB,GAAkBgC,EAAY,GAEhD7D,EACE,WACAgE,IACAjC,EAAmB,SACnBf,CACF,GAEAhB,EACE,SACAiB,IACAc,EAAmB,SACnBf,CACF,GAEAhB,EACE,eACA0D,IACAnB,IACAvB,CACF;AAGA,MAAMsD,KAAS/C,SAAUhD,oBAAAA,KAACQ,GAAA,EAAyB,UAAUL,GAAU,UAAUC,EAAAA,CAAU,GACrF4F,IAAoBnC,KAAgBjE,KAAa,QAEjDqG,KAA2B,EAC/B,UAAU,YACV,SAAS,QACT,UAAU,UACV,QAAQvC,IAActD,IAAW,cAAc,cAAe,QAC9D,aAAa,QACb,YAAY,QACZ,iBAAiB,QACjB,cAAc,QACd,eAAe,QACf,kBAAkB,QAClB,GAAGL,GACL;AAEA,aACEgB,oBAAAA,MAAC,OAAA,EAAK,GAAGlB,IAAO,KAAKyD,GAAkB,OAAO2C,IAAW,YAAS,QAC/D,UAAA,CAAAhD,OACDjD,oBAAAA,KAACN,GAAA,EAAc,KAAK6D,GAAkB,YAAYyC,GAC/C,UAAA9C,EAAAA,CACH,OAEAlD,oBAAAA,KAACC,GAAA,EACC,UAAUE,GACV,UAAUC,GACV,UAAU,KAAK,MAAMqD,EAAiB,OAAO,GAC7C,KAAKD,GACL,YAAYwC,GAEX,UAAAD,GAAAA,CACH,CAAA,EAAA,CACF;AAEJ,CACF;AAEAnD,GAAmB,cAAc;AIvW1B,IAAMsD,SAA0BvG,cAAAA,YACrC,CAAC,EAAE,OAAAI,GAAO,GAAGF,EAAM,GAAGC,MAAsB;AAC1C,MAAMmG,IAA2BhF,EAAkBlB,CAAK;AAExD,aAAOC,oBAAAA,KAAC,OAAA,EAAI,KAAKF,GAAM,GAAGD,GAAO,OAAOoG,GAAW,YAAS,QAAA,CAAQ;AACtE,CACF;AAEAC,GAAwB,cAAc;ACV/B,IAAMC,KAA2B,UACtC5E,cAAAA,QAAuC,EACrC,eAAe,MACf,iBAAiB,MACjB,aAAa,MAEX,QAAQ,KACN,sFACF,EACJ,CAAC;", "names": ["import_react", "import_jsx_runtime", "ContainerClip", "forwardRef", "transition", "props", "ref", "style", "jsx", "Container<PERSON><PERSON>le", "children", "disabled", "portrait", "position", "ThisArrow", "flip", "ReactCompareSliderHandle", "className", "buttonStyle", "linesStyle", "_style", "_linesStyle", "_buttonStyle", "jsxs", "KeyboardEventKeys", "styleFitContainer", "boxSizing", "objectFit", "objectPosition", "usePrevious", "value", "useRef", "useEffect", "useEventListener", "eventName", "handler", "element", "handlerOptions", "<PERSON><PERSON><PERSON><PERSON>", "eventListener", "event", "useIsomorphicLayoutEffect", "useLayoutEffect", "useResizeObserver", "observer", "observe", "useCallback", "entry", "EVENT_PASSIVE_PARAMS", "EVENT_CAPTURE_PARAMS", "handleContainerClick", "ev", "ReactCompareSlider", "boundsPadding", "browsingContext", "changePositionOnHover", "handle", "itemOne", "itemTwo", "keyboardIncrement", "onlyHandleDraggable", "onPositionChange", "rootContainerRef", "clipContainerRef", "handleContainerRef", "internalPosition", "isDragging", "setIsDragging", "useState", "canTransition", "setCanTransition", "hasBrowsingContextBinding", "interactiveTarget", "setInteractiveTarget", "previousPosition", "updateInternalPosition", "x", "y", "isOffset", "rootElement", "handleElement", "clipElement", "width", "height", "left", "top", "pixelPosition", "nextPosition", "zoomScale", "boundsPaddingPercentage", "nextPositionWithBoundsPadding", "handlePointerDown", "handlePointerMove", "handlePointerUp", "handleResize", "scaledWidth", "scaledHeight", "handleKeydown", "incrementPercentage", "isIncrement", "containerRef", "handlePointerLeave", "useImperativeHandle", "<PERSON><PERSON>", "appliedTransition", "rootStyle", "ReactCompareSliderImage", "useReactCompareSliderRef"]}