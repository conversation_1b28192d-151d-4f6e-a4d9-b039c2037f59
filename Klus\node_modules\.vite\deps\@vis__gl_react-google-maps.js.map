{"version": 3, "sources": ["../../@vis.gl/react-google-maps/src/libraries/api-loading-status.ts", "../../@vis.gl/react-google-maps/src/libraries/google-maps-api-loader.ts", "../../@vis.gl/react-google-maps/src/components/api-provider.tsx", "../../@vis.gl/react-google-maps/src/components/map/use-map-events.ts", "../../@vis.gl/react-google-maps/src/libraries/use-deep-compare-effect.tsx", "../../@vis.gl/react-google-maps/src/components/map/use-map-options.ts", "../../@vis.gl/react-google-maps/src/hooks/use-api-loading-status.ts", "../../@vis.gl/react-google-maps/src/components/map/use-deckgl-camera-update.ts", "../../@vis.gl/react-google-maps/src/libraries/lat-lng-utils.ts", "../../@vis.gl/react-google-maps/src/components/map/use-map-camera-params.ts", "../../@vis.gl/react-google-maps/src/components/map/auth-failure-message.tsx", "../../@vis.gl/react-google-maps/src/libraries/use-callback-ref.tsx", "../../@vis.gl/react-google-maps/src/hooks/use-api-is-loaded.ts", "../../@vis.gl/react-google-maps/src/libraries/use-force-update.ts", "../../@vis.gl/react-google-maps/src/components/map/use-tracked-camera-state-ref.ts", "../../@vis.gl/react-google-maps/src/components/map/use-map-instance.ts", "../../@vis.gl/react-google-maps/src/components/map/index.tsx", "../../@vis.gl/react-google-maps/src/libraries/errors.ts", "../../@vis.gl/react-google-maps/src/hooks/use-map.ts", "../../@vis.gl/react-google-maps/src/hooks/use-maps-library.ts", "../../@vis.gl/react-google-maps/src/hooks/use-maps-event-listener.ts", "../../@vis.gl/react-google-maps/src/hooks/use-prop-binding.ts", "../../@vis.gl/react-google-maps/src/hooks/use-dom-event-listener.ts", "../../@vis.gl/react-google-maps/src/components/advanced-marker.tsx", "../../@vis.gl/react-google-maps/src/libraries/set-value-for-styles.ts", "../../@vis.gl/react-google-maps/src/components/info-window.tsx", "../../@vis.gl/react-google-maps/src/libraries/create-static-maps-url/helpers.ts", "../../@vis.gl/react-google-maps/src/libraries/create-static-maps-url/assemble-marker-params.ts", "../../@vis.gl/react-google-maps/src/libraries/create-static-maps-url/assemble-path-params.ts", "../../@vis.gl/react-google-maps/src/libraries/create-static-maps-url/assemble-map-type-styles.ts", "../../@vis.gl/react-google-maps/src/libraries/create-static-maps-url/index.ts", "../../@vis.gl/react-google-maps/src/components/static-map.tsx", "../../@vis.gl/react-google-maps/src/components/map-control.tsx", "../../@vis.gl/react-google-maps/src/components/marker.tsx", "../../@vis.gl/react-google-maps/src/components/pin.tsx", "../../@vis.gl/react-google-maps/src/libraries/limit-tilt-range.ts"], "sourcesContent": ["export const APILoadingStatus = {\n  NOT_LOADED: 'NOT_LOADED',\n  LOADING: 'LOADING',\n  LOADED: 'LOADED',\n  FAILED: 'FAILED',\n  AUTH_FAILURE: 'AUTH_FAILURE'\n};\nexport type APILoadingStatus =\n  (typeof APILoadingStatus)[keyof typeof APILoadingStatus];\n", "import {APILoadingStatus} from './api-loading-status';\n\nexport type ApiParams = {\n  key: string;\n  v?: string;\n  language?: string;\n  region?: string;\n  libraries?: string;\n  channel?: number;\n  solutionChannel?: string;\n  authReferrerPolicy?: string;\n};\n\ntype LoadingStatusCallback = (status: APILoadingStatus) => void;\n\nconst MAPS_API_BASE_URL = 'https://maps.googleapis.com/maps/api/js';\n\n/**\n * A GoogleMapsApiLoader to reliably load and unload the Google Maps JavaScript API.\n *\n * The actual loading and unloading is delayed into the microtask queue, to\n * allow using the API in an useEffect hook, without worrying about multiple API loads.\n */\nexport class GoogleMapsApiLoader {\n  /**\n   * The current loadingStatus of the API.\n   */\n  public static loadingStatus: APILoadingStatus = APILoadingStatus.NOT_LOADED;\n\n  /**\n   * The parameters used for first loading the API.\n   */\n  public static serializedApiParams?: string;\n\n  /**\n   * A list of functions to be notified when the loading status changes.\n   */\n  private static listeners: LoadingStatusCallback[] = [];\n\n  /**\n   * Loads the Maps JavaScript API with the specified parameters.\n   * Since the Maps library can only be loaded once per page, this will\n   * produce a warning when called multiple times with different\n   * parameters.\n   *\n   * The returned promise resolves when loading completes\n   * and rejects in case of an error or when the loading was aborted.\n   */\n  static async load(\n    params: ApiParams,\n    onLoadingStatusChange: (status: APILoadingStatus) => void\n  ): Promise<void> {\n    const libraries = params.libraries ? params.libraries.split(',') : [];\n    const serializedParams = this.serializeParams(params);\n\n    this.listeners.push(onLoadingStatusChange);\n\n    // Note: if `google.maps.importLibrary` has been defined externally, we\n    //   assume that loading is complete and successful.\n    //   If it was defined by a previous call to this method, a warning\n    //   message is logged if there are differences in api-parameters used\n    //   for both calls.\n\n    if (window.google?.maps?.importLibrary as unknown) {\n      // no serialized parameters means it was loaded externally\n      if (!this.serializedApiParams) {\n        this.loadingStatus = APILoadingStatus.LOADED;\n      }\n      this.notifyLoadingStatusListeners();\n    } else {\n      this.serializedApiParams = serializedParams;\n      this.initImportLibrary(params);\n    }\n\n    if (\n      this.serializedApiParams &&\n      this.serializedApiParams !== serializedParams\n    ) {\n      console.warn(\n        `[google-maps-api-loader] The maps API has already been loaded ` +\n          `with different parameters and will not be loaded again. Refresh the ` +\n          `page for new values to have effect.`\n      );\n    }\n\n    const librariesToLoad = ['maps', ...libraries];\n    await Promise.all(\n      librariesToLoad.map(name => google.maps.importLibrary(name))\n    );\n  }\n\n  /**\n   * Serialize the parameters used to load the library for easier comparison.\n   */\n  private static serializeParams(params: ApiParams): string {\n    return [\n      params.v,\n      params.key,\n      params.language,\n      params.region,\n      params.authReferrerPolicy,\n      params.solutionChannel\n    ].join('/');\n  }\n\n  /**\n   * Creates the global `google.maps.importLibrary` function for bootstrapping.\n   * This is essentially a formatted version of the dynamic loading script\n   * from the official documentation with some minor adjustments.\n   *\n   * The created importLibrary function will load the Google Maps JavaScript API,\n   * which will then replace the `google.maps.importLibrary` function with the full\n   * implementation.\n   *\n   * @see https://developers.google.com/maps/documentation/javascript/load-maps-js-api#dynamic-library-import\n   */\n  private static initImportLibrary(params: ApiParams) {\n    if (!window.google) window.google = {} as never;\n    if (!window.google.maps) window.google.maps = {} as never;\n\n    if (window.google.maps['importLibrary']) {\n      console.error(\n        '[google-maps-api-loader-internal]: initImportLibrary must only be called once'\n      );\n\n      return;\n    }\n\n    let apiPromise: Promise<void> | null = null;\n\n    const loadApi = () => {\n      if (apiPromise) return apiPromise;\n\n      apiPromise = new Promise((resolve, reject) => {\n        const scriptElement = document.createElement('script');\n        const urlParams = new URLSearchParams();\n\n        for (const [key, value] of Object.entries(params)) {\n          const urlParamName = key.replace(\n            /[A-Z]/g,\n            t => '_' + t[0].toLowerCase()\n          );\n          urlParams.set(urlParamName, String(value));\n        }\n        urlParams.set('loading', 'async');\n        urlParams.set('callback', '__googleMapsCallback__');\n\n        scriptElement.async = true;\n        scriptElement.src = MAPS_API_BASE_URL + `?` + urlParams.toString();\n        scriptElement.nonce =\n          (document.querySelector('script[nonce]') as HTMLScriptElement)\n            ?.nonce || '';\n\n        scriptElement.onerror = () => {\n          this.loadingStatus = APILoadingStatus.FAILED;\n          this.notifyLoadingStatusListeners();\n          reject(new Error('The Google Maps JavaScript API could not load.'));\n        };\n\n        window.__googleMapsCallback__ = () => {\n          this.loadingStatus = APILoadingStatus.LOADED;\n          this.notifyLoadingStatusListeners();\n          resolve();\n        };\n\n        window.gm_authFailure = () => {\n          this.loadingStatus = APILoadingStatus.AUTH_FAILURE;\n          this.notifyLoadingStatusListeners();\n        };\n\n        this.loadingStatus = APILoadingStatus.LOADING;\n        this.notifyLoadingStatusListeners();\n\n        document.head.append(scriptElement);\n      });\n\n      return apiPromise;\n    };\n\n    // for the first load, we declare an importLibrary function that will\n    // be overwritten once the api is loaded.\n    google.maps.importLibrary = libraryName =>\n      loadApi().then(() => google.maps.importLibrary(libraryName));\n  }\n\n  /**\n   * Calls all registered loadingStatusListeners after a status update.\n   */\n  private static notifyLoadingStatusListeners() {\n    for (const fn of this.listeners) {\n      fn(this.loadingStatus);\n    }\n  }\n}\n\n// Declare global maps callback functions\ndeclare global {\n  interface Window {\n    __googleMapsCallback__?: () => void;\n    gm_authFailure?: () => void;\n  }\n}\n", "import React, {\n  FunctionComponent,\n  PropsWithChildren,\n  useCallback,\n  useEffect,\n  useMemo,\n  useReducer,\n  useState\n} from 'react';\n\nimport {\n  ApiParams,\n  GoogleMapsApiLoader\n} from '../libraries/google-maps-api-loader';\nimport {APILoadingStatus} from '../libraries/api-loading-status';\n\ntype ImportLibraryFunction = typeof google.maps.importLibrary;\ntype GoogleMapsLibrary = Awaited<ReturnType<ImportLibraryFunction>>;\ntype LoadedLibraries = {[name: string]: GoogleMapsLibrary};\n\nexport interface APIProviderContextValue {\n  status: APILoadingStatus;\n  loadedLibraries: LoadedLibraries;\n  importLibrary: typeof google.maps.importLibrary;\n  mapInstances: Record<string, google.maps.Map>;\n  addMapInstance: (map: google.maps.Map, id?: string) => void;\n  removeMapInstance: (id?: string) => void;\n  clearMapInstances: () => void;\n}\n\nconst DEFAULT_SOLUTION_CHANNEL = 'GMP_visgl_rgmlibrary_v1_default';\n\nexport const APIProviderContext =\n  React.createContext<APIProviderContextValue | null>(null);\n\nexport type APIProviderProps = PropsWithChildren<{\n  /**\n   * apiKey must be provided to load the Google Maps JavaScript API. To create an API key, see: https://developers.google.com/maps/documentation/javascript/get-api-key\n   * Part of:\n   */\n  apiKey: string;\n  /**\n   * A custom id to reference the script tag can be provided. The default is set to 'google-maps-api'\n   * @default 'google-maps-api'\n   */\n  libraries?: Array<string>;\n  /**\n   * A specific version of the Google Maps JavaScript API can be used.\n   * Read more about versioning: https://developers.google.com/maps/documentation/javascript/versions\n   * Part of: https://developers.google.com/maps/documentation/javascript/url-params\n   */\n  version?: string;\n  /**\n   * Sets the map to a specific region.\n   * Read more about localizing the Map: https://developers.google.com/maps/documentation/javascript/localization\n   * Part of: https://developers.google.com/maps/documentation/javascript/url-params\n   */\n  region?: string;\n  /**\n   * Use a specific language for the map.\n   * Read more about localizing the Map: https://developers.google.com/maps/documentation/javascript/localization\n   * Part of: https://developers.google.com/maps/documentation/javascript/url-params\n   */\n  language?: string;\n  /**\n   * auth_referrer_policy can be set to 'origin'.\n   * Part of: https://developers.google.com/maps/documentation/javascript/url-params\n   */\n  authReferrerPolicy?: string;\n  /**\n   * To understand usage and ways to improve our solutions, Google includes the\n   * `solution_channel` query parameter in API calls to gather information about\n   * code usage. You may opt out at any time by setting this attribute to an\n   * empty string. Read more in the\n   * [documentation](https://developers.google.com/maps/reporting-and-monitoring/reporting#solutions-usage).\n   */\n  channel?: number;\n  /**\n   * To track usage of Google Maps JavaScript API via numeric channels. The only acceptable channel values are numbers from 0-999.\n   * Read more in the\n   * [documentation](https://developers.google.com/maps/reporting-and-monitoring/reporting#usage-tracking-per-channel)\n   */\n  solutionChannel?: string;\n  /**\n   * A function that can be used to execute code after the Google Maps JavaScript API has been loaded.\n   */\n  onLoad?: () => void;\n  /**\n   * A function that will be called if there was an error when loading the Google Maps JavaScript API.\n   */\n  onError?: (error: unknown) => void;\n}>;\n\n/**\n * local hook to set up the map-instance management context.\n */\nfunction useMapInstances() {\n  const [mapInstances, setMapInstances] = useState<\n    Record<string, google.maps.Map>\n  >({});\n\n  const addMapInstance = (mapInstance: google.maps.Map, id = 'default') => {\n    setMapInstances(instances => ({...instances, [id]: mapInstance}));\n  };\n\n  const removeMapInstance = (id = 'default') => {\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    setMapInstances(({[id]: _, ...remaining}) => remaining);\n  };\n\n  const clearMapInstances = () => {\n    setMapInstances({});\n  };\n\n  return {mapInstances, addMapInstance, removeMapInstance, clearMapInstances};\n}\n\n/**\n * local hook to handle the loading of the maps API, returns the current loading status\n * @param props\n */\nfunction useGoogleMapsApiLoader(props: APIProviderProps) {\n  const {\n    onLoad,\n    onError,\n    apiKey,\n    version,\n    libraries = [],\n    ...otherApiParams\n  } = props;\n\n  const [status, setStatus] = useState<APILoadingStatus>(\n    GoogleMapsApiLoader.loadingStatus\n  );\n  const [loadedLibraries, addLoadedLibrary] = useReducer(\n    (\n      loadedLibraries: LoadedLibraries,\n      action: {name: keyof LoadedLibraries; value: LoadedLibraries[string]}\n    ) => {\n      return loadedLibraries[action.name]\n        ? loadedLibraries\n        : {...loadedLibraries, [action.name]: action.value};\n    },\n    {}\n  );\n\n  const librariesString = useMemo(() => libraries?.join(','), [libraries]);\n  const serializedParams = useMemo(\n    () => JSON.stringify({apiKey, version, ...otherApiParams}),\n    [apiKey, version, otherApiParams]\n  );\n\n  const importLibrary: typeof google.maps.importLibrary = useCallback(\n    async (name: string) => {\n      if (loadedLibraries[name]) {\n        return loadedLibraries[name];\n      }\n\n      if (!google?.maps?.importLibrary) {\n        throw new Error(\n          '[api-provider-internal] importLibrary was called before ' +\n            'google.maps.importLibrary was defined.'\n        );\n      }\n\n      const res = await window.google.maps.importLibrary(name);\n      addLoadedLibrary({name, value: res});\n\n      return res;\n    },\n    [loadedLibraries]\n  );\n\n  useEffect(\n    () => {\n      (async () => {\n        try {\n          const params: ApiParams = {key: apiKey, ...otherApiParams};\n          if (version) params.v = version;\n          if (librariesString?.length > 0) params.libraries = librariesString;\n\n          if (\n            params.channel === undefined ||\n            params.channel < 0 ||\n            params.channel > 999\n          )\n            delete params.channel;\n\n          if (params.solutionChannel === undefined)\n            params.solutionChannel = DEFAULT_SOLUTION_CHANNEL;\n          else if (params.solutionChannel === '') delete params.solutionChannel;\n\n          await GoogleMapsApiLoader.load(params, status => setStatus(status));\n\n          for (const name of ['core', 'maps', ...libraries]) {\n            await importLibrary(name);\n          }\n\n          if (onLoad) {\n            onLoad();\n          }\n        } catch (error) {\n          if (onError) {\n            onError(error);\n          } else {\n            console.error(\n              '<ApiProvider> failed to load the Google Maps JavaScript API',\n              error\n            );\n          }\n        }\n      })();\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [apiKey, librariesString, serializedParams]\n  );\n\n  return {\n    status,\n    loadedLibraries,\n    importLibrary\n  };\n}\n\n/**\n * Component to wrap the components from this library and load the Google Maps JavaScript API\n */\nexport const APIProvider: FunctionComponent<APIProviderProps> = props => {\n  const {children, ...loaderProps} = props;\n  const {mapInstances, addMapInstance, removeMapInstance, clearMapInstances} =\n    useMapInstances();\n\n  const {status, loadedLibraries, importLibrary} =\n    useGoogleMapsApiLoader(loaderProps);\n\n  const contextValue: APIProviderContextValue = useMemo(\n    () => ({\n      mapInstances,\n      addMapInstance,\n      removeMapInstance,\n      clearMapInstances,\n      status,\n      loadedLibraries,\n      importLibrary\n    }),\n    [\n      mapInstances,\n      addMapInstance,\n      removeMapInstance,\n      clearMapInstances,\n      status,\n      loadedLibraries,\n      importLibrary\n    ]\n  );\n\n  return (\n    <APIProviderContext.Provider value={contextValue}>\n      {children}\n    </APIProviderContext.Provider>\n  );\n};\n", "import {useEffect} from 'react';\n\n/**\n * Handlers for all events that could be emitted by map-instances.\n */\nexport type MapEventProps = Partial<{\n  // map view state events\n  onBoundsChanged: (event: MapCameraChangedEvent) => void;\n  onCenterChanged: (event: MapCameraChangedEvent) => void;\n  onHeadingChanged: (event: MapCameraChangedEvent) => void;\n  onTiltChanged: (event: MapCameraChangedEvent) => void;\n  onZoomChanged: (event: MapCameraChangedEvent) => void;\n  onCameraChanged: (event: MapCameraChangedEvent) => void;\n\n  // mouse / touch / pointer events\n  onClick: (event: MapMouseEvent) => void;\n  onDblclick: (event: MapMouseEvent) => void;\n  onContextmenu: (event: MapMouseEvent) => void;\n  onMousemove: (event: MapMouseEvent) => void;\n  onMouseover: (event: MapMouseEvent) => void;\n  onMouseout: (event: MapMouseEvent) => void;\n  onDrag: (event: MapEvent) => void;\n  onDragend: (event: MapEvent) => void;\n  onDragstart: (event: MapEvent) => void;\n\n  // loading events\n  onTilesLoaded: (event: MapEvent) => void;\n  onIdle: (event: MapEvent) => void;\n\n  // configuration events\n  onProjectionChanged: (event: MapEvent) => void;\n  onIsFractionalZoomEnabledChanged: (event: MapEvent) => void;\n  onMapCapabilitiesChanged: (event: MapEvent) => void;\n  onMapTypeIdChanged: (event: MapEvent) => void;\n  onRenderingTypeChanged: (event: MapEvent) => void;\n}>;\n\n/**\n * Sets up effects to bind event-handlers for all event-props in MapEventProps.\n * @internal\n */\nexport function useMapEvents(\n  map: google.maps.Map | null,\n  props: MapEventProps\n) {\n  // note: calling a useEffect hook from within a loop is prohibited by the\n  // rules of hooks, but it's ok here since it's unconditional and the number\n  // and order of iterations is always strictly the same.\n  // (see https://legacy.reactjs.org/docs/hooks-rules.html)\n\n  for (const propName of eventPropNames) {\n    // fixme: this cast is essentially a 'trust me, bro' for typescript, but\n    //   a proper solution seems way too complicated right now\n    const handler = props[propName] as (ev: MapEvent) => void;\n    const eventType = propNameToEventType[propName];\n\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      if (!map) return;\n      if (!handler) return;\n\n      const listener = google.maps.event.addListener(\n        map,\n        eventType,\n        (ev?: google.maps.MapMouseEvent | google.maps.IconMouseEvent) => {\n          handler(createMapEvent(eventType, map, ev));\n        }\n      );\n\n      return () => listener.remove();\n    }, [map, eventType, handler]);\n  }\n}\n\n/**\n * Create the wrapped map-events used for the event-props.\n * @param type the event type as it is specified to the maps api\n * @param map the map instance the event originates from\n * @param srcEvent the source-event if there is one.\n */\nfunction createMapEvent(\n  type: string,\n  map: google.maps.Map,\n  srcEvent?: google.maps.MapMouseEvent | google.maps.IconMouseEvent\n): MapEvent {\n  const ev: MapEvent = {\n    type,\n    map,\n    detail: {},\n    stoppable: false,\n    stop: () => {}\n  };\n\n  if (cameraEventTypes.includes(type)) {\n    const camEvent = ev as MapCameraChangedEvent;\n\n    const center = map.getCenter();\n    const zoom = map.getZoom();\n    const heading = map.getHeading() || 0;\n    const tilt = map.getTilt() || 0;\n    const bounds = map.getBounds();\n\n    if (!center || !bounds || !Number.isFinite(zoom)) {\n      console.warn(\n        '[createEvent] at least one of the values from the map ' +\n          'returned undefined. This is not expected to happen. Please ' +\n          'report an issue at https://github.com/visgl/react-google-maps/issues/new'\n      );\n    }\n\n    camEvent.detail = {\n      center: center?.toJSON() || {lat: 0, lng: 0},\n      zoom: (zoom as number) || 0,\n      heading: heading as number,\n      tilt: tilt as number,\n      bounds: bounds?.toJSON() || {\n        north: 90,\n        east: 180,\n        south: -90,\n        west: -180\n      }\n    };\n\n    return camEvent;\n  } else if (mouseEventTypes.includes(type)) {\n    if (!srcEvent)\n      throw new Error('[createEvent] mouse events must provide a srcEvent');\n    const mouseEvent = ev as MapMouseEvent;\n\n    mouseEvent.domEvent = srcEvent.domEvent;\n    mouseEvent.stoppable = true;\n    mouseEvent.stop = () => srcEvent.stop();\n\n    mouseEvent.detail = {\n      latLng: srcEvent.latLng?.toJSON() || null,\n      placeId: (srcEvent as google.maps.IconMouseEvent).placeId\n    };\n\n    return mouseEvent;\n  }\n\n  return ev;\n}\n\n/**\n * maps the camelCased names of event-props to the corresponding event-types\n * used in the maps API.\n */\nconst propNameToEventType: {[prop in keyof Required<MapEventProps>]: string} = {\n  onBoundsChanged: 'bounds_changed',\n  onCenterChanged: 'center_changed',\n  onClick: 'click',\n  onContextmenu: 'contextmenu',\n  onDblclick: 'dblclick',\n  onDrag: 'drag',\n  onDragend: 'dragend',\n  onDragstart: 'dragstart',\n  onHeadingChanged: 'heading_changed',\n  onIdle: 'idle',\n  onIsFractionalZoomEnabledChanged: 'isfractionalzoomenabled_changed',\n  onMapCapabilitiesChanged: 'mapcapabilities_changed',\n  onMapTypeIdChanged: 'maptypeid_changed',\n  onMousemove: 'mousemove',\n  onMouseout: 'mouseout',\n  onMouseover: 'mouseover',\n  onProjectionChanged: 'projection_changed',\n  onRenderingTypeChanged: 'renderingtype_changed',\n  onTilesLoaded: 'tilesloaded',\n  onTiltChanged: 'tilt_changed',\n  onZoomChanged: 'zoom_changed',\n\n  // note: onCameraChanged is an alias for the bounds_changed event,\n  // since that is going to be fired in every situation where the camera is\n  // updated.\n  onCameraChanged: 'bounds_changed'\n} as const;\n\nconst cameraEventTypes = [\n  'bounds_changed',\n  'center_changed',\n  'heading_changed',\n  'tilt_changed',\n  'zoom_changed'\n];\n\nconst mouseEventTypes = [\n  'click',\n  'contextmenu',\n  'dblclick',\n  'mousemove',\n  'mouseout',\n  'mouseover'\n];\n\ntype MapEventPropName = keyof MapEventProps;\nconst eventPropNames = Object.keys(propNameToEventType) as MapEventPropName[];\n\nexport type MapEvent<T = unknown> = {\n  type: string;\n  map: google.maps.Map;\n  detail: T;\n\n  stoppable: boolean;\n  stop: () => void;\n  domEvent?: MouseEvent | TouchEvent | PointerEvent | KeyboardEvent | Event;\n};\n\nexport type MapMouseEvent = MapEvent<{\n  latLng: google.maps.LatLngLiteral | null;\n  placeId: string | null;\n}>;\n\nexport type MapCameraChangedEvent = MapEvent<{\n  center: google.maps.LatLngLiteral;\n  bounds: google.maps.LatLngBoundsLiteral;\n  zoom: number;\n  heading: number;\n  tilt: number;\n}>;\n", "import {DependencyList, EffectCallback, useEffect, useRef} from 'react';\nimport isDeepEqual from 'fast-deep-equal';\n\nexport function useDeepCompareEffect(\n  effect: EffectCallback,\n  deps: DependencyList\n) {\n  const ref = useRef<DependencyList | undefined>(undefined);\n\n  if (!ref.current || !isDeepEqual(deps, ref.current)) {\n    ref.current = deps;\n  }\n\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useEffect(effect, ref.current);\n}\n", "import {MapProps} from '../map';\nimport {useDeepCompareEffect} from '../../libraries/use-deep-compare-effect';\n\nconst mapOptionKeys: Set<keyof google.maps.MapOptions> = new Set([\n  'backgroundColor',\n  'clickableIcons',\n  'controlSize',\n  'disableDefaultUI',\n  'disableDoubleClickZoom',\n  'draggable',\n  'draggableCursor',\n  'draggingCursor',\n  'fullscreenControl',\n  'fullscreenControlOptions',\n  'gestureHandling',\n  'headingInteractionEnabled',\n  'isFractionalZoomEnabled',\n  'keyboardShortcuts',\n  'mapTypeControl',\n  'mapTypeControlOptions',\n  'mapTypeId',\n  'maxZoom',\n  'minZoom',\n  'noClear',\n  'panControl',\n  'panControlOptions',\n  'restriction',\n  'rotateControl',\n  'rotateControlOptions',\n  'scaleControl',\n  'scaleControlOptions',\n  'scrollwheel',\n  'streetView',\n  'streetViewControl',\n  'streetViewControlOptions',\n  'styles',\n  'tiltInteractionEnabled',\n  'zoomControl',\n  'zoomControlOptions'\n]);\n\n/**\n * Internal hook to update the map-options when props are changed.\n *\n * @param map the map instance\n * @param mapProps the props to update the map-instance with\n * @internal\n */\nexport function useMapOptions(map: google.maps.Map | null, mapProps: MapProps) {\n  /* eslint-disable react-hooks/exhaustive-deps --\n   *\n   * The following effects aren't triggered when the map is changed.\n   * In that case, the values will be or have been passed to the map\n   * constructor via mapOptions.\n   */\n\n  const mapOptions: google.maps.MapOptions = {};\n  const keys = Object.keys(mapProps) as (keyof google.maps.MapOptions)[];\n  for (const key of keys) {\n    if (!mapOptionKeys.has(key)) continue;\n\n    mapOptions[key] = mapProps[key] as never;\n  }\n\n  // update the map options when mapOptions is changed\n  // Note: due to the destructuring above, mapOptions will be seen as changed\n  //   with every re-render, so we're assuming the maps-api will properly\n  //   deal with unchanged option-values passed into setOptions.\n  useDeepCompareEffect(() => {\n    if (!map) return;\n\n    map.setOptions(mapOptions);\n  }, [mapOptions]);\n  /* eslint-enable react-hooks/exhaustive-deps */\n}\n", "import {useContext} from 'react';\nimport {APIProviderContext} from '../components/api-provider';\nimport {APILoadingStatus} from '../libraries/api-loading-status';\n\nexport function useApiLoadingStatus(): APILoadingStatus {\n  return useContext(APIProviderContext)?.status || APILoadingStatus.NOT_LOADED;\n}\n", "import {useLayoutEffect} from 'react';\n\nexport type DeckGlCompatProps = {\n  /**\n   * Viewport from deck.gl\n   */\n  viewport?: unknown;\n  /**\n   * View state from deck.gl\n   */\n  viewState?: Record<string, unknown>;\n  /**\n   * Initial View State from deck.gl\n   */\n  initialViewState?: Record<string, unknown>;\n};\n\n/**\n * Internal hook that updates the camera when deck.gl viewState changes.\n * @internal\n */\nexport function useDeckGLCameraUpdate(\n  map: google.maps.Map | null,\n  props: DeckGlCompatProps\n) {\n  const {viewport, viewState} = props;\n  const isDeckGlControlled = !!viewport;\n\n  useLayoutEffect(() => {\n    if (!map || !viewState) return;\n\n    const {\n      latitude,\n      longitude,\n      bearing: heading,\n      pitch: tilt,\n      zoom\n    } = viewState as Record<string, number>;\n\n    map.moveCamera({\n      center: {lat: latitude, lng: longitude},\n      heading,\n      tilt,\n      zoom: zoom + 1\n    });\n  }, [map, viewState]);\n\n  return isDeckGlControlled;\n}\n", "export function isLatLngLiteral(\n  obj: unknown\n): obj is google.maps.LatLngLiteral {\n  if (!obj || typeof obj !== 'object') return false;\n  if (!('lat' in obj && 'lng' in obj)) return false;\n\n  return Number.isFinite(obj.lat) && Number.isFinite(obj.lng);\n}\n\nexport function latLngEquals(\n  a: google.maps.LatLngLiteral | google.maps.LatLng | undefined | null,\n  b: google.maps.LatLngLiteral | google.maps.LatLng | undefined | null\n): boolean {\n  if (!a || !b) return false;\n  const A = toLatLngLiteral(a);\n  const B = toLatLngLiteral(b);\n  if (A.lat !== B.lat || A.lng !== B.lng) return false;\n  return true;\n}\n\nexport function toLatLngLiteral(\n  obj: google.maps.LatLngLiteral | google.maps.LatLng\n): google.maps.LatLngLiteral {\n  if (isLatLngLiteral(obj)) return obj;\n\n  return obj.toJSON();\n}\n", "import {useLayoutEffect} from 'react';\nimport {CameraStateRef} from './use-tracked-camera-state-ref';\nimport {toLatLngLiteral} from '../../libraries/lat-lng-utils';\nimport {MapProps} from '../map';\n\nexport function useMapCameraParams(\n  map: google.maps.Map | null,\n  cameraStateRef: CameraStateRef,\n  mapProps: MapProps\n) {\n  const center = mapProps.center ? toLatLngLiteral(mapProps.center) : null;\n\n  let lat: number | null = null;\n  let lng: number | null = null;\n\n  if (center && Number.isFinite(center.lat) && Number.isFinite(center.lng)) {\n    lat = center.lat as number;\n    lng = center.lng as number;\n  }\n\n  const zoom: number | null = Number.isFinite(mapProps.zoom)\n    ? (mapProps.zoom as number)\n    : null;\n  const heading: number | null = Number.isFinite(mapProps.heading)\n    ? (mapProps.heading as number)\n    : null;\n  const tilt: number | null = Number.isFinite(mapProps.tilt)\n    ? (mapProps.tilt as number)\n    : null;\n\n  // the following effect runs for every render of the map component and checks\n  // if there are differences between the known state of the map instance\n  // (cameraStateRef, which is updated by all bounds_changed events) and the\n  // desired state in the props.\n\n  useLayoutEffect(() => {\n    if (!map) return;\n\n    const nextCamera: google.maps.CameraOptions = {};\n    let needsUpdate = false;\n\n    if (\n      lat !== null &&\n      lng !== null &&\n      (cameraStateRef.current.center.lat !== lat ||\n        cameraStateRef.current.center.lng !== lng)\n    ) {\n      nextCamera.center = {lat, lng};\n      needsUpdate = true;\n    }\n\n    if (zoom !== null && cameraStateRef.current.zoom !== zoom) {\n      nextCamera.zoom = zoom as number;\n      needsUpdate = true;\n    }\n\n    if (heading !== null && cameraStateRef.current.heading !== heading) {\n      nextCamera.heading = heading as number;\n      needsUpdate = true;\n    }\n\n    if (tilt !== null && cameraStateRef.current.tilt !== tilt) {\n      nextCamera.tilt = tilt as number;\n      needsUpdate = true;\n    }\n\n    if (needsUpdate) {\n      map.moveCamera(nextCamera);\n    }\n  });\n}\n", "import React, {CSSProperties, FunctionComponent} from 'react';\n\nexport const AuthFailureMessage: FunctionComponent = () => {\n  const style: CSSProperties = {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    bottom: 0,\n    right: 0,\n    zIndex: 999,\n    display: 'flex',\n    flexFlow: 'column nowrap',\n    textAlign: 'center',\n    justifyContent: 'center',\n    fontSize: '.8rem',\n    color: 'rgba(0,0,0,0.6)',\n    background: '#dddddd',\n    padding: '1rem 1.5rem'\n  };\n\n  return (\n    <div style={style}>\n      <h2>Error: AuthFailure</h2>\n      <p>\n        A problem with your API key prevents the map from rendering correctly.\n        Please make sure the value of the <code>APIProvider.apiKey</code> prop\n        is correct. Check the error-message in the console for further details.\n      </p>\n    </div>\n  );\n};\n", "import {Ref, useCallback, useState} from 'react';\n\nexport function useCallbackRef<T>() {\n  const [el, setEl] = useState<T | null>(null);\n  const ref = useCallback((value: T) => setEl(value), [setEl]);\n\n  return [el, ref as Ref<T>] as const;\n}\n", "import {useApiLoadingStatus} from './use-api-loading-status';\nimport {APILoadingStatus} from '../libraries/api-loading-status';\n/**\n * Hook to check if the Maps JavaScript API is loaded\n */\nexport function useApiIsLoaded(): boolean {\n  const status = useApiLoadingStatus();\n\n  return status === APILoadingStatus.LOADED;\n}\n", "import {useReducer} from 'react';\n\nexport function useForceUpdate(): () => void {\n  const [, forceUpdate] = useReducer(x => x + 1, 0);\n\n  return forceUpdate;\n}\n", "import {MutableRefObject, useEffect, useRef} from 'react';\nimport {useForceUpdate} from '../../libraries/use-force-update';\n\nexport type CameraState = {\n  center: google.maps.LatLngLiteral;\n  heading: number;\n  tilt: number;\n  zoom: number;\n};\n\nexport type CameraStateRef = MutableRefObject<CameraState>;\n\nfunction handleBoundsChange(map: google.maps.Map, ref: CameraStateRef) {\n  const center = map.getCenter();\n  const zoom = map.getZoom();\n  const heading = map.getHeading() || 0;\n  const tilt = map.getTilt() || 0;\n  const bounds = map.getBounds();\n\n  if (!center || !bounds || !Number.isFinite(zoom)) {\n    console.warn(\n      '[useTrackedCameraState] at least one of the values from the map ' +\n        'returned undefined. This is not expected to happen. Please ' +\n        'report an issue at https://github.com/visgl/react-google-maps/issues/new'\n    );\n  }\n\n  // fixme: do we need the `undefined` cases for the camera-params? When are they used in the maps API?\n  Object.assign(ref.current, {\n    center: center?.toJSON() || {lat: 0, lng: 0},\n    zoom: (zoom as number) || 0,\n    heading: heading as number,\n    tilt: tilt as number\n  });\n}\n\n/**\n * Creates a mutable ref object to track the last known state of the map camera.\n * This is used in `useMapCameraParams` to reduce stuttering in normal operation\n * by avoiding updates of the map camera with values that have already been processed.\n */\nexport function useTrackedCameraStateRef(\n  map: google.maps.Map | null\n): CameraStateRef {\n  const forceUpdate = useForceUpdate();\n  const ref = useRef<CameraState>({\n    center: {lat: 0, lng: 0},\n    heading: 0,\n    tilt: 0,\n    zoom: 0\n  });\n\n  // Record camera state with every bounds_changed event dispatched by the map.\n  // This data is used to prevent feeding these values back to the\n  // map-instance when a typical \"controlled component\" setup (state variable is\n  // fed into and updated by the map).\n  useEffect(() => {\n    if (!map) return;\n\n    const listener = google.maps.event.addListener(\n      map,\n      'bounds_changed',\n      () => {\n        handleBoundsChange(map, ref);\n\n        // When an event is occured, we have to update during the next cycle.\n        // The application could decide to ignore the event and not update any\n        // camera props of the map, meaning that in that case we will have to\n        // 'undo' the change to the camera.\n        forceUpdate();\n      }\n    );\n\n    return () => listener.remove();\n  }, [map, forceUpdate]);\n\n  return ref;\n}\n", "import {Ref, useEffect, useRef, useState} from 'react';\n\nimport {MapProps} from '../map';\nimport {APIProviderContextValue} from '../api-provider';\n\nimport {useCallbackRef} from '../../libraries/use-callback-ref';\nimport {useApiIsLoaded} from '../../hooks/use-api-is-loaded';\nimport {\n  CameraState,\n  CameraStateRef,\n  useTrackedCameraStateRef\n} from './use-tracked-camera-state-ref';\n\n/**\n * Stores a stack of map-instances for each mapId. Whenever an\n * instance is used, it is removed from the stack while in use,\n * and returned to the stack when the component unmounts.\n * This allows us to correctly implement caching for multiple\n * maps om the same page, while reusing as much as possible.\n *\n * FIXME: while it should in theory be possible to reuse maps solely\n *   based on the mapId (as all other parameters can be changed at\n *   runtime), we don't yet have good enough tracking of options to\n *   reliably unset all the options that have been set.\n */\nclass CachedMapStack {\n  static entries: {[key: string]: google.maps.Map[]} = {};\n\n  static has(key: string) {\n    return this.entries[key] && this.entries[key].length > 0;\n  }\n\n  static pop(key: string) {\n    if (!this.entries[key]) return null;\n\n    return this.entries[key].pop() || null;\n  }\n\n  static push(key: string, value: google.maps.Map) {\n    if (!this.entries[key]) this.entries[key] = [];\n\n    this.entries[key].push(value);\n  }\n}\n\n/**\n * The main hook takes care of creating map-instances and registering them in\n * the api-provider context.\n * @return a tuple of the map-instance created (or null) and the callback\n *   ref that will be used to pass the map-container into this hook.\n * @internal\n */\nexport function useMapInstance(\n  props: MapProps,\n  context: APIProviderContextValue\n): readonly [\n  map: google.maps.Map | null,\n  containerRef: Ref<HTMLDivElement>,\n  cameraStateRef: CameraStateRef\n] {\n  const apiIsLoaded = useApiIsLoaded();\n  const [map, setMap] = useState<google.maps.Map | null>(null);\n  const [container, containerRef] = useCallbackRef<HTMLDivElement>();\n\n  const cameraStateRef = useTrackedCameraStateRef(map);\n\n  const {\n    id,\n    defaultBounds,\n    defaultCenter,\n    defaultZoom,\n    defaultHeading,\n    defaultTilt,\n    reuseMaps,\n    renderingType,\n    colorScheme,\n\n    ...mapOptions\n  } = props;\n\n  const hasZoom = props.zoom !== undefined || props.defaultZoom !== undefined;\n  const hasCenter =\n    props.center !== undefined || props.defaultCenter !== undefined;\n\n  if (!defaultBounds && (!hasZoom || !hasCenter)) {\n    console.warn(\n      '<Map> component is missing configuration. ' +\n        'You have to provide zoom and center (via the `zoom`/`defaultZoom` and ' +\n        '`center`/`defaultCenter` props) or specify the region to show using ' +\n        '`defaultBounds`. See ' +\n        'https://visgl.github.io/react-google-maps/docs/api-reference/components/map#required'\n    );\n  }\n\n  // apply default camera props if available and not overwritten by controlled props\n  if (!mapOptions.center && defaultCenter) mapOptions.center = defaultCenter;\n  if (!mapOptions.zoom && Number.isFinite(defaultZoom))\n    mapOptions.zoom = defaultZoom;\n  if (!mapOptions.heading && Number.isFinite(defaultHeading))\n    mapOptions.heading = defaultHeading;\n  if (!mapOptions.tilt && Number.isFinite(defaultTilt))\n    mapOptions.tilt = defaultTilt;\n\n  for (const key of Object.keys(mapOptions) as (keyof typeof mapOptions)[])\n    if (mapOptions[key] === undefined) delete mapOptions[key];\n\n  const savedMapStateRef = useRef<{\n    mapId?: string | null;\n    cameraState: CameraState;\n  }>(undefined);\n\n  // create the map instance and register it in the context\n  useEffect(\n    () => {\n      if (!container || !apiIsLoaded) return;\n\n      const {addMapInstance, removeMapInstance} = context;\n\n      // note: colorScheme (upcoming feature) isn't yet in the typings, remove once that is fixed:\n      const {mapId} = props;\n      const cacheKey = `${mapId || 'default'}:${renderingType || 'default'}:${colorScheme || 'LIGHT'}`;\n\n      let mapDiv: HTMLElement;\n      let map: google.maps.Map;\n\n      if (reuseMaps && CachedMapStack.has(cacheKey)) {\n        map = CachedMapStack.pop(cacheKey) as google.maps.Map;\n        mapDiv = map.getDiv();\n\n        container.appendChild(mapDiv);\n        map.setOptions(mapOptions);\n\n        // detaching the element from the DOM lets the map fall back to its default\n        // size, setting the center will trigger reloading the map.\n        setTimeout(() => map.setCenter(map.getCenter()!), 0);\n      } else {\n        mapDiv = document.createElement('div');\n        mapDiv.style.height = '100%';\n        container.appendChild(mapDiv);\n\n        map = new google.maps.Map(mapDiv, {\n          ...mapOptions,\n          ...(renderingType\n            ? {renderingType: renderingType as google.maps.RenderingType}\n            : {}),\n          ...(colorScheme\n            ? {colorScheme: colorScheme as google.maps.ColorScheme}\n            : {})\n        });\n      }\n\n      setMap(map);\n      addMapInstance(map, id);\n\n      if (defaultBounds) {\n        const {padding, ...defBounds} = defaultBounds;\n        map.fitBounds(defBounds, padding);\n      }\n\n      // prevent map not rendering due to missing configuration\n      else if (!hasZoom || !hasCenter) {\n        map.fitBounds({east: 180, west: -180, south: -90, north: 90});\n      }\n\n      // the savedMapState is used to restore the camera parameters when the mapId is changed\n      if (savedMapStateRef.current) {\n        const {mapId: savedMapId, cameraState: savedCameraState} =\n          savedMapStateRef.current;\n        if (savedMapId !== mapId) {\n          map.setOptions(savedCameraState);\n        }\n      }\n\n      return () => {\n        savedMapStateRef.current = {\n          mapId,\n          // eslint-disable-next-line react-hooks/exhaustive-deps\n          cameraState: cameraStateRef.current\n        };\n\n        // detach the map-div from the dom\n        mapDiv.remove();\n\n        if (reuseMaps) {\n          // push back on the stack\n          CachedMapStack.push(cacheKey, map);\n        } else {\n          // remove all event-listeners to minimize the possibility of memory-leaks\n          google.maps.event.clearInstanceListeners(map);\n        }\n\n        setMap(null);\n        removeMapInstance(id);\n      };\n    },\n\n    // some dependencies are ignored in the list below:\n    //  - defaultBounds and the default* camera props will only be used once, and\n    //    changes should be ignored\n    //  - mapOptions has special hooks that take care of updating the options\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      container,\n      apiIsLoaded,\n      id,\n\n      // these props can't be changed after initialization and require a new\n      // instance to be created\n      props.mapId,\n      props.renderingType,\n      props.colorScheme\n    ]\n  );\n\n  return [map, containerRef, cameraStateRef] as const;\n}\n", "/* eslint-disable complexity */\nimport React, {\n  CSSProperties,\n  FunctionComponent,\n  PropsWithChildren,\n  useContext,\n  useEffect,\n  useLayoutEffect,\n  useMemo\n} from 'react';\n\nimport {APIProviderContext} from '../api-provider';\n\nimport {MapEventProps, useMapEvents} from './use-map-events';\nimport {useMapOptions} from './use-map-options';\nimport {useApiLoadingStatus} from '../../hooks/use-api-loading-status';\nimport {APILoadingStatus} from '../../libraries/api-loading-status';\nimport {\n  DeckGlCompatProps,\n  useDeckGLCameraUpdate\n} from './use-deckgl-camera-update';\nimport {toLatLngLiteral} from '../../libraries/lat-lng-utils';\nimport {useMapCameraParams} from './use-map-camera-params';\nimport {AuthFailureMessage} from './auth-failure-message';\nimport {useMapInstance} from './use-map-instance';\n\nexport interface GoogleMapsContextValue {\n  map: google.maps.Map | null;\n}\nexport const GoogleMapsContext =\n  React.createContext<GoogleMapsContextValue | null>(null);\n\nexport type {\n  MapCameraChangedEvent,\n  MapEvent,\n  MapEventProps,\n  MapMouseEvent\n} from './use-map-events';\n\nexport type MapCameraProps = {\n  center: google.maps.LatLngLiteral;\n  zoom: number;\n  heading?: number;\n  tilt?: number;\n};\n\n// ColorScheme and RenderingType are redefined here to make them usable before the\n// maps API has been fully loaded.\n\nexport const ColorScheme = {\n  DARK: 'DARK',\n  LIGHT: 'LIGHT',\n  FOLLOW_SYSTEM: 'FOLLOW_SYSTEM'\n} as const;\nexport type ColorScheme = (typeof ColorScheme)[keyof typeof ColorScheme];\n\nexport const RenderingType = {\n  VECTOR: 'VECTOR',\n  RASTER: 'RASTER',\n  UNINITIALIZED: 'UNINITIALIZED'\n} as const;\nexport type RenderingType = (typeof RenderingType)[keyof typeof RenderingType];\n\n/**\n * Props for the Map Component\n */\nexport type MapProps = PropsWithChildren<\n  Omit<google.maps.MapOptions, 'renderingType' | 'colorScheme'> &\n    MapEventProps &\n    DeckGlCompatProps & {\n      /**\n       * An id for the map, this is required when multiple maps are present\n       * in the same APIProvider context.\n       */\n      id?: string;\n\n      /**\n       * Additional style rules to apply to the map dom-element.\n       */\n      style?: CSSProperties;\n\n      /**\n       * Additional css class-name to apply to the element containing the map.\n       */\n      className?: string;\n\n      /**\n       * The color-scheme to use for the map.\n       */\n      colorScheme?: ColorScheme;\n\n      /**\n       * The rendering-type to be used.\n       */\n      renderingType?: RenderingType;\n\n      /**\n       * Indicates that the map will be controlled externally. Disables all controls provided by the map itself.\n       */\n      controlled?: boolean;\n\n      /**\n       * Enable caching of map-instances created by this component.\n       */\n      reuseMaps?: boolean;\n\n      defaultCenter?: google.maps.LatLngLiteral;\n      defaultZoom?: number;\n      defaultHeading?: number;\n      defaultTilt?: number;\n      /**\n       * Alternative way to specify the default camera props as a geographic region that should be fully visible\n       */\n      defaultBounds?: google.maps.LatLngBoundsLiteral & {\n        padding?: number | google.maps.Padding;\n      };\n    }\n>;\n\nexport const Map: FunctionComponent<MapProps> = (props: MapProps) => {\n  const {children, id, className, style} = props;\n  const context = useContext(APIProviderContext);\n  const loadingStatus = useApiLoadingStatus();\n\n  if (!context) {\n    throw new Error(\n      '<Map> can only be used inside an <ApiProvider> component.'\n    );\n  }\n\n  const [map, mapRef, cameraStateRef] = useMapInstance(props, context);\n\n  useMapCameraParams(map, cameraStateRef, props);\n  useMapEvents(map, props);\n  useMapOptions(map, props);\n\n  const isDeckGlControlled = useDeckGLCameraUpdate(map, props);\n  const isControlledExternally = !!props.controlled;\n\n  // disable interactions with the map for externally controlled maps\n  useEffect(() => {\n    if (!map) return;\n\n    // fixme: this doesn't seem to belong here (and it's mostly there for convenience anyway).\n    //   The reasoning is that a deck.gl canvas will be put on top of the map, rendering\n    //   any default map controls pretty much useless\n    if (isDeckGlControlled) {\n      map.setOptions({disableDefaultUI: true});\n    }\n\n    // disable all control-inputs when the map is controlled externally\n    if (isDeckGlControlled || isControlledExternally) {\n      map.setOptions({\n        gestureHandling: 'none',\n        keyboardShortcuts: false\n      });\n    }\n\n    return () => {\n      map.setOptions({\n        gestureHandling: props.gestureHandling,\n        keyboardShortcuts: props.keyboardShortcuts\n      });\n    };\n  }, [\n    map,\n    isDeckGlControlled,\n    isControlledExternally,\n    props.gestureHandling,\n    props.keyboardShortcuts\n  ]);\n\n  // setup a stable cameraOptions object that can be used as dependency\n  const center = props.center ? toLatLngLiteral(props.center) : null;\n  let lat: number | null = null;\n  let lng: number | null = null;\n  if (center && Number.isFinite(center.lat) && Number.isFinite(center.lng)) {\n    lat = center.lat as number;\n    lng = center.lng as number;\n  }\n\n  const cameraOptions: google.maps.CameraOptions = useMemo(() => {\n    return {\n      center: {lat: lat ?? 0, lng: lng ?? 0},\n      zoom: props.zoom ?? 0,\n      heading: props.heading ?? 0,\n      tilt: props.tilt ?? 0\n    };\n  }, [lat, lng, props.zoom, props.heading, props.tilt]);\n\n  // externally controlled mode: reject all camera changes that don't correspond to changes in props\n  useLayoutEffect(() => {\n    if (!map || !isControlledExternally) return;\n\n    map.moveCamera(cameraOptions);\n    const listener = map.addListener('bounds_changed', () => {\n      map.moveCamera(cameraOptions);\n    });\n\n    return () => listener.remove();\n  }, [map, isControlledExternally, cameraOptions]);\n\n  const combinedStyle: CSSProperties = useMemo(\n    () => ({\n      width: '100%',\n      height: '100%',\n      position: 'relative',\n      // when using deckgl, the map should be sent to the back\n      zIndex: isDeckGlControlled ? -1 : 0,\n\n      ...style\n    }),\n    [style, isDeckGlControlled]\n  );\n\n  const contextValue: GoogleMapsContextValue = useMemo(() => ({map}), [map]);\n\n  if (loadingStatus === APILoadingStatus.AUTH_FAILURE) {\n    return (\n      <div\n        style={{position: 'relative', ...(className ? {} : combinedStyle)}}\n        className={className}>\n        <AuthFailureMessage />\n      </div>\n    );\n  }\n\n  return (\n    <div\n      ref={mapRef}\n      data-testid={'map'}\n      style={className ? undefined : combinedStyle}\n      className={className}\n      {...(id ? {id} : {})}>\n      {map ? (\n        <GoogleMapsContext.Provider value={contextValue}>\n          {children}\n        </GoogleMapsContext.Provider>\n      ) : null}\n    </div>\n  );\n};\n\n// The deckGLViewProps flag here indicates to deck.gl that the Map component is\n// able to handle viewProps from deck.gl when deck.gl is used to control the map.\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n(Map as any).deckGLViewProps = true;\n", "const shownMessages = new Set();\n\nexport function logErrorOnce(...args: Parameters<typeof console.error>) {\n  const key = JSON.stringify(args);\n\n  if (!shownMessages.has(key)) {\n    shownMessages.add(key);\n\n    console.error(...args);\n  }\n}\n", "import {useContext} from 'react';\n\nimport {APIProviderContext} from '../components/api-provider';\nimport {GoogleMapsContext} from '../components/map';\nimport {logErrorOnce} from '../libraries/errors';\n\n/**\n * Retrieves a map-instance from the context. This is either an instance\n * identified by id or the parent map instance if no id is specified.\n * Returns null if neither can be found.\n */\nexport const useMap = (id: string | null = null): google.maps.Map | null => {\n  const ctx = useContext(APIProviderContext);\n  const {map} = useContext(GoogleMapsContext) || {};\n\n  if (ctx === null) {\n    logErrorOnce(\n      'useMap(): failed to retrieve APIProviderContext. ' +\n        'Make sure that the <APIProvider> component exists and that the ' +\n        'component you are calling `useMap()` from is a sibling of the ' +\n        '<APIProvider>.'\n    );\n\n    return null;\n  }\n\n  const {mapInstances} = ctx;\n\n  // if an id is specified, the corresponding map or null is returned\n  if (id !== null) return mapInstances[id] || null;\n\n  // otherwise, return the closest ancestor\n  if (map) return map;\n\n  // finally, return the default map instance\n  return mapInstances['default'] || null;\n};\n", "import {useContext, useEffect} from 'react';\n\nimport {APIProviderContext} from '../components/api-provider';\nimport {useApiIsLoaded} from './use-api-is-loaded';\n\ninterface ApiLibraries {\n  core: google.maps.CoreLibrary;\n  maps: google.maps.MapsLibrary;\n  places: google.maps.PlacesLibrary;\n  geocoding: google.maps.GeocodingLibrary;\n  routes: google.maps.RoutesLibrary;\n  marker: google.maps.MarkerLibrary;\n  geometry: google.maps.GeometryLibrary;\n  elevation: google.maps.ElevationLibrary;\n  streetView: google.maps.StreetViewLibrary;\n  journeySharing: google.maps.JourneySharingLibrary;\n  drawing: google.maps.DrawingLibrary;\n  visualization: google.maps.VisualizationLibrary;\n}\n\nexport function useMapsLibrary<\n  K extends keyof ApiLibraries,\n  V extends ApiLibraries[K]\n>(name: K): V | null;\n\nexport function useMapsLibrary(name: string) {\n  const apiIsLoaded = useApiIsLoaded();\n  const ctx = useContext(APIProviderContext);\n\n  useEffect(() => {\n    if (!apiIsLoaded || !ctx) return;\n\n    // Trigger loading the libraries via our proxy-method.\n    // The returned promise is ignored, since importLibrary will update loadedLibraries\n    // list in the context, triggering a re-render.\n    void ctx.importLibrary(name);\n  }, [apiIsLoaded, ctx, name]);\n\n  return ctx?.loadedLibraries[name] || null;\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport {useEffect} from 'react';\n\n/**\n * Internally used to bind events to Maps JavaScript API objects.\n * @internal\n */\nexport function useMapsEventListener<T extends (...args: any[]) => void>(\n  target?: object | null,\n  name?: string,\n  callback?: T | null\n) {\n  useEffect(() => {\n    if (!target || !name || !callback) return;\n\n    const listener = google.maps.event.addListener(target, name, callback);\n\n    return () => listener.remove();\n  }, [target, name, callback]);\n}\n", "import {useEffect} from 'react';\n\n/**\n * Internally used to copy values from props into API-Objects\n * whenever they change.\n *\n * @example\n *   usePropBinding(marker, 'position', position);\n *\n * @internal\n */\nexport function usePropBinding<T extends object, K extends keyof T>(\n  object: T | null,\n  prop: K,\n  value: T[K]\n) {\n  useEffect(() => {\n    if (!object) return;\n\n    object[prop] = value;\n  }, [object, prop, value]);\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport {useEffect} from 'react';\n\n/**\n * Internally used to bind events to DOM nodes.\n * @internal\n */\nexport function useDomEventListener<T extends (...args: any[]) => void>(\n  target?: Node | null,\n  name?: string,\n  callback?: T | null\n) {\n  useEffect(() => {\n    if (!target || !name || !callback) return;\n\n    target.addEventListener(name, callback);\n\n    return () => target.removeEventListener(name, callback);\n  }, [target, name, callback]);\n}\n", "/* eslint-disable complexity */\nimport React, {\n  Children,\n  CSSProperties,\n  forwardRef,\n  useCallback,\n  useEffect,\n  useImperativeHandle,\n  useMemo,\n  useState\n} from 'react';\n\nimport {createPortal} from 'react-dom';\nimport {useMap} from '../hooks/use-map';\nimport {useMapsLibrary} from '../hooks/use-maps-library';\n\nimport type {Ref, PropsWithChildren} from 'react';\nimport {useMapsEventListener} from '../hooks/use-maps-event-listener';\nimport {usePropBinding} from '../hooks/use-prop-binding';\nimport {useDomEventListener} from '../hooks/use-dom-event-listener';\n\nexport interface AdvancedMarkerContextValue {\n  marker: google.maps.marker.AdvancedMarkerElement;\n}\n\nexport function isAdvancedMarker(\n  marker: google.maps.Marker | google.maps.marker.AdvancedMarkerElement\n): marker is google.maps.marker.AdvancedMarkerElement {\n  return (\n    (marker as google.maps.marker.AdvancedMarkerElement).content !== undefined\n  );\n}\n\nfunction isElementNode(node: Node): node is HTMLElement {\n  return node.nodeType === Node.ELEMENT_NODE;\n}\n\n/**\n * Copy of the `google.maps.CollisionBehavior` constants.\n * They have to be duplicated here since we can't wait for the maps API to load to be able to use them.\n */\nexport const CollisionBehavior = {\n  REQUIRED: 'REQUIRED',\n  REQUIRED_AND_HIDES_OPTIONAL: 'REQUIRED_AND_HIDES_OPTIONAL',\n  OPTIONAL_AND_HIDES_LOWER_PRIORITY: 'OPTIONAL_AND_HIDES_LOWER_PRIORITY'\n} as const;\nexport type CollisionBehavior =\n  (typeof CollisionBehavior)[keyof typeof CollisionBehavior];\n\nexport const AdvancedMarkerContext =\n  React.createContext<AdvancedMarkerContextValue | null>(null);\n\n// [xPosition, yPosition] when the top left corner is [0, 0]\nexport const AdvancedMarkerAnchorPoint = {\n  TOP_LEFT: ['0%', '0%'],\n  TOP_CENTER: ['50%', '0%'],\n  TOP: ['50%', '0%'],\n  TOP_RIGHT: ['100%', '0%'],\n  LEFT_CENTER: ['0%', '50%'],\n  LEFT_TOP: ['0%', '0%'],\n  LEFT: ['0%', '50%'],\n  LEFT_BOTTOM: ['0%', '100%'],\n  RIGHT_TOP: ['100%', '0%'],\n  RIGHT: ['100%', '50%'],\n  RIGHT_CENTER: ['100%', '50%'],\n  RIGHT_BOTTOM: ['100%', '100%'],\n  BOTTOM_LEFT: ['0%', '100%'],\n  BOTTOM_CENTER: ['50%', '100%'],\n  BOTTOM: ['50%', '100%'],\n  BOTTOM_RIGHT: ['100%', '100%'],\n  CENTER: ['50%', '50%']\n} as const;\n\nexport type AdvancedMarkerAnchorPoint =\n  (typeof AdvancedMarkerAnchorPoint)[keyof typeof AdvancedMarkerAnchorPoint];\n\ntype AdvancedMarkerEventProps = {\n  onClick?: (e: google.maps.MapMouseEvent) => void;\n  onMouseEnter?: (e: google.maps.MapMouseEvent['domEvent']) => void;\n  onMouseLeave?: (e: google.maps.MapMouseEvent['domEvent']) => void;\n  onDrag?: (e: google.maps.MapMouseEvent) => void;\n  onDragStart?: (e: google.maps.MapMouseEvent) => void;\n  onDragEnd?: (e: google.maps.MapMouseEvent) => void;\n};\n\nexport type AdvancedMarkerProps = PropsWithChildren<\n  Omit<\n    google.maps.marker.AdvancedMarkerElementOptions,\n    'gmpDraggable' | 'gmpClickable' | 'content' | 'map' | 'collisionBehavior'\n  > &\n    AdvancedMarkerEventProps & {\n      draggable?: boolean;\n      clickable?: boolean;\n      collisionBehavior?: CollisionBehavior;\n      /**\n       * The anchor point for the Advanced Marker.\n       * Either use one of the predefined anchor point from the \"AdvancedMarkerAnchorPoint\" export\n       * or provide a string tuple in the form of [\"xPosition\", \"yPosition\"].\n       * The position is measured from the top-left corner and\n       * can be anything that can be consumed by a CSS translate() function.\n       * For example in percent (\"50%\") or in pixels (\"20px\").\n       */\n      anchorPoint?: AdvancedMarkerAnchorPoint | [string, string];\n      /**\n       * A className for the content element.\n       * (can only be used with HTML Marker content)\n       */\n      className?: string;\n      /**\n       * Additional styles to apply to the content element.\n       */\n      style?: CSSProperties;\n    }\n>;\n\ntype MarkerContentProps = PropsWithChildren & {\n  styles?: CSSProperties;\n  className?: string;\n  anchorPoint?: AdvancedMarkerAnchorPoint | [string, string];\n};\n\nconst MarkerContent = ({\n  children,\n  styles,\n  className,\n  anchorPoint\n}: MarkerContentProps) => {\n  const [xTranslation, yTranslation] =\n    anchorPoint ?? AdvancedMarkerAnchorPoint['BOTTOM'];\n\n  let xTranslationFlipped = `-${xTranslation}`;\n  let yTranslationFlipped = `-${yTranslation}`;\n  if (xTranslation.trimStart().startsWith('-')) {\n    xTranslationFlipped = xTranslation.substring(1);\n  }\n  if (yTranslation.trimStart().startsWith('-')) {\n    yTranslationFlipped = yTranslation.substring(1);\n  }\n\n  // The \"translate(50%, 100%)\" is here to counter and reset the default anchoring of the advanced marker element\n  // that comes from the api\n  const transformStyle = `translate(50%, 100%) translate(${xTranslationFlipped}, ${yTranslationFlipped})`;\n\n  return (\n    // anchoring container\n    <div style={{transform: transformStyle}}>\n      {/* AdvancedMarker div that user can give styles and classes */}\n      <div className={className} style={styles}>\n        {children}\n      </div>\n    </div>\n  );\n};\n\nexport type CustomMarkerContent =\n  | (HTMLDivElement & {isCustomMarker?: boolean})\n  | null;\n\nexport type AdvancedMarkerRef = google.maps.marker.AdvancedMarkerElement | null;\nfunction useAdvancedMarker(props: AdvancedMarkerProps) {\n  const [marker, setMarker] =\n    useState<google.maps.marker.AdvancedMarkerElement | null>(null);\n  const [contentContainer, setContentContainer] =\n    useState<HTMLDivElement | null>(null);\n\n  const map = useMap();\n  const markerLibrary = useMapsLibrary('marker');\n\n  const {\n    children,\n    onClick,\n    className,\n    onMouseEnter,\n    onMouseLeave,\n    onDrag,\n    onDragStart,\n    onDragEnd,\n    collisionBehavior,\n    clickable,\n    draggable,\n    position,\n    title,\n    zIndex\n  } = props;\n\n  const numChildren = Children.count(children);\n\n  // create an AdvancedMarkerElement instance and add it to the map once available\n  useEffect(() => {\n    if (!map || !markerLibrary) return;\n\n    const newMarker = new markerLibrary.AdvancedMarkerElement();\n    newMarker.map = map;\n\n    setMarker(newMarker);\n\n    // create the container for marker content if there are children\n    let contentElement: CustomMarkerContent = null;\n    if (numChildren > 0) {\n      contentElement = document.createElement('div');\n\n      // We need some kind of flag to identify the custom marker content\n      // in the infowindow component. Choosing a custom property instead of a className\n      // to not encourage users to style the marker content directly.\n      contentElement.isCustomMarker = true;\n\n      newMarker.content = contentElement;\n      setContentContainer(contentElement);\n    }\n\n    return () => {\n      newMarker.map = null;\n      contentElement?.remove();\n      setMarker(null);\n      setContentContainer(null);\n    };\n  }, [map, markerLibrary, numChildren]);\n\n  // When no children are present we don't have our own wrapper div\n  // which usually gets the user provided className. In this case\n  // we set the className directly on the marker.content element that comes\n  // with the AdvancedMarker.\n  useEffect(() => {\n    if (!marker || !marker.content || numChildren > 0) return;\n\n    (marker.content as HTMLElement).className = className || '';\n  }, [marker, className, numChildren]);\n\n  // copy other props\n  usePropBinding(marker, 'position', position);\n  usePropBinding(marker, 'title', title ?? '');\n  usePropBinding(marker, 'zIndex', zIndex);\n  usePropBinding(\n    marker,\n    'collisionBehavior',\n    collisionBehavior as google.maps.CollisionBehavior\n  );\n\n  // set gmpDraggable from props (when unspecified, it's true if any drag-event\n  // callbacks are specified)\n  useEffect(() => {\n    if (!marker) return;\n\n    if (draggable !== undefined) marker.gmpDraggable = draggable;\n    else if (onDrag || onDragStart || onDragEnd) marker.gmpDraggable = true;\n    else marker.gmpDraggable = false;\n  }, [marker, draggable, onDrag, onDragEnd, onDragStart]);\n\n  // set gmpClickable from props (when unspecified, it's true if the onClick or one of\n  // the hover events callbacks are specified)\n  useEffect(() => {\n    if (!marker) return;\n\n    const gmpClickable =\n      clickable !== undefined ||\n      Boolean(onClick) ||\n      Boolean(onMouseEnter) ||\n      Boolean(onMouseLeave);\n\n    // gmpClickable is only available in beta version of the\n    // maps api (as of 2024-10-10)\n    marker.gmpClickable = gmpClickable;\n\n    // enable pointer events for the markers with custom content\n    if (gmpClickable && marker?.content && isElementNode(marker.content)) {\n      marker.content.style.pointerEvents = 'none';\n\n      if (marker.content.firstElementChild) {\n        (marker.content.firstElementChild as HTMLElement).style.pointerEvents =\n          'all';\n      }\n    }\n  }, [marker, clickable, onClick, onMouseEnter, onMouseLeave]);\n\n  useMapsEventListener(marker, 'click', onClick);\n  useMapsEventListener(marker, 'drag', onDrag);\n  useMapsEventListener(marker, 'dragstart', onDragStart);\n  useMapsEventListener(marker, 'dragend', onDragEnd);\n\n  useDomEventListener(marker?.element, 'mouseenter', onMouseEnter);\n  useDomEventListener(marker?.element, 'mouseleave', onMouseLeave);\n\n  return [marker, contentContainer] as const;\n}\n\nexport const AdvancedMarker = forwardRef(\n  (props: AdvancedMarkerProps, ref: Ref<AdvancedMarkerRef>) => {\n    const {children, style, className, anchorPoint} = props;\n    const [marker, contentContainer] = useAdvancedMarker(props);\n\n    const advancedMarkerContextValue: AdvancedMarkerContextValue | null =\n      useMemo(() => (marker ? {marker} : null), [marker]);\n\n    useImperativeHandle(\n      ref,\n      () => marker as google.maps.marker.AdvancedMarkerElement,\n      [marker]\n    );\n\n    if (!contentContainer) return null;\n\n    return (\n      <AdvancedMarkerContext.Provider value={advancedMarkerContextValue}>\n        {createPortal(\n          <MarkerContent\n            anchorPoint={anchorPoint}\n            styles={style}\n            className={className}>\n            {children}\n          </MarkerContent>,\n          contentContainer\n        )}\n      </AdvancedMarkerContext.Provider>\n    );\n  }\n);\n\nexport function useAdvancedMarkerRef() {\n  const [marker, setMarker] =\n    useState<google.maps.marker.AdvancedMarkerElement | null>(null);\n\n  const refCallback = useCallback((m: AdvancedMarkerRef | null) => {\n    setMarker(m);\n  }, []);\n\n  return [refCallback, marker] as const;\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n/*\n * The code in this file was adapted from the internal react-dom-bindings package.\n * https://github.com/facebook/react/tree/4508873393058e86bed308b56e49ec883ece59d1/packages/react-dom-bindings\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport {CSSProperties} from 'react';\n\nexport function setValueForStyles(\n  element: HTMLElement,\n  styles: CSSProperties | null,\n  prevStyles: CSSProperties | null\n) {\n  if (styles != null && typeof styles !== 'object') {\n    throw new Error(\n      'The `style` prop expects a mapping from style properties to values, ' +\n        \"not a string. For example, style={{marginRight: spacing + 'em'}} when \" +\n        'using JSX.'\n    );\n  }\n\n  const elementStyle = element.style;\n\n  // without `prevStyles`, just set all values\n  if (prevStyles == null) {\n    if (styles == null) return;\n\n    for (const styleName in styles) {\n      if (!styles.hasOwnProperty(styleName)) continue;\n\n      setValueForStyle(\n        elementStyle,\n        styleName,\n        styles[styleName as keyof CSSProperties]\n      );\n    }\n\n    return;\n  }\n\n  // unset all styles in `prevStyles` that aren't in `styles`\n  for (const styleName in prevStyles) {\n    if (\n      prevStyles.hasOwnProperty(styleName) &&\n      (styles == null || !styles.hasOwnProperty(styleName))\n    ) {\n      // Clear style\n      const isCustomProperty = styleName.indexOf('--') === 0;\n      if (isCustomProperty) {\n        elementStyle.setProperty(styleName, '');\n      } else if (styleName === 'float') {\n        elementStyle.cssFloat = '';\n      } else {\n        elementStyle[styleName as any] = '';\n      }\n    }\n  }\n\n  // only assign values from `styles` that are different from `prevStyles`\n  if (styles == null) return;\n\n  for (const styleName in styles) {\n    const value = styles[styleName as keyof CSSProperties];\n    if (\n      styles.hasOwnProperty(styleName) &&\n      prevStyles[styleName as keyof CSSProperties] !== value\n    ) {\n      setValueForStyle(elementStyle, styleName, value);\n    }\n  }\n}\n\nfunction setValueForStyle(\n  elementStyle: CSSStyleDeclaration,\n  styleName: string,\n  value: unknown\n) {\n  const isCustomProperty = styleName.indexOf('--') === 0;\n\n  // falsy values will unset the style property\n  if (value == null || typeof value === 'boolean' || value === '') {\n    if (isCustomProperty) {\n      elementStyle.setProperty(styleName, '');\n    } else if (styleName === 'float') {\n      elementStyle.cssFloat = '';\n    } else {\n      elementStyle[styleName as any] = '';\n    }\n  }\n\n  // custom properties can't be directly assigned\n  else if (isCustomProperty) {\n    elementStyle.setProperty(styleName, value as string);\n  }\n\n  // numeric values are treated as 'px' unless the style property expects unitless numbers\n  else if (\n    typeof value === 'number' &&\n    value !== 0 &&\n    !isUnitlessNumber(styleName)\n  ) {\n    elementStyle[styleName as any] = value + 'px'; // Presumes implicit 'px' suffix for unitless numbers\n  }\n\n  // everything else can just be assigned\n  else {\n    if (styleName === 'float') {\n      elementStyle.cssFloat = value as string;\n    } else {\n      elementStyle[styleName as any] = ('' + value).trim();\n    }\n  }\n}\n\n// CSS properties which accept numbers but are not in units of \"px\".\nconst unitlessNumbers = new Set([\n  'animationIterationCount',\n  'aspectRatio',\n  'borderImageOutset',\n  'borderImageSlice',\n  'borderImageWidth',\n  'boxFlex',\n  'boxFlexGroup',\n  'boxOrdinalGroup',\n  'columnCount',\n  'columns',\n  'flex',\n  'flexGrow',\n  'flexPositive',\n  'flexShrink',\n  'flexNegative',\n  'flexOrder',\n  'gridArea',\n  'gridRow',\n  'gridRowEnd',\n  'gridRowSpan',\n  'gridRowStart',\n  'gridColumn',\n  'gridColumnEnd',\n  'gridColumnSpan',\n  'gridColumnStart',\n  'fontWeight',\n  'lineClamp',\n  'lineHeight',\n  'opacity',\n  'order',\n  'orphans',\n  'scale',\n  'tabSize',\n  'widows',\n  'zIndex',\n  'zoom',\n  'fillOpacity', // SVG-related properties\n  'floodOpacity',\n  'stopOpacity',\n  'strokeDasharray',\n  'strokeDashoffset',\n  'strokeMiterlimit',\n  'strokeOpacity',\n  'strokeWidth'\n]);\nfunction isUnitlessNumber(name: string): boolean {\n  return unitlessNumbers.has(name);\n}\n", "/* eslint-disable complexity */\nimport React, {\n  CSSProperties,\n  FunctionComponent,\n  PropsWithChildren,\n  ReactNode,\n  useEffect,\n  useRef,\n  useState\n} from 'react';\nimport {createPortal} from 'react-dom';\n\nimport {useMap} from '../hooks/use-map';\nimport {useMapsEventListener} from '../hooks/use-maps-event-listener';\nimport {setValueForStyles} from '../libraries/set-value-for-styles';\nimport {useMapsLibrary} from '../hooks/use-maps-library';\nimport {useDeepCompareEffect} from '../libraries/use-deep-compare-effect';\nimport {CustomMarkerContent, isAdvancedMarker} from './advanced-marker';\n\nexport type InfoWindowProps = Omit<\n  google.maps.InfoWindowOptions,\n  'headerContent' | 'content' | 'pixelOffset'\n> & {\n  style?: CSSProperties;\n  className?: string;\n  anchor?: google.maps.Marker | google.maps.marker.AdvancedMarkerElement | null;\n  pixelOffset?: [number, number];\n  shouldFocus?: boolean;\n  onClose?: () => void;\n  onCloseClick?: () => void;\n\n  headerContent?: ReactNode;\n};\n\n/**\n * Component to render an Info Window with the Maps JavaScript API\n */\nexport const InfoWindow: FunctionComponent<\n  PropsWithChildren<InfoWindowProps>\n> = props => {\n  const {\n    // content options\n    children,\n    headerContent,\n\n    style,\n    className,\n    pixelOffset,\n\n    // open options\n    anchor,\n    shouldFocus,\n\n    // events\n    onClose,\n    onCloseClick,\n\n    // other options\n    ...infoWindowOptions\n  } = props;\n\n  // ## create infowindow instance once the mapsLibrary is available.\n  const mapsLibrary = useMapsLibrary('maps');\n  const [infoWindow, setInfoWindow] = useState<google.maps.InfoWindow | null>(\n    null\n  );\n\n  const contentContainerRef = useRef<HTMLElement | null>(null);\n  const headerContainerRef = useRef<HTMLElement | null>(null);\n\n  useEffect(\n    () => {\n      if (!mapsLibrary) return;\n\n      contentContainerRef.current = document.createElement('div');\n      headerContainerRef.current = document.createElement('div');\n\n      const opts: google.maps.InfoWindowOptions = infoWindowOptions;\n      if (pixelOffset) {\n        opts.pixelOffset = new google.maps.Size(pixelOffset[0], pixelOffset[1]);\n      }\n\n      if (headerContent) {\n        // if headerContent is specified as string we can directly forward it,\n        // otherwise we'll pass the element the portal will render into\n        opts.headerContent =\n          typeof headerContent === 'string'\n            ? headerContent\n            : headerContainerRef.current;\n      }\n\n      // intentionally shadowing the state variables here\n      const infoWindow = new google.maps.InfoWindow(infoWindowOptions);\n      infoWindow.setContent(contentContainerRef.current);\n\n      setInfoWindow(infoWindow);\n\n      // unmount: remove infoWindow and content elements (note: close is called in a different effect-cleanup)\n      return () => {\n        infoWindow.setContent(null);\n\n        contentContainerRef.current?.remove();\n        headerContainerRef.current?.remove();\n\n        contentContainerRef.current = null;\n        headerContainerRef.current = null;\n\n        setInfoWindow(null);\n      };\n    },\n    // `infoWindowOptions` and other props are missing from dependencies:\n    //\n    // We don't want to re-create the infowindow instance\n    // when the options change.\n    // Updating the options is handled in the useEffect below.\n    //\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [mapsLibrary]\n  );\n\n  // ## update className and styles for `contentContainer`\n  // stores previously applied style properties, so they can be removed when unset\n  const prevStyleRef = useRef<CSSProperties | null>(null);\n  useEffect(() => {\n    if (!infoWindow || !contentContainerRef.current) return;\n\n    setValueForStyles(\n      contentContainerRef.current,\n      style || null,\n      prevStyleRef.current\n    );\n\n    prevStyleRef.current = style || null;\n\n    if (className !== contentContainerRef.current.className)\n      contentContainerRef.current.className = className || '';\n  }, [infoWindow, className, style]);\n\n  // ## update options\n  useDeepCompareEffect(\n    () => {\n      if (!infoWindow) return;\n\n      const opts: google.maps.InfoWindowOptions = infoWindowOptions;\n      if (!pixelOffset) {\n        opts.pixelOffset = null;\n      } else {\n        opts.pixelOffset = new google.maps.Size(pixelOffset[0], pixelOffset[1]);\n      }\n\n      if (!headerContent) {\n        opts.headerContent = null;\n      } else {\n        opts.headerContent =\n          typeof headerContent === 'string'\n            ? headerContent\n            : headerContainerRef.current;\n      }\n\n      infoWindow.setOptions(infoWindowOptions);\n    },\n\n    // dependency `infoWindow` isn't needed since options are also passed\n    // to the constructor when a new infoWindow is created.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [infoWindowOptions, pixelOffset, headerContent]\n  );\n\n  // ## bind event handlers\n  useMapsEventListener(infoWindow, 'close', onClose);\n  useMapsEventListener(infoWindow, 'closeclick', onCloseClick);\n\n  // ## open info window when content and map are available\n  const map = useMap();\n  useDeepCompareEffect(() => {\n    // `anchor === null` means an anchor is defined but not ready yet.\n    if (!map || !infoWindow || anchor === null) return;\n\n    const isOpenedWithAnchor = !!anchor;\n    const openOptions: google.maps.InfoWindowOpenOptions = {map};\n    if (anchor) {\n      openOptions.anchor = anchor;\n\n      // Only do the infowindow adjusting when dealing with an AdvancedMarker\n      if (isAdvancedMarker(anchor) && anchor.content instanceof Element) {\n        const wrapper = anchor.content as CustomMarkerContent;\n        const wrapperBcr = wrapper?.getBoundingClientRect();\n\n        // This checks whether or not the anchor has custom content with our own\n        // div wrapper. If not, that means we have a regular AdvancedMarker without any children.\n        // In that case we do not want to adjust the infowindow since it is all handled correctly\n        // by the Google Maps API.\n        if (wrapperBcr && wrapper?.isCustomMarker) {\n          // We can safely typecast here since we control that element and we know that\n          // it is a div\n          const anchorDomContent = anchor.content.firstElementChild\n            ?.firstElementChild as Element;\n\n          const contentBcr = anchorDomContent?.getBoundingClientRect();\n\n          // center infowindow above marker\n          const anchorOffsetX =\n            contentBcr.x -\n            wrapperBcr.x +\n            (contentBcr.width - wrapperBcr.width) / 2;\n          const anchorOffsetY = contentBcr.y - wrapperBcr.y;\n\n          const opts: google.maps.InfoWindowOptions = infoWindowOptions;\n\n          opts.pixelOffset = new google.maps.Size(\n            pixelOffset ? pixelOffset[0] + anchorOffsetX : anchorOffsetX,\n            pixelOffset ? pixelOffset[1] + anchorOffsetY : anchorOffsetY\n          );\n\n          infoWindow.setOptions(opts);\n        }\n      }\n    }\n\n    if (shouldFocus !== undefined) {\n      openOptions.shouldFocus = shouldFocus;\n    }\n\n    infoWindow.open(openOptions);\n\n    return () => {\n      // Note: when the infowindow has an anchor, it will automatically show up again when the\n      // anchor was removed from the map before infoWindow.close() is called but the it gets\n      // added back to the map after that.\n      // More information here: https://issuetracker.google.com/issues/343750849\n      if (isOpenedWithAnchor) infoWindow.set('anchor', null);\n\n      infoWindow.close();\n    };\n  }, [infoWindow, anchor, map, shouldFocus, infoWindowOptions, pixelOffset]);\n\n  return (\n    <>\n      {contentContainerRef.current &&\n        createPortal(children, contentContainerRef.current)}\n\n      {headerContainerRef.current !== null &&\n        createPortal(headerContent, headerContainerRef.current)}\n    </>\n  );\n};\n", "import {StaticMapsLocation} from './types';\n\n/**\n * Formats a location into a string representation suitable for Google Static Maps API.\n *\n * @param location - The location to format, can be either a string or an object with lat/lng properties\n * @returns A string representation of the location in the format \"lat,lng\" or the original string\n *\n * @example\n * // Returns \"40.714728,-73.998672\"\n * formatLocation({ lat: 40.714728, lng: -73.998672 })\n *\n * @example\n * // Returns \"New York, NY\"\n * formatLocation(\"New York, NY\")\n */\nexport function formatLocation(location: StaticMapsLocation): string {\n  return typeof location === 'string'\n    ? location\n    : `${location.lat},${location.lng}`;\n}\n\n// Used for removing the leading pipe from the param string\nexport function formatParam(string: string) {\n  return string.slice(1);\n}\n", "import {formatParam} from './helpers';\nimport {StaticMapsMarker} from './types';\n\n/**\n * Assembles marker parameters for static maps.\n *\n * This function takes an array of markers and groups them by their style properties.\n * It then creates a string representation of these markers, including their styles and locations,\n * which can be used as parameters for static map APIs.\n *\n * @param {StaticMapsMarker[]} [markers=[]] - An array of markers to be processed. Each marker can have properties such as color, label, size, scale, icon, anchor, and location.\n * @returns {string[]} An array of strings, each representing a group of markers with their styles and locations.\n *\n * @example\n * const markers = [\n *   { color: 'blue', label: 'A', size: 'mid', location: '40.714728,-73.998672' },\n *   { color: 'blue', label: 'B', size: 'mid', location: '40.714728,-73.998672' },\n *   { icon: 'http://example.com/icon.png', location: { lat: 40.714728, lng: -73.998672 } }\n * ];\n * const params = assembleMarkerParams(markers);\n * // Params will be an array of strings representing the marker parameters\n * Example output: [\n *   \"color:blue|label:A|size:mid|40.714728,-73.998672|40.714728,-73.998672\",\n *   \"color:blue|label:B|size:mid|40.714728,-73.998672|40.714728,-73.998672\",\n *   \"icon:http://example.com/icon.png|40.714728,-73.998672\"\n * ]\n */\nexport function assembleMarkerParams(markers: StaticMapsMarker[] = []) {\n  const markerParams: Array<string> = [];\n\n  // Group markers by style\n  const markersByStyle = markers?.reduce(\n    (styles, marker) => {\n      const {color = 'red', label, size, scale, icon, anchor} = marker;\n\n      // Create a unique style key based on either icon properties or standard marker properties\n      const relevantProps = icon ? [icon, anchor, scale] : [color, label, size];\n      const key = relevantProps.filter(Boolean).join('-');\n\n      styles[key] = styles[key] || [];\n      styles[key].push(marker);\n      return styles;\n    },\n    {} as Record<string, StaticMapsMarker[]>\n  );\n\n  Object.values(markersByStyle ?? {}).forEach(markers => {\n    let markerParam: string = '';\n\n    const {icon} = markers[0];\n\n    // Create marker style from first marker in group since all markers share the same style.\n    Object.entries(markers[0]).forEach(([key, value]) => {\n      // Determine which properties to include based on whether marker uses custom icon\n      const relevantKeys = icon\n        ? ['icon', 'anchor', 'scale']\n        : ['color', 'label', 'size'];\n\n      if (relevantKeys.includes(key)) {\n        markerParam += `|${key}:${value}`;\n      }\n    });\n\n    // Add location coordinates for each marker in the style group\n    // Handles both string locations and lat/lng object formats.\n    for (const marker of markers) {\n      const location =\n        typeof marker.location === 'string'\n          ? marker.location\n          : `${marker.location.lat},${marker.location.lng}`;\n\n      markerParam += `|${location}`;\n    }\n\n    markerParams.push(markerParam);\n  });\n\n  return markerParams.map(formatParam);\n}\n", "import {formatLocation, formatParam} from './helpers';\nimport {StaticMapsPath} from './types';\n\n/**\n * Assembles path parameters for the Static Maps Api from an array of paths.\n *\n * This function groups paths by their style properties (color, weight, fillcolor, geodesic)\n * and then constructs a string of path parameters for each group. Each path parameter string\n * includes the style properties and the coordinates of the paths.\n *\n * @param {Array<StaticMapsPath>} [paths=[]] - An array of paths to be assembled into path parameters.\n * @returns {Array<string>} An array of path parameter strings.\n *\n * @example\n * const paths = [\n *   {\n *     color: 'red',\n *     weight: 5,\n *     coordinates: [\n *       { lat: 40.714728, lng: -73.998672 },\n *       { lat: 40.718217, lng: -73.998284 }\n *     ]\n *   }\n * ];\n *\n * const pathParams = assemblePathParams(paths);\n * Output: [\n *    'color:red|weight:5|40.714728,-73.998672|40.718217,-73.998284'\n *  ]\n */\nexport function assemblePathParams(paths: Array<StaticMapsPath> = []) {\n  const pathParams: Array<string> = [];\n\n  // Group paths by their style properties (color, weight, fillcolor, geodesic)\n  // to combine paths with identical styles into single parameter strings\n  const pathsByStyle = paths?.reduce(\n    (styles, path) => {\n      const {color = 'default', weight, fillcolor, geodesic} = path;\n\n      // Create unique key for this style combination\n      const key = [color, weight, fillcolor, geodesic]\n        .filter(Boolean)\n        .join('-');\n\n      styles[key] = styles[key] || [];\n      styles[key].push(path);\n      return styles;\n    },\n    {} as Record<string, Array<StaticMapsPath>>\n  );\n\n  // Process each group of paths with identical styles\n  Object.values(pathsByStyle ?? {}).forEach(paths => {\n    let pathParam = '';\n\n    // Build style parameter string using properties from first path in group\n    // since all paths in this group share the same style\n    Object.entries(paths[0]).forEach(([key, value]) => {\n      if (['color', 'weight', 'fillcolor', 'geodesic'].includes(key)) {\n        pathParam += `|${key}:${value}`;\n      }\n    });\n\n    // Add location for all marker in style group\n    for (const path of paths) {\n      if (typeof path.coordinates === 'string') {\n        pathParam += `|${decodeURIComponent(path.coordinates)}`;\n      } else {\n        for (const location of path.coordinates) {\n          pathParam += `|${formatLocation(location)}`;\n        }\n      }\n    }\n\n    pathParams.push(pathParam);\n  });\n\n  return pathParams.map(formatParam);\n}\n", "import {formatParam} from './helpers';\n\n/**\n * Converts an array of Google Maps style objects into an array of style strings\n * compatible with the Google Static Maps API.\n *\n * @param styles - An array of Google Maps MapTypeStyle objects that define the styling rules\n * @returns An array of formatted style strings ready to be used with the Static Maps API\n *\n * @example\n * const styles = [{\n *   featureType: \"road\",\n *   elementType: \"geometry\",\n *   stylers: [{color: \"#ff0000\"}, {weight: 1}]\n * }];\n *\n * const styleStrings = assembleMapTypeStyles(styles);\n * // Returns: [\"|feature:road|element:geometry|color:0xff0000|weight:1\"]\n *\n * Each style string follows the format:\n * \"feature:{featureType}|element:{elementType}|{stylerName}:{stylerValue}\"\n *\n * Note: Color values with hexadecimal notation (#) are automatically converted\n * to the required 0x format for the Static Maps API.\n */\nexport function assembleMapTypeStyles(\n  styles: Array<google.maps.MapTypeStyle>\n): string[] {\n  return styles\n    .map((mapTypeStyle: google.maps.MapTypeStyle) => {\n      const {featureType, elementType, stylers = []} = mapTypeStyle;\n\n      let styleString = '';\n\n      if (featureType) {\n        styleString += `|feature:${featureType}`;\n      }\n\n      if (elementType) {\n        styleString += `|element:${elementType}`;\n      }\n\n      for (const styler of stylers) {\n        Object.entries(styler).forEach(([name, value]) => {\n          styleString += `|${name}:${String(value).replace('#', '0x')}`;\n        });\n      }\n\n      return styleString;\n    })\n    .map(formatParam);\n}\n", "import {assembleMarkerParams} from './assemble-marker-params';\nimport {assemblePathParams} from './assemble-path-params';\nimport {formatLocation} from './helpers';\n\nimport {StaticMapsApiOptions} from './types';\nimport {assembleMapTypeStyles} from './assemble-map-type-styles';\n\nconst STATIC_MAPS_BASE = 'https://maps.googleapis.com/maps/api/staticmap';\n\n/**\n * Creates a URL for the Google Static Maps API with the specified parameters.\n *\n * @param {Object} options - The configuration options for the static map\n * @param {string} options.apiKey - Your Google Maps API key (required)\n * @param {number} options.width - The width of the map image in pixels (required)\n * @param {number} options.height - The height of the map image in pixels (required)\n * @param {StaticMapsLocation} [options.center] - The center point of the map (lat/lng or address).\n *  Required if no markers or paths or \"visible locations\" are provided.\n * @param {number} [options.zoom] - The zoom level of the map. Required if no markers or paths or \"visible locations\" are provided.\n * @param {1|2|4} [options.scale] - The resolution of the map (1, 2, or 4)\n * @param {string} [options.format] - The image format (png, png8, png32, gif, jpg, jpg-baseline)\n * @param {string} [options.mapType] - The type of map (roadmap, satellite, terrain, hybrid)\n * @param {string} [options.language] - The language of the map labels\n * @param {string} [options.region] - The region code for the map\n * @param {string} [options.map_id] - The Cloud-based map style ID\n * @param {StaticMapsMarker[]} [options.markers=[]] - Array of markers to display on the map\n * @param {StaticMapsPath[]} [options.paths=[]] - Array of paths to display on the map\n * @param {StaticMapsLocation[]} [options.visible=[]] - Array of locations that should be visible on the map\n * @param {MapTypeStyle[]} [options.style=[]] - Array of style objects to customize the map appearance\n *\n * @returns {string} The complete Google Static Maps API URL\n *\n * @throws {Error} If API key is not provided\n * @throws {Error} If width or height is not provided\n *\n * @example\n * const url = createStaticMapsUrl({\n *   apiKey: 'YOUR_API_KEY',\n *   width: 600,\n *   height: 400,\n *   center: { lat: 40.714728, lng: -73.998672 },\n *   zoom: 12,\n *   markers: [\n *     {\n *       location: { lat: 40.714728, lng: -73.998672 },\n *       color: 'red',\n *       label: 'A'\n *     }\n *   ],\n *   paths: [\n *     {\n *       coordinates: [\n *         { lat: 40.714728, lng: -73.998672 },\n *         { lat: 40.719728, lng: -73.991672 }\n *       ],\n *       color: '0x0000ff',\n *       weight: 5\n *     }\n *   ],\n *   style: [\n *     {\n *       featureType: 'road',\n *       elementType: 'geometry',\n *       stylers: [{color: '#00ff00'}]\n *     }\n *   ]\n * });\n *\n * // Results in URL similar to:\n * // https://maps.googleapis.com/maps/api/staticmap?key=YOUR_API_KEY\n * // &size=600x400\n * // &center=40.714728,-73.998672&zoom=12\n * // &markers=color:red|label:A|40.714728,-73.998672\n * // &path=color:0x0000ff|weight:5|40.714728,-73.998672|40.719728,-73.991672\n * // &style=feature:road|element:geometry|color:0x00ff00\n */\nexport function createStaticMapsUrl({\n  apiKey,\n  width,\n  height,\n  center,\n  zoom,\n  scale,\n  format,\n  mapType,\n  language,\n  region,\n  mapId,\n  markers = [],\n  paths = [],\n  visible = [],\n  style = []\n}: StaticMapsApiOptions) {\n  if (!apiKey) {\n    console.warn('API key is required');\n  }\n  if (!width || !height) {\n    console.warn('Width and height are required');\n  }\n\n  const params: Record<string, string | number | null> = {\n    key: apiKey,\n    size: `${width}x${height}`,\n    ...(center && {center: formatLocation(center)}),\n    ...(zoom && {zoom}),\n    ...(scale && {scale}),\n    ...(format && {format}),\n    ...(mapType && {maptype: mapType}),\n    ...(language && {language}),\n    ...(region && {region}),\n    ...(mapId && {map_id: mapId})\n  };\n\n  const url = new URL(STATIC_MAPS_BASE);\n\n  // Params that don't need special handling\n  Object.entries(params).forEach(([key, value]) => {\n    url.searchParams.append(key, String(value));\n  });\n\n  // Assemble Markers\n  for (const markerParam of assembleMarkerParams(markers)) {\n    url.searchParams.append('markers', markerParam);\n  }\n\n  // Assemble Paths\n  for (const pathParam of assemblePathParams(paths)) {\n    url.searchParams.append('path', pathParam);\n  }\n\n  // Assemble visible locations\n  if (visible.length) {\n    url.searchParams.append(\n      'visible',\n      visible.map(location => formatLocation(location)).join('|')\n    );\n  }\n\n  // Assemble Map Type Styles\n  for (const styleString of assembleMapTypeStyles(style)) {\n    url.searchParams.append('style', styleString);\n  }\n\n  return url.toString();\n}\n", "import React from 'react';\n\nexport {createStaticMapsUrl} from '../libraries/create-static-maps-url';\nexport * from '../libraries/create-static-maps-url/types';\n\n/**\n * Props for the StaticMap component\n */\nexport type StaticMapProps = {\n  url: string;\n  className?: string;\n};\n\nexport const StaticMap = (props: StaticMapProps) => {\n  const {url, className} = props;\n\n  if (!url) throw new Error('URL is required');\n\n  return <img className={className} src={url} width=\"100%\" />;\n};\n", "import {FunctionComponent, useEffect, useMemo} from 'react';\nimport {createPortal} from 'react-dom';\nimport {useMap} from '../hooks/use-map';\n\nimport type {PropsWithChildren} from 'react';\n\ntype MapControlProps = PropsWithChildren<{\n  position: ControlPosition;\n}>;\n\n/**\n * Copy of the `google.maps.ControlPosition` constants.\n * They have to be duplicated here since we can't wait for the maps API to load to be able to use them.\n */\nexport const ControlPosition = {\n  TOP_LEFT: 1,\n  TOP_CENTER: 2,\n  TOP: 2,\n  TOP_RIGHT: 3,\n  LEFT_CENTER: 4,\n  LEFT_TOP: 5,\n  LEFT: 5,\n  LEFT_BOTTOM: 6,\n  RIGHT_TOP: 7,\n  RIGHT: 7,\n  RIGHT_CENTER: 8,\n  RIGHT_BOTTOM: 9,\n  BOTTOM_LEFT: 10,\n  BOTTOM_CENTER: 11,\n  BOTTOM: 11,\n  BOTTOM_RIGHT: 12,\n  CENTER: 13,\n  BLOCK_START_INLINE_START: 14,\n  BLOCK_START_INLINE_CENTER: 15,\n  BLOCK_START_INLINE_END: 16,\n  INLINE_START_BLOCK_CENTER: 17,\n  INLINE_START_BLOCK_START: 18,\n  INLINE_START_BLOCK_END: 19,\n  INLINE_END_BLOCK_START: 20,\n  INLINE_END_BLOCK_CENTER: 21,\n  INLINE_END_BLOCK_END: 22,\n  BLOCK_END_INLINE_START: 23,\n  BLOCK_END_INLINE_CENTER: 24,\n  BLOCK_END_INLINE_END: 25\n} as const;\nexport type ControlPosition =\n  (typeof ControlPosition)[keyof typeof ControlPosition];\n\nexport const MapControl: FunctionComponent<MapControlProps> = ({\n  children,\n  position\n}) => {\n  const controlContainer = useMemo(() => document.createElement('div'), []);\n  const map = useMap();\n\n  useEffect(() => {\n    if (!map) return;\n\n    const controls = map.controls[position];\n\n    controls.push(controlContainer);\n\n    return () => {\n      const controlsArray = controls.getArray();\n      // controlsArray could be undefined if the map is in an undefined state (e.g. invalid API-key, see #276\n      if (!controlsArray) return;\n\n      const index = controlsArray.indexOf(controlContainer);\n      controls.removeAt(index);\n    };\n  }, [controlContainer, map, position]);\n\n  return createPortal(children, controlContainer);\n};\n", "/* eslint-disable complexity */\nimport React, {\n  forwardRef,\n  useCallback,\n  useEffect,\n  useImperativeHandle,\n  useState\n} from 'react';\n\nimport {useMap} from '../hooks/use-map';\n\nimport type {Ref} from 'react';\n\ntype MarkerEventProps = {\n  onClick?: (e: google.maps.MapMouseEvent) => void;\n  onDrag?: (e: google.maps.MapMouseEvent) => void;\n  onDragStart?: (e: google.maps.MapMouseEvent) => void;\n  onDragEnd?: (e: google.maps.MapMouseEvent) => void;\n  onMouseOver?: (e: google.maps.MapMouseEvent) => void;\n  onMouseOut?: (e: google.maps.MapMouseEvent) => void;\n};\n\nexport type MarkerProps = Omit<google.maps.MarkerOptions, 'map'> &\n  MarkerEventProps;\n\nexport type MarkerRef = Ref<google.maps.Marker | null>;\n\nfunction useMarker(props: MarkerProps) {\n  const [marker, setMarker] = useState<google.maps.Marker | null>(null);\n  const map = useMap();\n\n  const {\n    onClick,\n    onDrag,\n    onDragStart,\n    onDragEnd,\n    onMouseOver,\n    onMouseOut,\n    ...markerOptions\n  } = props;\n\n  const {position, draggable} = markerOptions;\n\n  // create marker instance and add to the map once the map is available\n  useEffect(() => {\n    if (!map) {\n      if (map === undefined)\n        console.error('<Marker> has to be inside a Map component.');\n\n      return;\n    }\n\n    const newMarker = new google.maps.Marker(markerOptions);\n    newMarker.setMap(map);\n    setMarker(newMarker);\n\n    return () => {\n      newMarker.setMap(null);\n      setMarker(null);\n    };\n    // We do not want to re-render the whole marker when the options change.\n    // Marker options update is handled in a useEffect below.\n    // Excluding markerOptions from dependency array on purpose here.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [map]);\n\n  // attach and re-attach event-handlers when any of the properties change\n  useEffect(() => {\n    if (!marker) return;\n\n    const m = marker;\n\n    // Add event listeners\n    const gme = google.maps.event;\n\n    if (onClick) gme.addListener(m, 'click', onClick);\n    if (onDrag) gme.addListener(m, 'drag', onDrag);\n    if (onDragStart) gme.addListener(m, 'dragstart', onDragStart);\n    if (onDragEnd) gme.addListener(m, 'dragend', onDragEnd);\n    if (onMouseOver) gme.addListener(m, 'mouseover', onMouseOver);\n    if (onMouseOut) gme.addListener(m, 'mouseout', onMouseOut);\n\n    marker.setDraggable(Boolean(draggable));\n\n    return () => {\n      gme.clearInstanceListeners(m);\n    };\n  }, [\n    marker,\n    draggable,\n    onClick,\n    onDrag,\n    onDragStart,\n    onDragEnd,\n    onMouseOver,\n    onMouseOut\n  ]);\n\n  // update markerOptions (note the dependencies aren't properly checked\n  // here, we just assume that setOptions is smart enough to not waste a\n  // lot of time updating values that didn't change)\n  useEffect(() => {\n    if (!marker) return;\n    if (markerOptions) marker.setOptions(markerOptions);\n  }, [marker, markerOptions]);\n\n  // update position when changed\n  useEffect(() => {\n    // Should not update position when draggable\n    if (draggable || !position || !marker) return;\n\n    marker.setPosition(position);\n  }, [draggable, position, marker]);\n\n  return marker;\n}\n\n/**\n * Component to render a marker on a map\n */\nexport const Marker = forwardRef((props: MarkerProps, ref: MarkerRef) => {\n  const marker = useMarker(props);\n\n  useImperativeHandle(ref, () => marker as google.maps.Marker, [marker]);\n\n  return <></>;\n});\n\nexport function useMarkerRef() {\n  const [marker, setMarker] = useState<google.maps.Marker | null>(null);\n\n  const refCallback = useCallback((m: google.maps.Marker | null) => {\n    setMarker(m);\n  }, []);\n\n  return [refCallback, marker] as const;\n}\n", "import {\n  Children,\n  FunctionComponent,\n  PropsWithChildren,\n  useContext,\n  useEffect,\n  useMemo\n} from 'react';\nimport {AdvancedMarkerContext} from './advanced-marker';\nimport {createPortal} from 'react-dom';\nimport {logErrorOnce} from '../libraries/errors';\n\n/**\n * Props for the Pin component\n */\nexport type PinProps = PropsWithChildren<google.maps.marker.PinElementOptions>;\n\n/**\n * Component to configure the appearance of an AdvancedMarker\n */\nexport const Pin: FunctionComponent<PinProps> = props => {\n  const advancedMarker = useContext(AdvancedMarkerContext)?.marker;\n  const glyphContainer = useMemo(() => document.createElement('div'), []);\n\n  // Create Pin View instance\n  useEffect(() => {\n    if (!advancedMarker) {\n      if (advancedMarker === undefined) {\n        console.error(\n          'The <Pin> component can only be used inside <AdvancedMarker>.'\n        );\n      }\n\n      return;\n    }\n\n    if (props.glyph && props.children) {\n      logErrorOnce(\n        'The <Pin> component only uses children to render the glyph if both the glyph property and children are present.'\n      );\n    }\n\n    if (Children.count(props.children) > 1) {\n      logErrorOnce(\n        'Passing multiple children to the <Pin> component might lead to unexpected results.'\n      );\n    }\n\n    const pinViewOptions: google.maps.marker.PinElementOptions = {\n      ...props\n    };\n\n    const pinElement = new google.maps.marker.PinElement(pinViewOptions);\n\n    // Set glyph to glyph container if children are present (rendered via portal).\n    // If both props.glyph and props.children are present, props.children takes priority.\n    if (props.children) {\n      pinElement.glyph = glyphContainer;\n    }\n\n    // Set content of Advanced Marker View to the Pin View element\n    // Here we are selecting the anchor container.\n    // The hierarchy is as follows:\n    // \"advancedMarker.content\" (from google) -> \"pointer events reset div\" -> \"anchor container\"\n    const markerContent = advancedMarker.content?.firstChild?.firstChild;\n\n    while (markerContent?.firstChild) {\n      markerContent.removeChild(markerContent.firstChild);\n    }\n\n    if (markerContent) {\n      markerContent.appendChild(pinElement.element);\n    }\n  }, [advancedMarker, glyphContainer, props]);\n\n  return createPortal(props.children, glyphContainer);\n};\n", "const mapLinear = (x: number, a1: number, a2: number, b1: number, b2: number) =>\n  b1 + ((x - a1) * (b2 - b1)) / (a2 - a1);\n\nconst getMapMaxTilt = (zoom: number) => {\n  if (zoom <= 10) {\n    return 30;\n  }\n  if (zoom >= 15.5) {\n    return 67.5;\n  }\n\n  // range [10...14]\n  if (zoom <= 14) {\n    return mapLinear(zoom, 10, 14, 30, 45);\n  }\n\n  // range [14...15.5]\n  return mapLinear(zoom, 14, 15.5, 45, 67.5);\n};\n\n/**\n * Function to limit the tilt range of the Google map when updating the view state\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport const limitTiltRange = ({viewState}: any) => {\n  const pitch = viewState.pitch;\n  const gmZoom = viewState.zoom + 1;\n  const maxTilt = getMapMaxTilt(gmZoom);\n\n  return {...viewState, fovy: 25, pitch: Math.min(maxTilt, pitch)};\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,IAAMA,mBAAmB;EAC9BC,YAAY;EACZC,SAAS;EACTC,QAAQ;EACRC,QAAQ;EACRC,cAAc;;ACUhB,IAAMC,oBAAoB;IAQbC,4BAAmB;;;;;;;;;;EAyB9B,aAAaC,KACXC,QACAC,uBAAyD;AAAA,QAAAC;AAEzD,UAAMC,YAAYH,OAAOG,YAAYH,OAAOG,UAAUC,MAAM,GAAG,IAAI,CAAA;AACnE,UAAMC,mBAAmB,KAAKC,gBAAgBN,MAAM;AAEpD,SAAKO,UAAUC,KAAKP,qBAAqB;AAQzC,SAAAC,iBAAIO,OAAOC,WAAMR,SAAAA,iBAAbA,eAAeS,SAAI,QAAnBT,eAAqBU,eAA0B;AAEjD,UAAI,CAAC,KAAKC,qBAAqB;AAC7B,aAAKC,gBAAgBvB,iBAAiBG;MACxC;AACA,WAAKqB,6BAA4B;IACnC,OAAO;AACL,WAAKF,sBAAsBR;AAC3B,WAAKW,kBAAkBhB,MAAM;IAC/B;AAEA,QACE,KAAKa,uBACL,KAAKA,wBAAwBR,kBAC7B;AACAY,cAAQC,KACN,uKAEuC;IAE3C;AAEA,UAAMC,kBAAkB,CAAC,QAAQ,GAAGhB,SAAS;AAC7C,UAAMiB,QAAQC,IACZF,gBAAgBG,IAAIC,UAAQb,OAAOC,KAAKC,cAAcW,IAAI,CAAC,CAAC;EAEhE;;;;EAKQ,OAAOjB,gBAAgBN,QAAiB;AAC9C,WAAO,CACLA,OAAOwB,GACPxB,OAAOyB,KACPzB,OAAO0B,UACP1B,OAAO2B,QACP3B,OAAO4B,oBACP5B,OAAO6B,eAAe,EACtBC,KAAK,GAAG;EACZ;;;;;;;;;;;;EAaQ,OAAOd,kBAAkBhB,QAAiB;AAChD,QAAI,CAACS,OAAOC,OAAQD,QAAOC,SAAS,CAAA;AACpC,QAAI,CAACD,OAAOC,OAAOC,KAAMF,QAAOC,OAAOC,OAAO,CAAA;AAE9C,QAAIF,OAAOC,OAAOC,KAAK,eAAe,GAAG;AACvCM,cAAQc,MACN,+EAA+E;AAGjF;IACF;AAEA,QAAIC,aAAmC;AAEvC,UAAMC,UAAUA,MAAK;AACnB,UAAID,WAAY,QAAOA;AAEvBA,mBAAa,IAAIZ,QAAQ,CAACc,SAASC,WAAU;AAAA,YAAAC;AAC3C,cAAMC,gBAAgBC,SAASC,cAAc,QAAQ;AACrD,cAAMC,YAAY,IAAIC,gBAAe;AAErC,mBAAW,CAAChB,KAAKiB,KAAK,KAAKC,OAAOC,QAAQ5C,MAAM,GAAG;AACjD,gBAAM6C,eAAepB,IAAIqB,QACvB,UACAC,OAAK,MAAMA,EAAE,CAAC,EAAEC,YAAW,CAAE;AAE/BR,oBAAUS,IAAIJ,cAAcK,OAAOR,KAAK,CAAC;QAC3C;AACAF,kBAAUS,IAAI,WAAW,OAAO;AAChCT,kBAAUS,IAAI,YAAY,wBAAwB;AAElDZ,sBAAcc,QAAQ;AACtBd,sBAAce,MAAMvD,oBAAoB,MAAM2C,UAAUa,SAAQ;AAChEhB,sBAAciB,UACXlB,wBAAAE,SAASiB,cAAc,eAAe,MAAuB,OAAA,SAA7DnB,sBACGkB,UAAS;AAEfjB,sBAAcmB,UAAU,MAAK;AAC3B,eAAK1C,gBAAgBvB,iBAAiBI;AACtC,eAAKoB,6BAA4B;AACjCoB,iBAAO,IAAIsB,MAAM,gDAAgD,CAAC;;AAGpEhD,eAAOiD,yBAAyB,MAAK;AACnC,eAAK5C,gBAAgBvB,iBAAiBG;AACtC,eAAKqB,6BAA4B;AACjCmB,kBAAO;;AAGTzB,eAAOkD,iBAAiB,MAAK;AAC3B,eAAK7C,gBAAgBvB,iBAAiBK;AACtC,eAAKmB,6BAA4B;;AAGnC,aAAKD,gBAAgBvB,iBAAiBE;AACtC,aAAKsB,6BAA4B;AAEjCuB,iBAASsB,KAAKC,OAAOxB,aAAa;MACpC,CAAC;AAED,aAAOL;;AAKTtB,WAAOC,KAAKC,gBAAgBkD,iBAC1B7B,QAAO,EAAG8B,KAAK,MAAMrD,OAAOC,KAAKC,cAAckD,WAAW,CAAC;EAC/D;;;;EAKQ,OAAO/C,+BAA4B;AACzC,eAAWiD,MAAM,KAAKzD,WAAW;AAC/ByD,SAAG,KAAKlD,aAAa;IACvB;EACF;;AAzKWhB,oBAIGgB,gBAAkCvB,iBAAiBC;AAJtDM,oBASGe,sBAAmB;AATtBf,oBAcIS,YAAqC,CAAA;;;ACPtD,IAAM0D,2BAA2B;AAE1B,IAAMC,qBACXC,aAAAA,QAAMC,cAA8C,IAAI;AA+D1D,SAASC,kBAAe;AACtB,QAAM,CAACC,cAAcC,eAAe,QAAIC,uBAEtC,CAAA,CAAE;AAEJ,QAAMC,iBAAiBA,CAACC,aAA8BC,KAAK,cAAa;AACtEJ,oBAAgBK,eAASC,SAAA,CAAA,GAASD,WAAS;MAAE,CAACD,EAAE,GAAGD;IAAW,CAAA,CAAE;;AAGlE,QAAMI,oBAAoBA,CAACH,KAAK,cAAa;AAE3CJ,oBAAgBQ,UAAA;AAAA,UAAcC,YAASC,8BAAAF,MAAA,CAApBJ,EAAE,EAAArD,IAAA4D,cAAA,CAAA;AAAA,aAAwBF;KAAU;;AAGzD,QAAMG,oBAAoBA,MAAK;AAC7BZ,oBAAgB,CAAA,CAAE;;AAGpB,SAAO;IAACD;IAAcG;IAAgBK;IAAmBK;;AAC3D;AAMA,SAASC,uBAAuBC,OAAuB;AACrD,QAAM;IACJC;IACAC;IACAC;IACAC;IACAtF,YAAY,CAAA;EAEb,IAAGkF,OADCK,iBAAcT,8BACfI,OAAKM,WAAA;AAET,QAAM,CAACC,QAAQC,SAAS,QAAIrB,uBAC1B1E,oBAAoBgB,aAAa;AAEnC,QAAM,CAACgF,iBAAiBC,gBAAgB,QAAIC,yBAC1C,CACEF,kBACAG,WACE;AACF,WAAOH,iBAAgBG,OAAO1E,IAAI,IAC9BuE,mBAAejB,SAAA,CAAA,GACXiB,kBAAe;MAAE,CAACG,OAAO1E,IAAI,GAAG0E,OAAOvD;KAAM;KAEvD,CAAA,CAAE;AAGJ,QAAMwD,sBAAkBC,sBAAQ,MAAMhG,aAAAA,OAAAA,SAAAA,UAAW2B,KAAK,GAAG,GAAG,CAAC3B,SAAS,CAAC;AACvE,QAAME,uBAAmB8F,sBACvB,MAAMC,KAAKC,UAASxB,SAAA;IAAEW;IAAQC;KAAYC,cAAc,CAAC,GACzD,CAACF,QAAQC,SAASC,cAAc,CAAC;AAGnC,QAAM9E,oBAAkD0F,0BACtD,OAAO/E,SAAgB;AAAA,QAAAgF;AACrB,QAAIT,gBAAgBvE,IAAI,GAAG;AACzB,aAAOuE,gBAAgBvE,IAAI;IAC7B;AAEA,QAAI,GAAAgF,UAAC7F,WAAM,SAAA6F,UAANA,QAAQ5F,SAAI,QAAZ4F,QAAc3F,gBAAe;AAChC,YAAM,IAAI6C,MACR,gGAC0C;IAE9C;AAEA,UAAM+C,MAAM,MAAM/F,OAAOC,OAAOC,KAAKC,cAAcW,IAAI;AACvDwE,qBAAiB;MAACxE;MAAMmB,OAAO8D;IAAI,CAAA;AAEnC,WAAOA;EACT,GACA,CAACV,eAAe,CAAC;AAGnBW;IACE,MAAK;AACH,OAAC,YAAW;AACV,YAAI;AACF,gBAAMzG,SAAM6E,SAAA;YAAepD,KAAK+D;UAAM,GAAKE,cAAc;AACzD,cAAID,QAASzF,QAAOwB,IAAIiE;AACxB,eAAIS,mBAAAA,OAAAA,SAAAA,gBAAiBQ,UAAS,EAAG1G,QAAOG,YAAY+F;AAEpD,cACElG,OAAO2G,YAAYC,UACnB5G,OAAO2G,UAAU,KACjB3G,OAAO2G,UAAU,IAEjB,QAAO3G,OAAO2G;AAEhB,cAAI3G,OAAO6B,oBAAoB+E,OAC7B5G,QAAO6B,kBAAkBoC;mBAClBjE,OAAO6B,oBAAoB,GAAI,QAAO7B,OAAO6B;AAEtD,gBAAM/B,oBAAoBC,KAAKC,QAAQ4F,CAAAA,YAAUC,UAAUD,OAAM,CAAC;AAElE,qBAAWrE,QAAQ,CAAC,QAAQ,QAAQ,GAAGpB,SAAS,GAAG;AACjD,kBAAMS,cAAcW,IAAI;UAC1B;AAEA,cAAI+D,QAAQ;AACVA,mBAAM;UACR;iBACOvD,OAAO;AACd,cAAIwD,SAAS;AACXA,oBAAQxD,KAAK;UACf,OAAO;AACLd,oBAAQc,MACN,+DACAA,KAAK;UAET;QACF;MACF,GAAC;;;IAGH,CAACyD,QAAQU,iBAAiB7F,gBAAgB;EAAC;AAG7C,SAAO;IACLuF;IACAE;IACAlF;;AAEJ;AAKaiG,IAAAA,cAAmDxB,WAAQ;AACtE,QAAM;IAACyB;EAAyB,IAAGzB,OAAf0B,cAAW9B,8BAAII,OAAK2B,YAAA;AACxC,QAAM;IAAC1C;IAAcG;IAAgBK;IAAmBK;MACtDd,gBAAe;AAEjB,QAAM;IAACuB;IAAQE;IAAiBlF;EAAc,IAC5CwE,uBAAuB2B,WAAW;AAEpC,QAAME,mBAAwCd,sBAC5C,OAAO;IACL7B;IACAG;IACAK;IACAK;IACAS;IACAE;IACAlF;EACD,IACD,CACE0D,cACAG,gBACAK,mBACAK,mBACAS,QACAE,iBACAlF,aAAa,CACd;AAGH,SACEuD,aAAAA,QAAA5B,cAAC2B,mBAAmBgD,UAAQ;IAACxE,OAAOuE;EAAa,GAC9CH,QAC0B;AAEjC;AC5NgB,SAAAK,aACd7F,KACA+D,OAAoB;AAOpB,aAAW+B,YAAYC,gBAAgB;AAGrC,UAAMC,UAAUjC,MAAM+B,QAAQ;AAC9B,UAAMG,YAAYC,oBAAoBJ,QAAQ;AAG9CX,gCAAU,MAAK;AACb,UAAI,CAACnF,IAAK;AACV,UAAI,CAACgG,QAAS;AAEd,YAAMG,WAAW/G,OAAOC,KAAK+G,MAAMC,YACjCrG,KACAiG,WACCK,QAA+D;AAC9DN,gBAAQO,eAAeN,WAAWjG,KAAKsG,EAAE,CAAC;MAC5C,CAAC;AAGH,aAAO,MAAMH,SAASK,OAAM;OAC3B,CAACxG,KAAKiG,WAAWD,OAAO,CAAC;EAC9B;AACF;AAQA,SAASO,eACPE,MACAzG,KACA0G,UAAiE;AAEjE,QAAMJ,KAAe;IACnBG;IACAzG;IACA2G,QAAQ,CAAA;IACRC,WAAW;IACXC,MAAMA,MAAK;IAAA;;AAGb,MAAIC,iBAAiBC,SAASN,IAAI,GAAG;AACnC,UAAMO,WAAWV;AAEjB,UAAMW,SAASjH,IAAIkH,UAAS;AAC5B,UAAMC,OAAOnH,IAAIoH,QAAO;AACxB,UAAMC,UAAUrH,IAAIsH,WAAU,KAAM;AACpC,UAAMC,OAAOvH,IAAIwH,QAAO,KAAM;AAC9B,UAAMC,SAASzH,IAAI0H,UAAS;AAE5B,QAAI,CAACT,UAAU,CAACQ,UAAU,CAACE,OAAOC,SAAST,IAAI,GAAG;AAChDxH,cAAQC,KACN,2LAE4E;IAEhF;AAEAoH,aAASL,SAAS;MAChBM,SAAQA,UAAM,OAAA,SAANA,OAAQY,OAAM,MAAM;QAACC,KAAK;QAAGC,KAAK;;MAC1CZ,MAAOA,QAAmB;MAC1BE;MACAE;MACAE,SAAQA,UAAM,OAAA,SAANA,OAAQI,OAAM,MAAM;QAC1BG,OAAO;QACPC,MAAM;QACNC,OAAO;QACPC,MAAM;MACP;;AAGH,WAAOnB;aACEoB,gBAAgBrB,SAASN,IAAI,GAAG;AAAA,QAAA4B;AACzC,QAAI,CAAC3B,SACH,OAAM,IAAIvE,MAAM,oDAAoD;AACtE,UAAMmG,aAAahC;AAEnBgC,eAAWC,WAAW7B,SAAS6B;AAC/BD,eAAW1B,YAAY;AACvB0B,eAAWzB,OAAO,MAAMH,SAASG,KAAI;AAErCyB,eAAW3B,SAAS;MAClB6B,UAAQH,mBAAA3B,SAAS8B,WAATH,OAAAA,SAAAA,iBAAiBR,OAAM,MAAM;MACrCY,SAAU/B,SAAwC+B;;AAGpD,WAAOH;EACT;AAEA,SAAOhC;AACT;AAMA,IAAMJ,sBAAyE;EAC7EwC,iBAAiB;EACjBC,iBAAiB;EACjBC,SAAS;EACTC,eAAe;EACfC,YAAY;EACZC,QAAQ;EACRC,WAAW;EACXC,aAAa;EACbC,kBAAkB;EAClBC,QAAQ;EACRC,kCAAkC;EAClCC,0BAA0B;EAC1BC,oBAAoB;EACpBC,aAAa;EACbC,YAAY;EACZC,aAAa;EACbC,qBAAqB;EACrBC,wBAAwB;EACxBC,eAAe;EACfC,eAAe;EACfC,eAAe;;;;EAKfC,iBAAiB;;AAGnB,IAAMjD,mBAAmB,CACvB,kBACA,kBACA,mBACA,gBACA,cAAc;AAGhB,IAAMsB,kBAAkB,CACtB,SACA,eACA,YACA,aACA,YACA,WAAW;AAIb,IAAMrC,iBAAiB1E,OAAO2I,KAAK9D,mBAAmB;AChMtC,SAAA+D,qBACdC,QACAC,MAAoB;AAEpB,QAAMC,UAAMC,qBAAmC/E,MAAS;AAExD,MAAI,CAAC8E,IAAIE,WAAW,KAACC,uBAAAA,SAAYJ,MAAMC,IAAIE,OAAO,GAAG;AACnDF,QAAIE,UAAUH;EAChB;AAGAhF,8BAAU+E,QAAQE,IAAIE,OAAO;AAC/B;ACZA,IAAME,gBAAmD,oBAAIC,IAAI,CAC/D,mBACA,kBACA,eACA,oBACA,0BACA,aACA,mBACA,kBACA,qBACA,4BACA,mBACA,6BACA,2BACA,qBACA,kBACA,yBACA,aACA,WACA,WACA,WACA,cACA,qBACA,eACA,iBACA,wBACA,gBACA,uBACA,eACA,cACA,qBACA,4BACA,UACA,0BACA,eACA,oBAAoB,CACrB;AASe,SAAAC,cAAc1K,KAA6B2K,UAAkB;AAQ3E,QAAMC,aAAqC,CAAA;AAC3C,QAAMZ,OAAO3I,OAAO2I,KAAKW,QAAQ;AACjC,aAAWxK,OAAO6J,MAAM;AACtB,QAAI,CAACQ,cAAcK,IAAI1K,GAAG,EAAG;AAE7ByK,eAAWzK,GAAG,IAAIwK,SAASxK,GAAG;EAChC;AAMA8J,uBAAqB,MAAK;AACxB,QAAI,CAACjK,IAAK;AAEVA,QAAI8K,WAAWF,UAAU;EAC3B,GAAG,CAACA,UAAU,CAAC;AAEjB;SCtEgBG,sBAAmB;AAAA,MAAAC;AACjC,WAAOA,kBAAAC,yBAAWrI,kBAAkB,MAA7BoI,OAAAA,SAAAA,YAAgC1G,WAAUrG,iBAAiBC;AACpE;ACegB,SAAAgN,sBACdlL,KACA+D,OAAwB;AAExB,QAAM;IAACoH;IAAUC;EAAU,IAAGrH;AAC9B,QAAMsH,qBAAqB,CAAC,CAACF;AAE7BG,oCAAgB,MAAK;AACnB,QAAI,CAACtL,OAAO,CAACoL,UAAW;AAExB,UAAM;MACJG;MACAC;MACAC,SAASpE;MACTqE,OAAOnE;MACPJ;IACD,IAAGiE;AAEJpL,QAAI2L,WAAW;MACb1E,QAAQ;QAACa,KAAKyD;QAAUxD,KAAKyD;;MAC7BnE;MACAE;MACAJ,MAAMA,OAAO;IACd,CAAA;EACH,GAAG,CAACnH,KAAKoL,SAAS,CAAC;AAEnB,SAAOC;AACT;AChDM,SAAUO,gBACdC,KAAY;AAEZ,MAAI,CAACA,OAAO,OAAOA,QAAQ,SAAU,QAAO;AAC5C,MAAI,EAAE,SAASA,OAAO,SAASA,KAAM,QAAO;AAE5C,SAAOlE,OAAOC,SAASiE,IAAI/D,GAAG,KAAKH,OAAOC,SAASiE,IAAI9D,GAAG;AAC5D;AAEgB,SAAA+D,aACdC,GACAC,GAAoE;AAEpE,MAAI,CAACD,KAAK,CAACC,EAAG,QAAO;AACrB,QAAMC,IAAIC,gBAAgBH,CAAC;AAC3B,QAAMI,IAAID,gBAAgBF,CAAC;AAC3B,MAAIC,EAAEnE,QAAQqE,EAAErE,OAAOmE,EAAElE,QAAQoE,EAAEpE,IAAK,QAAO;AAC/C,SAAO;AACT;AAEM,SAAUmE,gBACdL,KAAmD;AAEnD,MAAID,gBAAgBC,GAAG,EAAG,QAAOA;AAEjC,SAAOA,IAAIhE,OAAM;AACnB;SCrBgBuE,mBACdpM,KACAqM,gBACA1B,UAAkB;AAElB,QAAM1D,SAAS0D,SAAS1D,SAASiF,gBAAgBvB,SAAS1D,MAAM,IAAI;AAEpE,MAAIa,MAAqB;AACzB,MAAIC,MAAqB;AAEzB,MAAId,UAAUU,OAAOC,SAASX,OAAOa,GAAG,KAAKH,OAAOC,SAASX,OAAOc,GAAG,GAAG;AACxED,UAAMb,OAAOa;AACbC,UAAMd,OAAOc;EACf;AAEA,QAAMZ,OAAsBQ,OAAOC,SAAS+C,SAASxD,IAAI,IACpDwD,SAASxD,OACV;AACJ,QAAME,UAAyBM,OAAOC,SAAS+C,SAAStD,OAAO,IAC1DsD,SAAStD,UACV;AACJ,QAAME,OAAsBI,OAAOC,SAAS+C,SAASpD,IAAI,IACpDoD,SAASpD,OACV;AAOJ+D,oCAAgB,MAAK;AACnB,QAAI,CAACtL,IAAK;AAEV,UAAMsM,aAAwC,CAAA;AAC9C,QAAIC,cAAc;AAElB,QACEzE,QAAQ,QACRC,QAAQ,SACPsE,eAAe/B,QAAQrD,OAAOa,QAAQA,OACrCuE,eAAe/B,QAAQrD,OAAOc,QAAQA,MACxC;AACAuE,iBAAWrF,SAAS;QAACa;QAAKC;;AAC1BwE,oBAAc;IAChB;AAEA,QAAIpF,SAAS,QAAQkF,eAAe/B,QAAQnD,SAASA,MAAM;AACzDmF,iBAAWnF,OAAOA;AAClBoF,oBAAc;IAChB;AAEA,QAAIlF,YAAY,QAAQgF,eAAe/B,QAAQjD,YAAYA,SAAS;AAClEiF,iBAAWjF,UAAUA;AACrBkF,oBAAc;IAChB;AAEA,QAAIhF,SAAS,QAAQ8E,eAAe/B,QAAQ/C,SAASA,MAAM;AACzD+E,iBAAW/E,OAAOA;AAClBgF,oBAAc;IAChB;AAEA,QAAIA,aAAa;AACfvM,UAAI2L,WAAWW,UAAU;IAC3B;EACF,CAAC;AACH;ACpEO,IAAME,qBAAwCA,MAAK;AACxD,QAAMC,QAAuB;IAC3BC,UAAU;IACVC,KAAK;IACLC,MAAM;IACNC,QAAQ;IACRC,OAAO;IACPC,QAAQ;IACRC,SAAS;IACTC,UAAU;IACVC,WAAW;IACXC,gBAAgB;IAChBC,UAAU;IACVC,OAAO;IACPC,YAAY;IACZC,SAAS;;AAGX,SACE1K,aAAAA,QAAA5B,cAAA,OAAA;IAAKwL;KACH5J,aAAAA,QAAA5B,cAAA,MAAA,MAAI,oBAAsB,GAC1B4B,aAAAA,QAAA5B,cAAA,KAAA,MACE,6GACkC4B,aAAAA,QAAA5B,cAAA,QAAA,MAAM,oBAAwB,GAE/D,+EAAA,CACA;AAET;SC5BgBuM,iBAAc;AAC5B,QAAM,CAACC,IAAIC,KAAK,QAAIxK,uBAAmB,IAAI;AAC3C,QAAMkH,UAAMpF,0BAAa5D,WAAasM,MAAMtM,KAAK,GAAG,CAACsM,KAAK,CAAC;AAE3D,SAAO,CAACD,IAAIrD,GAAa;AAC3B;SCFgBuD,iBAAc;AAC5B,QAAMrJ,SAASyG,oBAAmB;AAElC,SAAOzG,WAAWrG,iBAAiBG;AACrC;SCPgBwP,iBAAc;AAC5B,QAAM,CAAA,EAAGC,WAAW,QAAInJ,yBAAWoJ,OAAKA,IAAI,GAAG,CAAC;AAEhD,SAAOD;AACT;ACMA,SAASE,mBAAmB/N,KAAsBoK,KAAmB;AACnE,QAAMnD,SAASjH,IAAIkH,UAAS;AAC5B,QAAMC,OAAOnH,IAAIoH,QAAO;AACxB,QAAMC,UAAUrH,IAAIsH,WAAU,KAAM;AACpC,QAAMC,OAAOvH,IAAIwH,QAAO,KAAM;AAC9B,QAAMC,SAASzH,IAAI0H,UAAS;AAE5B,MAAI,CAACT,UAAU,CAACQ,UAAU,CAACE,OAAOC,SAAST,IAAI,GAAG;AAChDxH,YAAQC,KACN,qMAE4E;EAEhF;AAGAyB,SAAO2M,OAAO5D,IAAIE,SAAS;IACzBrD,SAAQA,UAAM,OAAA,SAANA,OAAQY,OAAM,MAAM;MAACC,KAAK;MAAGC,KAAK;;IAC1CZ,MAAOA,QAAmB;IAC1BE;IACAE;EACD,CAAA;AACH;AAOM,SAAU0G,yBACdjO,KAA2B;AAE3B,QAAM6N,cAAcD,eAAc;AAClC,QAAMxD,UAAMC,qBAAoB;IAC9BpD,QAAQ;MAACa,KAAK;MAAGC,KAAK;;IACtBV,SAAS;IACTE,MAAM;IACNJ,MAAM;EACP,CAAA;AAMDhC,8BAAU,MAAK;AACb,QAAI,CAACnF,IAAK;AAEV,UAAMmG,WAAW/G,OAAOC,KAAK+G,MAAMC,YACjCrG,KACA,kBACA,MAAK;AACH+N,yBAAmB/N,KAAKoK,GAAG;AAM3ByD,kBAAW;IACb,CAAC;AAGH,WAAO,MAAM1H,SAASK,OAAM;EAC9B,GAAG,CAACxG,KAAK6N,WAAW,CAAC;AAErB,SAAOzD;AACT;;;ACpDA,IAAM8D,iBAAN,MAAoB;EAGlB,OAAOrD,IAAI1K,KAAW;AACpB,WAAO,KAAKmB,QAAQnB,GAAG,KAAK,KAAKmB,QAAQnB,GAAG,EAAEiF,SAAS;EACzD;EAEA,OAAO+I,IAAIhO,KAAW;AACpB,QAAI,CAAC,KAAKmB,QAAQnB,GAAG,EAAG,QAAO;AAE/B,WAAO,KAAKmB,QAAQnB,GAAG,EAAEgO,IAAG,KAAM;EACpC;EAEA,OAAOjP,KAAKiB,KAAaiB,OAAsB;AAC7C,QAAI,CAAC,KAAKE,QAAQnB,GAAG,EAAG,MAAKmB,QAAQnB,GAAG,IAAI,CAAA;AAE5C,SAAKmB,QAAQnB,GAAG,EAAEjB,KAAKkC,KAAK;EAC9B;;AAjBI8M,eACG5M,UAA8C,CAAA;AA0BvC,SAAA8M,eACdrK,OACAsK,SAAgC;AAMhC,QAAMC,cAAcX,eAAc;AAClC,QAAM,CAAC3N,KAAKuO,MAAM,QAAIrL,uBAAiC,IAAI;AAC3D,QAAM,CAACsL,WAAWC,YAAY,IAAIjB,eAAc;AAEhD,QAAMnB,iBAAiB4B,yBAAyBjO,GAAG;AAEnD,QAAM;IACJqD;IACAqL;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EAEa,IACXlL,OADC6G,aAAUjH,8BACXI,OAAKM,WAAA;AAET,QAAM6K,UAAUnL,MAAMoD,SAAS7B,UAAavB,MAAM6K,gBAAgBtJ;AAClE,QAAM6J,YACJpL,MAAMkD,WAAW3B,UAAavB,MAAM4K,kBAAkBrJ;AAExD,MAAI,CAACoJ,kBAAkB,CAACQ,WAAW,CAACC,YAAY;AAC9CxP,YAAQC,KACN,+RAIwF;EAE5F;AAGA,MAAI,CAACgL,WAAW3D,UAAU0H,cAAe/D,YAAW3D,SAAS0H;AAC7D,MAAI,CAAC/D,WAAWzD,QAAQQ,OAAOC,SAASgH,WAAW,EACjDhE,YAAWzD,OAAOyH;AACpB,MAAI,CAAChE,WAAWvD,WAAWM,OAAOC,SAASiH,cAAc,EACvDjE,YAAWvD,UAAUwH;AACvB,MAAI,CAACjE,WAAWrD,QAAQI,OAAOC,SAASkH,WAAW,EACjDlE,YAAWrD,OAAOuH;AAEpB,aAAW3O,OAAOkB,OAAO2I,KAAKY,UAAU,EACtC,KAAIA,WAAWzK,GAAG,MAAMmF,OAAW,QAAOsF,WAAWzK,GAAG;AAE1D,QAAMiP,uBAAmB/E,qBAGtB/E,MAAS;AAGZH;IACE,MAAK;AACH,UAAI,CAACqJ,aAAa,CAACF,YAAa;AAEhC,YAAM;QAACnL;QAAgBK;MAAkB,IAAG6K;AAG5C,YAAM;QAACgB;MAAM,IAAGtL;AAChB,YAAMuL,WAAW,GAAGD,SAAS,SAAS,IAAIL,iBAAiB,SAAS,IAAIC,eAAe,OAAO;AAE9F,UAAIM;AACJ,UAAIvP;AAEJ,UAAI+O,aAAab,eAAerD,IAAIyE,QAAQ,GAAG;AAC7CtP,QAAAA,OAAMkO,eAAeC,IAAImB,QAAQ;AACjCC,iBAASvP,KAAIwP,OAAM;AAEnBhB,kBAAUiB,YAAYF,MAAM;AAC5BvP,QAAAA,KAAI8K,WAAWF,UAAU;AAIzB8E,mBAAW,MAAM1P,KAAI2P,UAAU3P,KAAIkH,UAAS,CAAG,GAAG,CAAC;MACrD,OAAO;AACLqI,iBAASvO,SAASC,cAAc,KAAK;AACrCsO,eAAO9C,MAAMmD,SAAS;AACtBpB,kBAAUiB,YAAYF,MAAM;AAE5BvP,QAAAA,OAAM,IAAIZ,OAAOC,KAAKwQ,IAAIN,QAAMhM,SAAA,CAAA,GAC3BqH,YACCoE,gBACA;UAACA;QAA0D,IAC3D,CAAA,GACAC,cACA;UAACA;QAAoD,IACrD,CAAA,CAAE,CACP;MACH;AAEAV,aAAOvO,IAAG;AACVmD,qBAAenD,MAAKqD,EAAE;AAEtB,UAAIqL,eAAe;AACjB,cAAM;UAACnB;QAAsB,IAAGmB,eAAboB,YAASnM,8BAAI+K,eAAahJ,UAAA;AAC7C1F,QAAAA,KAAI+P,UAAUD,WAAWvC,OAAO;MAClC,WAGS,CAAC2B,WAAW,CAACC,WAAW;AAC/BnP,QAAAA,KAAI+P,UAAU;UAAC9H,MAAM;UAAKE,MAAM;UAAMD,OAAO;UAAKF,OAAO;QAAG,CAAA;MAC9D;AAGA,UAAIoH,iBAAiB9E,SAAS;AAC5B,cAAM;UAAC+E,OAAOW;UAAYC,aAAaC;YACrCd,iBAAiB9E;AACnB,YAAI0F,eAAeX,OAAO;AACxBrP,UAAAA,KAAI8K,WAAWoF,gBAAgB;QACjC;MACF;AAEA,aAAO,MAAK;AACVd,yBAAiB9E,UAAU;UACzB+E;;UAEAY,aAAa5D,eAAe/B;;AAI9BiF,eAAO/I,OAAM;AAEb,YAAIuI,WAAW;AAEbb,yBAAehP,KAAKoQ,UAAUtP,IAAG;QACnC,OAAO;AAELZ,iBAAOC,KAAK+G,MAAM+J,uBAAuBnQ,IAAG;QAC9C;AAEAuO,eAAO,IAAI;AACX/K,0BAAkBH,EAAE;;;;;;;;IASxB;MACEmL;MACAF;MACAjL;;;MAIAU,MAAMsL;MACNtL,MAAMiL;MACNjL,MAAMkL;IAAW;EAClB;AAGH,SAAO,CAACjP,KAAKyO,cAAcpC,cAAc;AAC3C;AC1LO,IAAM+D,oBACXvN,aAAAA,QAAMC,cAA6C,IAAI;AAmBlD,IAAMuN,cAAc;EACzBC,MAAM;EACNC,OAAO;EACPC,eAAe;;AAIV,IAAMC,gBAAgB;EAC3BC,QAAQ;EACRC,QAAQ;EACRC,eAAe;;AA4DJf,IAAAA,MAAoC9L,WAAmB;AAClE,QAAM;IAACyB;IAAUnC;IAAIwN;IAAWpE;EAAK,IAAI1I;AACzC,QAAMsK,cAAUpD,yBAAWrI,kBAAkB;AAC7C,QAAMpD,gBAAgBuL,oBAAmB;AAEzC,MAAI,CAACsD,SAAS;AACZ,UAAM,IAAIlM,MACR,2DAA2D;EAE/D;AAEA,QAAM,CAACnC,KAAK8Q,QAAQzE,cAAc,IAAI+B,eAAerK,OAAOsK,OAAO;AAEnEjC,qBAAmBpM,KAAKqM,gBAAgBtI,KAAK;AAC7C8B,eAAa7F,KAAK+D,KAAK;AACvB2G,gBAAc1K,KAAK+D,KAAK;AAExB,QAAMsH,qBAAqBH,sBAAsBlL,KAAK+D,KAAK;AAC3D,QAAMgN,yBAAyB,CAAC,CAAChN,MAAMiN;AAGvC7L,8BAAU,MAAK;AACb,QAAI,CAACnF,IAAK;AAKV,QAAIqL,oBAAoB;AACtBrL,UAAI8K,WAAW;QAACmG,kBAAkB;MAAK,CAAA;IACzC;AAGA,QAAI5F,sBAAsB0F,wBAAwB;AAChD/Q,UAAI8K,WAAW;QACboG,iBAAiB;QACjBC,mBAAmB;MACpB,CAAA;IACH;AAEA,WAAO,MAAK;AACVnR,UAAI8K,WAAW;QACboG,iBAAiBnN,MAAMmN;QACvBC,mBAAmBpN,MAAMoN;MAC1B,CAAA;;EAEL,GAAG,CACDnR,KACAqL,oBACA0F,wBACAhN,MAAMmN,iBACNnN,MAAMoN,iBAAiB,CACxB;AAGD,QAAMlK,SAASlD,MAAMkD,SAASiF,gBAAgBnI,MAAMkD,MAAM,IAAI;AAC9D,MAAIa,MAAqB;AACzB,MAAIC,MAAqB;AACzB,MAAId,UAAUU,OAAOC,SAASX,OAAOa,GAAG,KAAKH,OAAOC,SAASX,OAAOc,GAAG,GAAG;AACxED,UAAMb,OAAOa;AACbC,UAAMd,OAAOc;EACf;AAEA,QAAMqJ,oBAA2CvM,sBAAQ,MAAK;AAAA,QAAAwM,MAAAC,MAAAC,aAAAC,gBAAAC;AAC5D,WAAO;MACLxK,QAAQ;QAACa,MAAGuJ,OAAEvJ,QAAGuJ,OAAAA,OAAI;QAAGtJ,MAAGuJ,OAAEvJ,QAAG,OAAAuJ,OAAI;;MACpCnK,OAAIoK,cAAExN,MAAMoD,SAAI,OAAAoK,cAAI;MACpBlK,UAAOmK,iBAAEzN,MAAMsD,YAAO,OAAAmK,iBAAI;MAC1BjK,OAAIkK,cAAE1N,MAAMwD,SAAI,OAAAkK,cAAI;;EAExB,GAAG,CAAC3J,KAAKC,KAAKhE,MAAMoD,MAAMpD,MAAMsD,SAAStD,MAAMwD,IAAI,CAAC;AAGpD+D,oCAAgB,MAAK;AACnB,QAAI,CAACtL,OAAO,CAAC+Q,uBAAwB;AAErC/Q,QAAI2L,WAAWyF,aAAa;AAC5B,UAAMjL,WAAWnG,IAAIqG,YAAY,kBAAkB,MAAK;AACtDrG,UAAI2L,WAAWyF,aAAa;IAC9B,CAAC;AAED,WAAO,MAAMjL,SAASK,OAAM;KAC3B,CAACxG,KAAK+Q,wBAAwBK,aAAa,CAAC;AAE/C,QAAMM,oBAA+B7M,sBACnC,MAAAtB,SAAA;IACEoO,OAAO;IACP/B,QAAQ;IACRlD,UAAU;;IAEVK,QAAQ1B,qBAAqB,KAAK;KAE/BoB,KAAK,GAEV,CAACA,OAAOpB,kBAAkB,CAAC;AAG7B,QAAM1F,mBAAuCd,sBAAQ,OAAO;IAAC7E;MAAO,CAACA,GAAG,CAAC;AAEzE,MAAIR,kBAAkBvB,iBAAiBK,cAAc;AACnD,WACEuE,aAAAA,QAAA5B,cAAA,OAAA;MACEwL,OAAKlJ,SAAA;QAAGmJ,UAAU;MAAU,GAAMmE,YAAY,CAAA,IAAKa,aAAa;MAChEb;IAAqB,GACrBhO,aAAAA,QAAA5B,cAACuL,oBAAkB,IACrB,CAAK;EAET;AAEA,SACE3J,aAAAA,QAAA5B,cAAA,OAAAsC,SAAA;IACE6G,KAAK0G;IACL,eAAa;IACbrE,OAAOoE,YAAYvL,SAAYoM;IAC/Bb;EAAqB,GAChBxN,KAAK;IAACA;EAAG,IAAG,CAAA,CAAE,GAClBrD,MACC6C,aAAAA,QAAA5B,cAACmP,kBAAkBxK,UAAQ;IAACxE,OAAOuE;EAAa,GAC7CH,QACyB,IAC1B,IACD;AAET;AAKCqK,IAAY+B,kBAAkB;ACtP/B,IAAMC,gBAAgB,oBAAIpH,IAAG;AAEb,SAAAqH,gBAAgBC,MAAsC;AACpE,QAAM5R,MAAM2E,KAAKC,UAAUgN,IAAI;AAE/B,MAAI,CAACF,cAAchH,IAAI1K,GAAG,GAAG;AAC3B0R,kBAAcG,IAAI7R,GAAG;AAErBR,YAAQc,MAAM,GAAGsR,IAAI;EACvB;AACF;ICCaE,SAASA,CAAC5O,KAAoB,SAAgC;AACzE,QAAM6O,UAAMjH,yBAAWrI,kBAAkB;AACzC,QAAM;IAAC5C;EAAG,QAAIiL,yBAAWmF,iBAAiB,KAAK,CAAA;AAE/C,MAAI8B,QAAQ,MAAM;AAChBJ,iBACE,8LAGkB;AAGpB,WAAO;EACT;AAEA,QAAM;IAAC9O;EAAa,IAAGkP;AAGvB,MAAI7O,OAAO,KAAM,QAAOL,aAAaK,EAAE,KAAK;AAG5C,MAAIrD,IAAK,QAAOA;AAGhB,SAAOgD,aAAa,SAAS,KAAK;AACpC;ACXM,SAAUmP,eAAelS,MAAY;AACzC,QAAMqO,cAAcX,eAAc;AAClC,QAAMuE,UAAMjH,yBAAWrI,kBAAkB;AAEzCuC,8BAAU,MAAK;AACb,QAAI,CAACmJ,eAAe,CAAC4D,IAAK;AAK1B,SAAKA,IAAI5S,cAAcW,IAAI;KAC1B,CAACqO,aAAa4D,KAAKjS,IAAI,CAAC;AAE3B,UAAOiS,OAAG,OAAA,SAAHA,IAAK1N,gBAAgBvE,IAAI,MAAK;AACvC;SChCgBmS,qBACdC,QACApS,MACAqS,UAAmB;AAEnBnN,8BAAU,MAAK;AACb,QAAI,CAACkN,UAAU,CAACpS,QAAQ,CAACqS,SAAU;AAEnC,UAAMnM,WAAW/G,OAAOC,KAAK+G,MAAMC,YAAYgM,QAAQpS,MAAMqS,QAAQ;AAErE,WAAO,MAAMnM,SAASK,OAAM;KAC3B,CAAC6L,QAAQpS,MAAMqS,QAAQ,CAAC;AAC7B;SCRgBC,eACdC,QACAC,MACArR,OAAW;AAEX+D,8BAAU,MAAK;AACb,QAAI,CAACqN,OAAQ;AAEbA,WAAOC,IAAI,IAAIrR;KACd,CAACoR,QAAQC,MAAMrR,KAAK,CAAC;AAC1B;SCdgBsR,oBACdL,QACApS,MACAqS,UAAmB;AAEnBnN,8BAAU,MAAK;AACb,QAAI,CAACkN,UAAU,CAACpS,QAAQ,CAACqS,SAAU;AAEnCD,WAAOM,iBAAiB1S,MAAMqS,QAAQ;AAEtC,WAAO,MAAMD,OAAOO,oBAAoB3S,MAAMqS,QAAQ;KACrD,CAACD,QAAQpS,MAAMqS,QAAQ,CAAC;AAC7B;ACMM,SAAUO,iBACdC,QAAqE;AAErE,SACGA,OAAoDC,YAAYzN;AAErE;AAEA,SAAS0N,cAAcC,MAAU;AAC/B,SAAOA,KAAKC,aAAaC,KAAKC;AAChC;AAMO,IAAMC,oBAAoB;EAC/BC,UAAU;EACVC,6BAA6B;EAC7BC,mCAAmC;;AAK9B,IAAMC,wBACX5Q,aAAAA,QAAMC,cAAiD,IAAI;AAGtD,IAAM4Q,4BAA4B;EACvCC,UAAU,CAAC,MAAM,IAAI;EACrBC,YAAY,CAAC,OAAO,IAAI;EACxBC,KAAK,CAAC,OAAO,IAAI;EACjBC,WAAW,CAAC,QAAQ,IAAI;EACxBC,aAAa,CAAC,MAAM,KAAK;EACzBC,UAAU,CAAC,MAAM,IAAI;EACrBC,MAAM,CAAC,MAAM,KAAK;EAClBC,aAAa,CAAC,MAAM,MAAM;EAC1BC,WAAW,CAAC,QAAQ,IAAI;EACxBC,OAAO,CAAC,QAAQ,KAAK;EACrBC,cAAc,CAAC,QAAQ,KAAK;EAC5BC,cAAc,CAAC,QAAQ,MAAM;EAC7BC,aAAa,CAAC,MAAM,MAAM;EAC1BC,eAAe,CAAC,OAAO,MAAM;EAC7BC,QAAQ,CAAC,OAAO,MAAM;EACtBC,cAAc,CAAC,QAAQ,MAAM;EAC7BC,QAAQ,CAAC,OAAO,KAAK;;AAmDvB,IAAMC,gBAAgBA,CAAC;EACrBpP;EACAqP;EACAhE;EACAiE;AAAW,MACY;AACvB,QAAM,CAACC,cAAcC,YAAY,IAC/BF,eAAW,OAAXA,cAAepB,0BAA0B,QAAQ;AAEnD,MAAIuB,sBAAsB,IAAIF,YAAY;AAC1C,MAAIG,sBAAsB,IAAIF,YAAY;AAC1C,MAAID,aAAaI,UAAS,EAAGC,WAAW,GAAG,GAAG;AAC5CH,0BAAsBF,aAAaM,UAAU,CAAC;EAChD;AACA,MAAIL,aAAaG,UAAS,EAAGC,WAAW,GAAG,GAAG;AAC5CF,0BAAsBF,aAAaK,UAAU,CAAC;EAChD;AAIA,QAAMC,iBAAiB,kCAAkCL,mBAAmB,KAAKC,mBAAmB;AAEpG;;IAEErS,aAAAA,QAAA5B,cAAA,OAAA;MAAKwL,OAAO;QAAC8I,WAAWD;MAAe;OAErCzS,aAAAA,QAAA5B,cAAA,OAAA;MAAK4P;MAAsBpE,OAAOoI;IAAO,GACtCrP,QACE,CACF;;AAET;AAOA,SAASgQ,kBAAkBzR,OAA0B;AACnD,QAAM,CAAC+O,QAAQ2C,SAAS,QACtBvS,uBAA0D,IAAI;AAChE,QAAM,CAACwS,kBAAkBC,mBAAmB,QAC1CzS,uBAAgC,IAAI;AAEtC,QAAMlD,MAAMiS,OAAM;AAClB,QAAM2D,gBAAgBzD,eAAe,QAAQ;AAE7C,QAAM;IACJ3M;IACAoD;IACAiI;IACAgF;IACAC;IACA/M;IACAgN;IACAC;IACAC;IACAC;IACAC;IACAzJ;IACA0J;IACArJ;EACD,IAAGhJ;AAEJ,QAAMsS,cAAcC,sBAASC,MAAM/Q,QAAQ;AAG3CL,8BAAU,MAAK;AACb,QAAI,CAACnF,OAAO,CAAC4V,cAAe;AAE5B,UAAMY,YAAY,IAAIZ,cAAca,sBAAqB;AACzDD,cAAUxW,MAAMA;AAEhByV,cAAUe,SAAS;AAGnB,QAAIE,iBAAsC;AAC1C,QAAIL,cAAc,GAAG;AACnBK,uBAAiB1V,SAASC,cAAc,KAAK;AAK7CyV,qBAAeC,iBAAiB;AAEhCH,gBAAUzD,UAAU2D;AACpBf,0BAAoBe,cAAc;IACpC;AAEA,WAAO,MAAK;AAAA,UAAAE;AACVJ,gBAAUxW,MAAM;AAChB,OAAA4W,kBAAAF,mBAAc,QAAdE,gBAAgBpQ,OAAM;AACtBiP,gBAAU,IAAI;AACdE,0BAAoB,IAAI;;KAEzB,CAAC3V,KAAK4V,eAAeS,WAAW,CAAC;AAMpClR,8BAAU,MAAK;AACb,QAAI,CAAC2N,UAAU,CAACA,OAAOC,WAAWsD,cAAc,EAAG;AAElDvD,WAAOC,QAAwBlC,YAAYA,aAAa;KACxD,CAACiC,QAAQjC,WAAWwF,WAAW,CAAC;AAGnC9D,iBAAeO,QAAQ,YAAYpG,QAAQ;AAC3C6F,iBAAeO,QAAQ,SAASsD,SAAAA,OAAAA,QAAS,EAAE;AAC3C7D,iBAAeO,QAAQ,UAAU/F,MAAM;AACvCwF,iBACEO,QACA,qBACAmD,iBAAkD;AAKpD9Q,8BAAU,MAAK;AACb,QAAI,CAAC2N,OAAQ;AAEb,QAAIqD,cAAc7Q,OAAWwN,QAAO+D,eAAeV;aAC1CpN,UAAUgN,eAAeC,UAAWlD,QAAO+D,eAAe;QAC9D/D,QAAO+D,eAAe;EAC7B,GAAG,CAAC/D,QAAQqD,WAAWpN,QAAQiN,WAAWD,WAAW,CAAC;AAItD5Q,8BAAU,MAAK;AACb,QAAI,CAAC2N,OAAQ;AAEb,UAAMgE,eACJZ,cAAc5Q,UACdyR,QAAQnO,OAAO,KACfmO,QAAQlB,YAAY,KACpBkB,QAAQjB,YAAY;AAItBhD,WAAOgE,eAAeA;AAGtB,QAAIA,gBAAgBhE,UAAM,QAANA,OAAQC,WAAWC,cAAcF,OAAOC,OAAO,GAAG;AACpED,aAAOC,QAAQtG,MAAMuK,gBAAgB;AAErC,UAAIlE,OAAOC,QAAQkE,mBAAmB;AACnCnE,eAAOC,QAAQkE,kBAAkCxK,MAAMuK,gBACtD;MACJ;IACF;EACF,GAAG,CAAClE,QAAQoD,WAAWtN,SAASiN,cAAcC,YAAY,CAAC;AAE3D1D,uBAAqBU,QAAQ,SAASlK,OAAO;AAC7CwJ,uBAAqBU,QAAQ,QAAQ/J,MAAM;AAC3CqJ,uBAAqBU,QAAQ,aAAaiD,WAAW;AACrD3D,uBAAqBU,QAAQ,WAAWkD,SAAS;AAEjDtD,sBAAoBI,UAAM,OAAA,SAANA,OAAQoE,SAAS,cAAcrB,YAAY;AAC/DnD,sBAAoBI,UAAM,OAAA,SAANA,OAAQoE,SAAS,cAAcpB,YAAY;AAE/D,SAAO,CAAChD,QAAQ4C,gBAAgB;AAClC;AAEO,IAAMyB,qBAAiBC,yBAC5B,CAACrT,OAA4BqG,QAA+B;AAC1D,QAAM;IAAC5E;IAAUiH;IAAOoE;IAAWiE;EAAW,IAAI/Q;AAClD,QAAM,CAAC+O,QAAQ4C,gBAAgB,IAAIF,kBAAkBzR,KAAK;AAE1D,QAAMsT,iCACJxS,sBAAQ,MAAOiO,SAAS;IAACA;EAAM,IAAI,MAAO,CAACA,MAAM,CAAC;AAEpDwE,wCACElN,KACA,MAAM0I,QACN,CAACA,MAAM,CAAC;AAGV,MAAI,CAAC4C,iBAAkB,QAAO;AAE9B,SACE7S,aAAAA,QAAA5B,cAACwS,sBAAsB7N,UAAQ;IAACxE,OAAOiW;EAA2B,OAC/DE,+BACC1U,aAAAA,QAAA5B,cAAC2T,eAAa;IACZE;IACAD,QAAQpI;IACRoE;EAAqB,GACpBrL,QACY,GACfkQ,gBAAgB,CAEY;AAEpC,CAAC;SAGa8B,uBAAoB;AAClC,QAAM,CAAC1E,QAAQ2C,SAAS,QACtBvS,uBAA0D,IAAI;AAEhE,QAAMuU,kBAAczS,0BAAa0S,OAA+B;AAC9DjC,cAAUiC,CAAC;KACV,CAAA,CAAE;AAEL,SAAO,CAACD,aAAa3E,MAAM;AAC7B;SC3TgB6E,kBACdT,SACArC,QACA+C,YAAgC;AAEhC,MAAI/C,UAAU,QAAQ,OAAOA,WAAW,UAAU;AAChD,UAAM,IAAI1S,MACR,sJAEc;EAElB;AAEA,QAAM0V,eAAeX,QAAQzK;AAG7B,MAAImL,cAAc,MAAM;AACtB,QAAI/C,UAAU,KAAM;AAEpB,eAAWiD,aAAajD,QAAQ;AAC9B,UAAI,CAACA,OAAOkD,eAAeD,SAAS,EAAG;AAEvCE,uBACEH,cACAC,WACAjD,OAAOiD,SAAgC,CAAC;IAE5C;AAEA;EACF;AAGA,aAAWA,aAAaF,YAAY;AAClC,QACEA,WAAWG,eAAeD,SAAS,MAClCjD,UAAU,QAAQ,CAACA,OAAOkD,eAAeD,SAAS,IACnD;AAEA,YAAMG,mBAAmBH,UAAUI,QAAQ,IAAI,MAAM;AACrD,UAAID,kBAAkB;AACpBJ,qBAAaM,YAAYL,WAAW,EAAE;MACxC,WAAWA,cAAc,SAAS;AAChCD,qBAAaO,WAAW;MAC1B,OAAO;AACLP,qBAAaC,SAAgB,IAAI;MACnC;IACF;EACF;AAGA,MAAIjD,UAAU,KAAM;AAEpB,aAAWiD,aAAajD,QAAQ;AAC9B,UAAMzT,QAAQyT,OAAOiD,SAAgC;AACrD,QACEjD,OAAOkD,eAAeD,SAAS,KAC/BF,WAAWE,SAAgC,MAAM1W,OACjD;AACA4W,uBAAiBH,cAAcC,WAAW1W,KAAK;IACjD;EACF;AACF;AAEA,SAAS4W,iBACPH,cACAC,WACA1W,OAAc;AAEd,QAAM6W,mBAAmBH,UAAUI,QAAQ,IAAI,MAAM;AAGrD,MAAI9W,SAAS,QAAQ,OAAOA,UAAU,aAAaA,UAAU,IAAI;AAC/D,QAAI6W,kBAAkB;AACpBJ,mBAAaM,YAAYL,WAAW,EAAE;IACxC,WAAWA,cAAc,SAAS;AAChCD,mBAAaO,WAAW;IAC1B,OAAO;AACLP,mBAAaC,SAAgB,IAAI;IACnC;EACF,WAGSG,kBAAkB;AACzBJ,iBAAaM,YAAYL,WAAW1W,KAAe;EACrD,WAIE,OAAOA,UAAU,YACjBA,UAAU,KACV,CAACiX,iBAAiBP,SAAS,GAC3B;AACAD,iBAAaC,SAAgB,IAAI1W,QAAQ;EAC3C,OAGK;AACH,QAAI0W,cAAc,SAAS;AACzBD,mBAAaO,WAAWhX;IAC1B,OAAO;AACLyW,mBAAaC,SAAgB,KAAK,KAAK1W,OAAOkX,KAAI;IACpD;EACF;AACF;AAGA,IAAMC,kBAAkB,oBAAI9N,IAAI;EAC9B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAAa,CACd;AACD,SAAS4N,iBAAiBpY,MAAY;AACpC,SAAOsY,gBAAgB1N,IAAI5K,IAAI;AACjC;;ACjIauY,IAAAA,aAETzU,WAAQ;AACV,QAAM;;IAEJyB;IACAiT;IAEAhM;IACAoE;IACA6H;;IAGAC;IACAC;;IAGAC;IACAC;;EAGoB,IAClB/U,OADCgV,oBAAiBpV,8BAClBI,OAAKM,WAAA;AAGT,QAAM2U,cAAc7G,eAAe,MAAM;AACzC,QAAM,CAAC8G,YAAYC,aAAa,QAAIhW,uBAClC,IAAI;AAGN,QAAMiW,0BAAsB9O,qBAA2B,IAAI;AAC3D,QAAM+O,yBAAqB/O,qBAA2B,IAAI;AAE1DlF;IACE,MAAK;AACH,UAAI,CAAC6T,YAAa;AAElBG,0BAAoB7O,UAAUtJ,SAASC,cAAc,KAAK;AAC1DmY,yBAAmB9O,UAAUtJ,SAASC,cAAc,KAAK;AAEzD,YAAMoY,OAAsCN;AAC5C,UAAIL,aAAa;AACfW,aAAKX,cAAc,IAAItZ,OAAOC,KAAKia,KAAKZ,YAAY,CAAC,GAAGA,YAAY,CAAC,CAAC;MACxE;AAEA,UAAID,eAAe;AAGjBY,aAAKZ,gBACH,OAAOA,kBAAkB,WACrBA,gBACAW,mBAAmB9O;MAC3B;AAGA,YAAM2O,cAAa,IAAI7Z,OAAOC,KAAKmZ,WAAWO,iBAAiB;AAC/DE,MAAAA,YAAWM,WAAWJ,oBAAoB7O,OAAO;AAEjD4O,oBAAcD,WAAU;AAGxB,aAAO,MAAK;AAAA,YAAAO,uBAAAC;AACVR,QAAAA,YAAWM,WAAW,IAAI;AAE1B,SAAAC,wBAAAL,oBAAoB7O,YAAO,QAA3BkP,sBAA6BhT,OAAM;AACnC,SAAAiT,wBAAAL,mBAAmB9O,YAAO,QAA1BmP,sBAA4BjT,OAAM;AAElC2S,4BAAoB7O,UAAU;AAC9B8O,2BAAmB9O,UAAU;AAE7B4O,sBAAc,IAAI;;;;;;;;;;IAUtB,CAACF,WAAW;EAAC;AAKf,QAAMU,mBAAerP,qBAA6B,IAAI;AACtDlF,8BAAU,MAAK;AACb,QAAI,CAAC8T,cAAc,CAACE,oBAAoB7O,QAAS;AAEjDqN,sBACEwB,oBAAoB7O,SACpBmC,SAAS,MACTiN,aAAapP,OAAO;AAGtBoP,iBAAapP,UAAUmC,SAAS;AAEhC,QAAIoE,cAAcsI,oBAAoB7O,QAAQuG,UAC5CsI,qBAAoB7O,QAAQuG,YAAYA,aAAa;KACtD,CAACoI,YAAYpI,WAAWpE,KAAK,CAAC;AAGjCxC;IACE,MAAK;AACH,UAAI,CAACgP,WAAY;AAEjB,YAAMI,OAAsCN;AAC5C,UAAI,CAACL,aAAa;AAChBW,aAAKX,cAAc;MACrB,OAAO;AACLW,aAAKX,cAAc,IAAItZ,OAAOC,KAAKia,KAAKZ,YAAY,CAAC,GAAGA,YAAY,CAAC,CAAC;MACxE;AAEA,UAAI,CAACD,eAAe;AAClBY,aAAKZ,gBAAgB;MACvB,OAAO;AACLY,aAAKZ,gBACH,OAAOA,kBAAkB,WACrBA,gBACAW,mBAAmB9O;MAC3B;AAEA2O,iBAAWnO,WAAWiO,iBAAiB;;;;;IAMzC,CAACA,mBAAmBL,aAAaD,aAAa;EAAC;AAIjDrG,uBAAqB6G,YAAY,SAASJ,OAAO;AACjDzG,uBAAqB6G,YAAY,cAAcH,YAAY;AAG3D,QAAM9Y,MAAMiS,OAAM;AAClBhI,uBAAqB,MAAK;AAExB,QAAI,CAACjK,OAAO,CAACiZ,cAAcN,WAAW,KAAM;AAE5C,UAAMgB,qBAAqB,CAAC,CAAChB;AAC7B,UAAMiB,cAAiD;MAAC5Z;;AACxD,QAAI2Y,QAAQ;AACViB,kBAAYjB,SAASA;AAGrB,UAAI9F,iBAAiB8F,MAAM,KAAKA,OAAO5F,mBAAmB8G,SAAS;AACjE,cAAMC,UAAUnB,OAAO5F;AACvB,cAAMgH,aAAaD,WAAO,OAAA,SAAPA,QAASE,sBAAqB;AAMjD,YAAID,cAAcD,WAAO,QAAPA,QAASnD,gBAAgB;AAAA,cAAAsD;AAGzC,gBAAMC,oBAAgBD,wBAAGtB,OAAO5F,QAAQkE,sBAAiB,OAAA,SAAhCgD,sBACrBhD;AAEJ,gBAAMkD,aAAaD,oBAAgB,OAAA,SAAhBA,iBAAkBF,sBAAqB;AAG1D,gBAAMI,gBACJD,WAAWrM,IACXiM,WAAWjM,KACVqM,WAAWxI,QAAQoI,WAAWpI,SAAS;AAC1C,gBAAM0I,gBAAgBF,WAAWG,IAAIP,WAAWO;AAEhD,gBAAMjB,OAAsCN;AAE5CM,eAAKX,cAAc,IAAItZ,OAAOC,KAAKia,KACjCZ,cAAcA,YAAY,CAAC,IAAI0B,gBAAgBA,eAC/C1B,cAAcA,YAAY,CAAC,IAAI2B,gBAAgBA,aAAa;AAG9DpB,qBAAWnO,WAAWuO,IAAI;QAC5B;MACF;IACF;AAEA,QAAIT,gBAAgBtT,QAAW;AAC7BsU,kBAAYhB,cAAcA;IAC5B;AAEAK,eAAWsB,KAAKX,WAAW;AAE3B,WAAO,MAAK;AAKV,UAAID,mBAAoBV,YAAWtX,IAAI,UAAU,IAAI;AAErDsX,iBAAWuB,MAAK;;EAEpB,GAAG,CAACvB,YAAYN,QAAQ3Y,KAAK4Y,aAAaG,mBAAmBL,WAAW,CAAC;AAEzE,SACE7V,aAAAA,QAAA5B,cAAA4B,aAAAA,QAAA4X,UACGtB,MAAAA,oBAAoB7O,eACnBiN,+BAAa/R,UAAU2T,oBAAoB7O,OAAO,GAEnD8O,mBAAmB9O,YAAY,YAC9BiN,+BAAakB,eAAeW,mBAAmB9O,OAAO,CAC1D;AAEJ;ACrOM,SAAUoQ,eAAeC,UAA4B;AACzD,SAAO,OAAOA,aAAa,WACvBA,WACA,GAAGA,SAAS7S,GAAG,IAAI6S,SAAS5S,GAAG;AACrC;AAGM,SAAU6S,YAAYC,QAAc;AACxC,SAAOA,OAAOC,MAAM,CAAC;AACvB;ACEgB,SAAAC,qBAAqBC,UAA8B,CAAA,GAAE;AACnE,QAAMC,eAA8B,CAAA;AAGpC,QAAMC,iBAAiBF,WAAO,OAAA,SAAPA,QAASG,OAC9B,CAACtG,QAAQ/B,WAAU;AACjB,UAAM;MAACzF,QAAQ;MAAO+N;MAAOC;MAAMC;MAAOC;MAAM5C;IAAO,IAAG7F;AAG1D,UAAM0I,gBAAgBD,OAAO,CAACA,MAAM5C,QAAQ2C,KAAK,IAAI,CAACjO,OAAO+N,OAAOC,IAAI;AACxE,UAAMlb,MAAMqb,cAAcC,OAAO1E,OAAO,EAAEvW,KAAK,GAAG;AAElDqU,WAAO1U,GAAG,IAAI0U,OAAO1U,GAAG,KAAK,CAAA;AAC7B0U,WAAO1U,GAAG,EAAEjB,KAAK4T,MAAM;AACvB,WAAO+B;KAET,CAAA,CAAwC;AAG1CxT,SAAOqa,OAAOR,kBAAc,OAAdA,iBAAkB,CAAA,CAAE,EAAES,QAAQX,CAAAA,aAAU;AACpD,QAAIY,cAAsB;AAE1B,UAAM;MAACL;IAAK,IAAGP,SAAQ,CAAC;AAGxB3Z,WAAOC,QAAQ0Z,SAAQ,CAAC,CAAC,EAAEW,QAAQ,CAAC,CAACxb,KAAKiB,KAAK,MAAK;AAElD,YAAMya,eAAeN,OACjB,CAAC,QAAQ,UAAU,OAAO,IAC1B,CAAC,SAAS,SAAS,MAAM;AAE7B,UAAIM,aAAa9U,SAAS5G,GAAG,GAAG;AAC9Byb,uBAAe,IAAIzb,GAAG,IAAIiB,KAAK;MACjC;IACF,CAAC;AAID,eAAW0R,UAAUkI,UAAS;AAC5B,YAAML,WACJ,OAAO7H,OAAO6H,aAAa,WACvB7H,OAAO6H,WACP,GAAG7H,OAAO6H,SAAS7S,GAAG,IAAIgL,OAAO6H,SAAS5S,GAAG;AAEnD6T,qBAAe,IAAIjB,QAAQ;IAC7B;AAEAM,iBAAa/b,KAAK0c,WAAW;EAC/B,CAAC;AAED,SAAOX,aAAajb,IAAI4a,WAAW;AACrC;AChDgB,SAAAkB,mBAAmBC,QAA+B,CAAA,GAAE;AAClE,QAAMC,aAA4B,CAAA;AAIlC,QAAMC,eAAeF,SAAK,OAAA,SAALA,MAAOZ,OAC1B,CAACtG,QAAQqH,SAAQ;AACf,UAAM;MAAC7O,QAAQ;MAAW8O;MAAQC;MAAWC;IAAS,IAAGH;AAGzD,UAAM/b,MAAM,CAACkN,OAAO8O,QAAQC,WAAWC,QAAQ,EAC5CZ,OAAO1E,OAAO,EACdvW,KAAK,GAAG;AAEXqU,WAAO1U,GAAG,IAAI0U,OAAO1U,GAAG,KAAK,CAAA;AAC7B0U,WAAO1U,GAAG,EAAEjB,KAAKgd,IAAI;AACrB,WAAOrH;KAET,CAAA,CAA2C;AAI7CxT,SAAOqa,OAAOO,gBAAY,OAAZA,eAAgB,CAAA,CAAE,EAAEN,QAAQI,CAAAA,WAAQ;AAChD,QAAIO,YAAY;AAIhBjb,WAAOC,QAAQya,OAAM,CAAC,CAAC,EAAEJ,QAAQ,CAAC,CAACxb,KAAKiB,KAAK,MAAK;AAChD,UAAI,CAAC,SAAS,UAAU,aAAa,UAAU,EAAE2F,SAAS5G,GAAG,GAAG;AAC9Dmc,qBAAa,IAAInc,GAAG,IAAIiB,KAAK;MAC/B;IACF,CAAC;AAGD,eAAW8a,QAAQH,QAAO;AACxB,UAAI,OAAOG,KAAKK,gBAAgB,UAAU;AACxCD,qBAAa,IAAIE,mBAAmBN,KAAKK,WAAW,CAAC;MACvD,OAAO;AACL,mBAAW5B,YAAYuB,KAAKK,aAAa;AACvCD,uBAAa,IAAI5B,eAAeC,QAAQ,CAAC;QAC3C;MACF;IACF;AAEAqB,eAAW9c,KAAKod,SAAS;EAC3B,CAAC;AAED,SAAON,WAAWhc,IAAI4a,WAAW;AACnC;ACrDM,SAAU6B,sBACd5H,QAAuC;AAEvC,SAAOA,OACJ7U,IAAK0c,kBAA0C;AAC9C,UAAM;MAACC;MAAaC;MAAaC,UAAU,CAAA;IAAE,IAAIH;AAEjD,QAAII,cAAc;AAElB,QAAIH,aAAa;AACfG,qBAAe,YAAYH,WAAW;IACxC;AAEA,QAAIC,aAAa;AACfE,qBAAe,YAAYF,WAAW;IACxC;AAEA,eAAWG,UAAUF,SAAS;AAC5Bxb,aAAOC,QAAQyb,MAAM,EAAEpB,QAAQ,CAAC,CAAC1b,MAAMmB,KAAK,MAAK;AAC/C0b,uBAAe,IAAI7c,IAAI,IAAI2B,OAAOR,KAAK,EAAEI,QAAQ,KAAK,IAAI,CAAC;MAC7D,CAAC;IACH;AAEA,WAAOsb;EACT,CAAC,EACA9c,IAAI4a,WAAW;AACpB;AC5CA,IAAMoC,mBAAmB;SAqETC,oBAAoB;EAClC/Y;EACAyN;EACA/B;EACA3I;EACAE;EACAmU;EACA4B;EACAC;EACA/c;EACAC;EACAgP;EACA2L,UAAU,CAAA;EACVe,QAAQ,CAAA;EACRqB,UAAU,CAAA;EACV3Q,QAAQ,CAAA;AACa,GAAA;AACrB,MAAI,CAACvI,QAAQ;AACXvE,YAAQC,KAAK,qBAAqB;EACpC;AACA,MAAI,CAAC+R,SAAS,CAAC/B,QAAQ;AACrBjQ,YAAQC,KAAK,+BAA+B;EAC9C;AAEA,QAAMlB,SAAM6E,SAAA;IACVpD,KAAK+D;IACLmX,MAAM,GAAG1J,KAAK,IAAI/B,MAAM;EAAE,GACtB3I,UAAU;IAACA,QAAQyT,eAAezT,MAAM;KACxCE,QAAQ;IAACA;KACTmU,SAAS;IAACA;KACV4B,UAAU;IAACA;KACXC,WAAW;IAACE,SAASF;KACrB/c,YAAY;IAACA;KACbC,UAAU;IAACA;KACXgP,SAAS;IAACiO,QAAQjO;GAAM;AAG9B,QAAMkO,MAAM,IAAIC,IAAIR,gBAAgB;AAGpC3b,SAAOC,QAAQ5C,MAAM,EAAEid,QAAQ,CAAC,CAACxb,KAAKiB,KAAK,MAAK;AAC9Cmc,QAAIE,aAAalb,OAAOpC,KAAKyB,OAAOR,KAAK,CAAC;EAC5C,CAAC;AAGD,aAAWwa,eAAeb,qBAAqBC,OAAO,GAAG;AACvDuC,QAAIE,aAAalb,OAAO,WAAWqZ,WAAW;EAChD;AAGA,aAAWU,aAAaR,mBAAmBC,KAAK,GAAG;AACjDwB,QAAIE,aAAalb,OAAO,QAAQ+Z,SAAS;EAC3C;AAGA,MAAIc,QAAQhY,QAAQ;AAClBmY,QAAIE,aAAalb,OACf,WACA6a,QAAQpd,IAAI2a,cAAYD,eAAeC,QAAQ,CAAC,EAAEna,KAAK,GAAG,CAAC;EAE/D;AAGA,aAAWsc,eAAeL,sBAAsBhQ,KAAK,GAAG;AACtD8Q,QAAIE,aAAalb,OAAO,SAASua,WAAW;EAC9C;AAEA,SAAOS,IAAIxb,SAAQ;AACrB;ACnIa2b,IAAAA,YAAa3Z,WAAyB;AACjD,QAAM;IAACwZ;IAAK1M;EAAU,IAAG9M;AAEzB,MAAI,CAACwZ,IAAK,OAAM,IAAIpb,MAAM,iBAAiB;AAE3C,SAAOU,aAAAA,QAAA5B,cAAA,OAAA;IAAK4P;IAAsB/O,KAAKyb;IAAK5L,OAAM;EAAM,CAAA;AAC1D;ACLO,IAAMgM,kBAAkB;EAC7BhK,UAAU;EACVC,YAAY;EACZC,KAAK;EACLC,WAAW;EACXC,aAAa;EACbC,UAAU;EACVC,MAAM;EACNC,aAAa;EACbC,WAAW;EACXC,OAAO;EACPC,cAAc;EACdC,cAAc;EACdC,aAAa;EACbC,eAAe;EACfC,QAAQ;EACRC,cAAc;EACdC,QAAQ;EACRiJ,0BAA0B;EAC1BC,2BAA2B;EAC3BC,wBAAwB;EACxBC,2BAA2B;EAC3BC,0BAA0B;EAC1BC,wBAAwB;EACxBC,wBAAwB;EACxBC,yBAAyB;EACzBC,sBAAsB;EACtBC,wBAAwB;EACxBC,yBAAyB;EACzBC,sBAAsB;;AAKjB,IAAMC,aAAiDA,CAAC;EAC7DhZ;EACAkH;AACD,MAAI;AACH,QAAM+R,uBAAmB5Z,sBAAQ,MAAM7D,SAASC,cAAc,KAAK,GAAG,CAAA,CAAE;AACxE,QAAMjB,MAAMiS,OAAM;AAElB9M,8BAAU,MAAK;AACb,QAAI,CAACnF,IAAK;AAEV,UAAM0e,WAAW1e,IAAI0e,SAAShS,QAAQ;AAEtCgS,aAASxf,KAAKuf,gBAAgB;AAE9B,WAAO,MAAK;AACV,YAAME,gBAAgBD,SAASE,SAAQ;AAEvC,UAAI,CAACD,cAAe;AAEpB,YAAME,QAAQF,cAAczG,QAAQuG,gBAAgB;AACpDC,eAASI,SAASD,KAAK;;KAExB,CAACJ,kBAAkBze,KAAK0M,QAAQ,CAAC;AAEpC,aAAO6K,+BAAa/R,UAAUiZ,gBAAgB;AAChD;;AC9CA,SAASM,UAAUhb,OAAkB;AACnC,QAAM,CAAC+O,QAAQ2C,SAAS,QAAIvS,uBAAoC,IAAI;AACpE,QAAMlD,MAAMiS,OAAM;AAElB,QAAM;IACJrJ;IACAG;IACAgN;IACAC;IACAgJ;IACAC;EAED,IAAGlb,OADCmb,gBAAavb,8BACdI,OAAKM,SAAA;AAET,QAAM;IAACqI;IAAUyJ;EAAU,IAAG+I;AAG9B/Z,8BAAU,MAAK;AACb,QAAI,CAACnF,KAAK;AACR,UAAIA,QAAQsF,OACV3F,SAAQc,MAAM,4CAA4C;AAE5D;IACF;AAEA,UAAM+V,YAAY,IAAIpX,OAAOC,KAAK8f,OAAOD,aAAa;AACtD1I,cAAUjI,OAAOvO,GAAG;AACpByV,cAAUe,SAAS;AAEnB,WAAO,MAAK;AACVA,gBAAUjI,OAAO,IAAI;AACrBkH,gBAAU,IAAI;;EAMlB,GAAG,CAACzV,GAAG,CAAC;AAGRmF,8BAAU,MAAK;AACb,QAAI,CAAC2N,OAAQ;AAEb,UAAM4E,IAAI5E;AAGV,UAAMsM,MAAMhgB,OAAOC,KAAK+G;AAExB,QAAIwC,QAASwW,KAAI/Y,YAAYqR,GAAG,SAAS9O,OAAO;AAChD,QAAIG,OAAQqW,KAAI/Y,YAAYqR,GAAG,QAAQ3O,MAAM;AAC7C,QAAIgN,YAAaqJ,KAAI/Y,YAAYqR,GAAG,aAAa3B,WAAW;AAC5D,QAAIC,UAAWoJ,KAAI/Y,YAAYqR,GAAG,WAAW1B,SAAS;AACtD,QAAIgJ,YAAaI,KAAI/Y,YAAYqR,GAAG,aAAasH,WAAW;AAC5D,QAAIC,WAAYG,KAAI/Y,YAAYqR,GAAG,YAAYuH,UAAU;AAEzDnM,WAAOuM,aAAatI,QAAQZ,SAAS,CAAC;AAEtC,WAAO,MAAK;AACViJ,UAAIjP,uBAAuBuH,CAAC;;EAEhC,GAAG,CACD5E,QACAqD,WACAvN,SACAG,QACAgN,aACAC,WACAgJ,aACAC,UAAU,CACX;AAKD9Z,8BAAU,MAAK;AACb,QAAI,CAAC2N,OAAQ;AACb,QAAIoM,cAAepM,QAAOhI,WAAWoU,aAAa;EACpD,GAAG,CAACpM,QAAQoM,aAAa,CAAC;AAG1B/Z,8BAAU,MAAK;AAEb,QAAIgR,aAAa,CAACzJ,YAAY,CAACoG,OAAQ;AAEvCA,WAAOwM,YAAY5S,QAAQ;KAC1B,CAACyJ,WAAWzJ,UAAUoG,MAAM,CAAC;AAEhC,SAAOA;AACT;AAKO,IAAMqM,aAAS/H,yBAAW,CAACrT,OAAoBqG,QAAkB;AACtE,QAAM0I,SAASiM,UAAUhb,KAAK;AAE9BuT,wCAAoBlN,KAAK,MAAM0I,QAA8B,CAACA,MAAM,CAAC;AAErE,SAAOjQ,aAAAA,QAAA5B,cAAA4B,aAAAA,QAAA4X,UAAA,IAAA;AACT,CAAC;SAEe8E,eAAY;AAC1B,QAAM,CAACzM,QAAQ2C,SAAS,QAAIvS,uBAAoC,IAAI;AAEpE,QAAMuU,kBAAczS,0BAAa0S,OAAgC;AAC/DjC,cAAUiC,CAAC;KACV,CAAA,CAAE;AAEL,SAAO,CAACD,aAAa3E,MAAM;AAC7B;ACpHa0M,IAAAA,MAAmCzb,WAAQ;AAAA,MAAAiH;AACtD,QAAMyU,kBAAczU,kBAAGC,yBAAWwI,qBAAqB,MAAC,OAAA,SAAjCzI,YAAmC8H;AAC1D,QAAM4M,qBAAiB7a,sBAAQ,MAAM7D,SAASC,cAAc,KAAK,GAAG,CAAA,CAAE;AAGtEkE,8BAAU,MAAK;AAAA,QAAAwa;AACb,QAAI,CAACF,gBAAgB;AACnB,UAAIA,mBAAmBna,QAAW;AAChC3F,gBAAQc,MACN,+DAA+D;MAEnE;AAEA;IACF;AAEA,QAAIsD,MAAM6b,SAAS7b,MAAMyB,UAAU;AACjCsM,mBACE,iHAAiH;IAErH;AAEA,QAAIwE,sBAASC,MAAMxS,MAAMyB,QAAQ,IAAI,GAAG;AACtCsM,mBACE,oFAAoF;IAExF;AAEA,UAAM+N,iBAActc,SAAA,CAAA,GACfQ,KAAK;AAGV,UAAM+b,aAAa,IAAI1gB,OAAOC,KAAKyT,OAAOiN,WAAWF,cAAc;AAInE,QAAI9b,MAAMyB,UAAU;AAClBsa,iBAAWF,QAAQF;IACrB;AAMA,UAAMM,iBAAaL,wBAAGF,eAAe1M,YAAO,SAAA4M,wBAAtBA,sBAAwBM,eAAxBN,OAAAA,SAAAA,sBAAoCM;AAE1D,WAAOD,iBAAa,QAAbA,cAAeC,YAAY;AAChCD,oBAAcE,YAAYF,cAAcC,UAAU;IACpD;AAEA,QAAID,eAAe;AACjBA,oBAAcvQ,YAAYqQ,WAAW5I,OAAO;IAC9C;KACC,CAACuI,gBAAgBC,gBAAgB3b,KAAK,CAAC;AAE1C,aAAOwT,+BAAaxT,MAAMyB,UAAUka,cAAc;AACpD;AC5EA,IAAMS,YAAYA,CAACrS,GAAWsS,IAAYC,IAAYC,IAAYC,OAChED,MAAOxS,IAAIsS,OAAOG,KAAKD,OAAQD,KAAKD;AAEtC,IAAMI,gBAAiBrZ,UAAgB;AACrC,MAAIA,QAAQ,IAAI;AACd,WAAO;EACT;AACA,MAAIA,QAAQ,MAAM;AAChB,WAAO;EACT;AAGA,MAAIA,QAAQ,IAAI;AACd,WAAOgZ,UAAUhZ,MAAM,IAAI,IAAI,IAAI,EAAE;EACvC;AAGA,SAAOgZ,UAAUhZ,MAAM,IAAI,MAAM,IAAI,IAAI;AAC3C;AAMO,IAAMsZ,iBAAiBA,CAAC;EAACrV;AAAe,MAAI;AACjD,QAAMM,QAAQN,UAAUM;AACxB,QAAMgV,SAAStV,UAAUjE,OAAO;AAChC,QAAMwZ,UAAUH,cAAcE,MAAM;AAEpC,SAAAnd,SAAA,CAAA,GAAW6H,WAAS;IAAEwV,MAAM;IAAIlV,OAAOmV,KAAKC,IAAIH,SAASjV,KAAK;EAAC,CAAA;AACjE;", "names": ["APILoadingStatus", "NOT_LOADED", "LOADING", "LOADED", "FAILED", "AUTH_FAILURE", "MAPS_API_BASE_URL", "GoogleMapsApiLoader", "load", "params", "onLoadingStatusChange", "_window$google", "libraries", "split", "serializedParams", "serializeParams", "listeners", "push", "window", "google", "maps", "importLibrary", "serializedApiParams", "loadingStatus", "notifyLoadingStatusListeners", "initImportLibrary", "console", "warn", "librariesToLoad", "Promise", "all", "map", "name", "v", "key", "language", "region", "authReferrerPolicy", "solutionChannel", "join", "error", "apiPromise", "loadApi", "resolve", "reject", "_document$querySelect", "scriptElement", "document", "createElement", "urlParams", "URLSearchParams", "value", "Object", "entries", "urlParamName", "replace", "t", "toLowerCase", "set", "String", "async", "src", "toString", "nonce", "querySelector", "onerror", "Error", "__googleMapsCallback__", "gm_authFailure", "head", "append", "libraryName", "then", "fn", "DEFAULT_SOLUTION_CHANNEL", "APIProviderContext", "React", "createContext", "useMapInstances", "mapInstances", "setMapInstances", "useState", "addMapInstance", "mapInstance", "id", "instances", "_extends", "removeMapInstance", "_ref", "remaining", "_objectWithoutPropertiesLoose", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "clearMapInstances", "useGoogleMapsApiLoader", "props", "onLoad", "onError", "<PERSON><PERSON><PERSON><PERSON>", "version", "otherApiParams", "_excluded", "status", "setStatus", "loadedLibraries", "addLoadedLibrary", "useReducer", "action", "librariesString", "useMemo", "JSON", "stringify", "useCallback", "_google", "res", "useEffect", "length", "channel", "undefined", "APIProvider", "children", "loaderProps", "_excluded2", "contextValue", "Provider", "useMapEvents", "propName", "eventPropNames", "handler", "eventType", "propNameToEventType", "listener", "event", "addListener", "ev", "createMapEvent", "remove", "type", "srcEvent", "detail", "stoppable", "stop", "cameraEventTypes", "includes", "camEvent", "center", "getCenter", "zoom", "getZoom", "heading", "getHeading", "tilt", "getTilt", "bounds", "getBounds", "Number", "isFinite", "toJSON", "lat", "lng", "north", "east", "south", "west", "mouseEventTypes", "_srcEvent$latLng", "mouseEvent", "domEvent", "latLng", "placeId", "onBoundsChanged", "onCenterChanged", "onClick", "onContextmenu", "onDblclick", "onDrag", "onDragend", "onDragstart", "onHeadingChanged", "onIdle", "onIsFractionalZoomEnabledChanged", "onMapCapabilitiesChanged", "onMapTypeIdChanged", "onMousemove", "onMouseout", "onMouseover", "onProjectionChanged", "onRenderingTypeChanged", "onTilesLoaded", "onTiltChanged", "onZoomChanged", "onCameraChanged", "keys", "useDeepCompareEffect", "effect", "deps", "ref", "useRef", "current", "isDeepEqual", "mapOptionKeys", "Set", "useMapOptions", "mapProps", "mapOptions", "has", "setOptions", "useApiLoadingStatus", "_useContext", "useContext", "useDeckGLCameraUpdate", "viewport", "viewState", "isDeckGlControlled", "useLayoutEffect", "latitude", "longitude", "bearing", "pitch", "moveCamera", "isLatLngLiteral", "obj", "latLngEquals", "a", "b", "A", "toLatLngLiteral", "B", "useMapCameraParams", "cameraStateRef", "nextCamera", "needsUpdate", "AuthFailureMessage", "style", "position", "top", "left", "bottom", "right", "zIndex", "display", "flexFlow", "textAlign", "justifyContent", "fontSize", "color", "background", "padding", "useCallbackRef", "el", "setEl", "useApiIsLoaded", "useForceUpdate", "forceUpdate", "x", "handleBoundsChange", "assign", "useTrackedCameraStateRef", "CachedMapStack", "pop", "useMapInstance", "context", "apiIsLoaded", "setMap", "container", "containerRef", "defaultBounds", "defaultCenter", "defaultZoom", "defaultHeading", "defaultTilt", "reuseMaps", "renderingType", "colorScheme", "hasZoom", "hasCenter", "savedMapStateRef", "mapId", "cache<PERSON>ey", "mapDiv", "getDiv", "append<PERSON><PERSON><PERSON>", "setTimeout", "setCenter", "height", "Map", "defBounds", "fitBounds", "savedMapId", "cameraState", "savedCameraState", "clearInstanceListeners", "GoogleMapsContext", "ColorScheme", "DARK", "LIGHT", "FOLLOW_SYSTEM", "RenderingType", "VECTOR", "RASTER", "UNINITIALIZED", "className", "mapRef", "isControlledExternally", "controlled", "disableDefaultUI", "<PERSON><PERSON><PERSON><PERSON>", "keyboardShortcuts", "cameraOptions", "_lat", "_lng", "_props$zoom", "_props$heading", "_props$tilt", "combinedStyle", "width", "deckGLViewProps", "shownMessages", "logErrorOnce", "args", "add", "useMap", "ctx", "useMapsLibrary", "useMapsEventListener", "target", "callback", "usePropBinding", "object", "prop", "useDomEventListener", "addEventListener", "removeEventListener", "isAdvancedMarker", "marker", "content", "isElementNode", "node", "nodeType", "Node", "ELEMENT_NODE", "CollisionBehavior", "REQUIRED", "REQUIRED_AND_HIDES_OPTIONAL", "OPTIONAL_AND_HIDES_LOWER_PRIORITY", "AdvancedMarkerContext", "AdvancedMarkerAnchorPoint", "TOP_LEFT", "TOP_CENTER", "TOP", "TOP_RIGHT", "LEFT_CENTER", "LEFT_TOP", "LEFT", "LEFT_BOTTOM", "RIGHT_TOP", "RIGHT", "RIGHT_CENTER", "RIGHT_BOTTOM", "BOTTOM_LEFT", "BOTTOM_CENTER", "BOTTOM", "BOTTOM_RIGHT", "CENTER", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styles", "anchorPoint", "xTranslation", "yTranslation", "xTranslationFlipped", "yTranslationFlipped", "trimStart", "startsWith", "substring", "transformStyle", "transform", "useAdvancedMarker", "<PERSON><PERSON><PERSON><PERSON>", "contentContainer", "set<PERSON><PERSON>ntC<PERSON>r", "markerLibrary", "onMouseEnter", "onMouseLeave", "onDragStart", "onDragEnd", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clickable", "draggable", "title", "numC<PERSON><PERSON>n", "Children", "count", "newMarker", "AdvancedMarkerElement", "contentElement", "isCustomMarker", "_contentElement", "gmpDraggable", "gmpClickable", "Boolean", "pointerEvents", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "element", "AdvancedMarker", "forwardRef", "advancedMarkerContextValue", "useImperativeHandle", "createPortal", "useAdvancedMarkerRef", "refC<PERSON><PERSON>", "m", "setValueForStyles", "prevStyles", "elementStyle", "styleName", "hasOwnProperty", "setValueForStyle", "isCustomProperty", "indexOf", "setProperty", "cssFloat", "isUnitlessNumber", "trim", "unitlessNumbers", "InfoWindow", "headerContent", "pixelOffset", "anchor", "shouldFocus", "onClose", "onCloseClick", "infoWindowOptions", "mapsLibrary", "infoWindow", "setInfoWindow", "contentContainerRef", "headerContainerRef", "opts", "Size", "<PERSON><PERSON><PERSON><PERSON>", "_contentContainerRef$", "_headerContainerRef$c", "prevStyleRef", "isOpenedWithAnchor", "openOptions", "Element", "wrapper", "wrapperBcr", "getBoundingClientRect", "_anchor$content$first", "anchorDomContent", "contentBcr", "anchorOffsetX", "anchorOffsetY", "y", "open", "close", "Fragment", "formatLocation", "location", "formatParam", "string", "slice", "assembleMarkerParams", "markers", "markerParams", "markersByStyle", "reduce", "label", "size", "scale", "icon", "relevantProps", "filter", "values", "for<PERSON>ach", "markerParam", "relevant<PERSON><PERSON><PERSON>", "assemblePathParams", "paths", "pathParams", "pathsByStyle", "path", "weight", "fillcolor", "geodesic", "pathParam", "coordinates", "decodeURIComponent", "assembleMapTypeStyles", "mapTypeStyle", "featureType", "elementType", "stylers", "styleString", "styler", "STATIC_MAPS_BASE", "createStaticMapsUrl", "format", "mapType", "visible", "maptype", "map_id", "url", "URL", "searchParams", "StaticMap", "ControlPosition", "BLOCK_START_INLINE_START", "BLOCK_START_INLINE_CENTER", "BLOCK_START_INLINE_END", "INLINE_START_BLOCK_CENTER", "INLINE_START_BLOCK_START", "INLINE_START_BLOCK_END", "INLINE_END_BLOCK_START", "INLINE_END_BLOCK_CENTER", "INLINE_END_BLOCK_END", "BLOCK_END_INLINE_START", "BLOCK_END_INLINE_CENTER", "BLOCK_END_INLINE_END", "MapControl", "controlContainer", "controls", "controlsArray", "getArray", "index", "removeAt", "useMarker", "onMouseOver", "onMouseOut", "markerOptions", "<PERSON><PERSON>", "gme", "setDraggable", "setPosition", "useMarkerRef", "<PERSON>n", "advancedMarker", "glyph<PERSON><PERSON><PERSON>", "_advancedMarker$conte", "glyph", "pinViewOptions", "pinElement", "Pin<PERSON>lement", "markerContent", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "mapLinear", "a1", "a2", "b1", "b2", "getMapMaxTilt", "limitTiltRange", "gmZoom", "maxTilt", "fovy", "Math", "min"]}